import { useState, useEffect } from 'react';

interface Slide {
  title: string;
  content: string;
}

const [slides, setSlides] = useState<Slide[]>([]);
const [source, setSource] = useState<string>('');

useEffect(() => {
  fetch('/processed/index.json')
    .then((r) => r.json())
    .then((files: string[]) => {
      if (files.length) {
        setSource(files[0]);
        return fetch(`/processed/${files[0]}`).then((r) => r.json());
      }
      return [];
    })
    .then((data) => setSlides(data));
}, []);

export default function Presentation() {
  const [currentSlide, setCurrentSlide] = useState(0);

  const goToNextSlide = () => {
    if (currentSlide < slides.length - 1) {
      setCurrentSlide(currentSlide + 1);
    }
  };

  const goToPreviousSlide = () => {
    if (currentSlide > 0) {
      setCurrentSlide(currentSlide - 1);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-900 to-blue-800 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="relative h-[600px] bg-white rounded-lg shadow-2xl p-8">
          <div className="mb-4">
            <label className="mr-2 text-white">File:</label>
            <select
              className="p-2 rounded"
              value={source}
              onChange={(e) => {
                const file = e.target.value;
                setSource(file);
                fetch(`/processed/${file}`).then((r) => r.json()).then(setSlides);
              }}
            >
              {/** Options will be filled dynamically */}
            </select>
          </div>
          <h1 className="text-4xl font-bold mb-4 text-gray-800">
            {slides[currentSlide]?.title}
          </h1>
          <div className="text-lg text-gray-700">
            {slides[currentSlide]?.content}
          </div>
          
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
            <button
              onClick={goToPreviousSlide}
              className="px-4 py-2 mr-4 bg-blue-600 text-white rounded hover:bg-blue-700"
              disabled={currentSlide === 0}
            >
              Previous
            </button>
            <button
              onClick={goToNextSlide}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              disabled={currentSlide === slides.length - 1}
            >
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
