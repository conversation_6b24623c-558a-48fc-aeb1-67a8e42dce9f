import { useState, useEffect } from 'react';

interface Slide {
  title: string;
  content: string;
}

export default function Presentation() {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [slides, setSlides] = useState<Slide[]>([]);
  const [availableFiles, setAvailableFiles] = useState<string[]>([]);
  const [selectedFile, setSelectedFile] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);

  // Load available files on component mount
  useEffect(() => {
    fetch('/processed/index.json')
      .then((r) => r.json())
      .then((files: string[]) => {
        setAvailableFiles(files);
        if (files.length > 0) {
          setSelectedFile(files[0]);
          return fetch(`/processed/${files[0]}`).then((r) => r.json());
        }
        return [];
      })
      .then((data) => {
        setSlides(data);
        setIsLoading(false);
      })
      .catch((error) => {
        console.error('Error loading files:', error);
        setIsLoading(false);
      });
  }, []);

  // Load slides when file selection changes
  const loadSlidesFromFile = (filename: string) => {
    if (!filename) return;

    setIsLoading(true);
    fetch(`/processed/${filename}`)
      .then((r) => r.json())
      .then((data) => {
        setSlides(data);
        setCurrentSlide(0); // Reset to first slide
        setSelectedFile(filename);
        setIsLoading(false);
      })
      .catch((error) => {
        console.error('Error loading slides:', error);
        setIsLoading(false);
      });
  };

  // Import all slides from all files
  const importAllSlides = () => {
    if (availableFiles.length === 0) return;

    setIsLoading(true);
    const promises = availableFiles.map(file =>
      fetch(`/processed/${file}`).then(r => r.json())
    );

    Promise.all(promises)
      .then((allSlideArrays) => {
        const combinedSlides = allSlideArrays.flat();
        setSlides(combinedSlides);
        setCurrentSlide(0);
        setSelectedFile('All Files');
        setIsLoading(false);
      })
      .catch((error) => {
        console.error('Error importing all slides:', error);
        setIsLoading(false);
      });
  };

  const goToNextSlide = () => {
    if (currentSlide < slides.length - 1) {
      setCurrentSlide(currentSlide + 1);
    }
  };

  const goToPreviousSlide = () => {
    if (currentSlide > 0) {
      setCurrentSlide(currentSlide - 1);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-blue-900 to-blue-800 p-8 flex items-center justify-center">
        <div className="text-white text-xl">Loading presentation...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-900 to-blue-800 p-8">
      <div className="max-w-4xl mx-auto">
        {/* File Selection Controls */}
        <div className="mb-6 bg-white/10 backdrop-blur-sm rounded-lg p-4">
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center gap-2">
              <label className="text-white font-medium">Select File:</label>
              <select
                className="px-3 py-2 rounded-md border border-gray-300 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={selectedFile}
                onChange={(e) => {
                  const file = e.target.value;
                  if (file && file !== 'All Files') {
                    loadSlidesFromFile(file);
                  }
                }}
              >
                <option value="">Select a file...</option>
                {availableFiles.map((file) => (
                  <option key={file} value={file}>
                    {file.replace('.json', '')}
                  </option>
                ))}
              </select>
            </div>

            <button
              onClick={importAllSlides}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors"
              disabled={availableFiles.length === 0}
            >
              Import All Files
            </button>

            <div className="text-white/80 text-sm">
              {slides.length > 0 ? `${slides.length} slides loaded` : 'No slides loaded'}
            </div>
          </div>
        </div>

        {/* Slide Display */}
        <div className="relative h-[600px] bg-white rounded-lg shadow-2xl p-8">
          {slides.length > 0 ? (
            <>
              <h1 className="text-4xl font-bold mb-4 text-gray-800">
                {slides[currentSlide]?.title || 'Untitled Slide'}
              </h1>
              <div
                className="text-lg text-gray-700 overflow-auto max-h-[400px]"
                dangerouslySetInnerHTML={{
                  __html: slides[currentSlide]?.content || 'No content available'
                }}
              />

              {/* Slide Counter */}
              <div className="absolute top-4 right-4 text-gray-500 text-sm">
                {currentSlide + 1} / {slides.length}
              </div>

              {/* Navigation Buttons */}
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                <button
                  onClick={goToPreviousSlide}
                  className="px-4 py-2 mr-4 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                  disabled={currentSlide === 0}
                >
                  Previous
                </button>
                <button
                  onClick={goToNextSlide}
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                  disabled={currentSlide === slides.length - 1}
                >
                  Next
                </button>
              </div>
            </>
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center text-gray-500">
                <h2 className="text-2xl font-bold mb-2">No Slides Available</h2>
                <p>Please select a file or import slides to get started.</p>
                <p className="text-sm mt-2">
                  Add HTML files to the ToBeProcessed folder and run the processing script.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
