# Presentation Project

A modern React-based presentation system that automatically converts HTML files into interactive slide presentations.

## Features

- **Automatic HTML to Slides Conversion**: Drop HTML files into the `ToBeProcessed` folder and convert them to slides
- **File Selection**: Choose from processed files to create presentations
- **Import All**: Combine slides from multiple files into one presentation
- **Modern UI**: Built with React, TypeScript, and Tailwind CSS
- **Responsive Design**: Works on desktop and mobile devices
- **Slide Navigation**: Previous/Next buttons with slide counter

## Quick Start

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Add HTML content**:
   - Place your HTML files in the `../ToBeProcessed/` folder
   - HTML should use `<section>` tags for individual slides, or `<h1>`, `<h2>`, `<h3>` tags to auto-split content

3. **Process the content**:
   ```bash
   npm run process:slides
   ```

4. **Start the development server**:
   ```bash
   npm run dev
   ```

5. **View your presentation**:
   - Open the provided localhost URL
   - Select files from the dropdown or use "Import All Files"
   - Navigate through slides using Previous/Next buttons

## Workflow

### 1. Content Processing
- **Input**: HTML files in `../ToBeProcessed/`
- **Processing**: Run `npm run process:slides`
- **Output**: JSON slide files in `../Processed/` and `public/processed/`

### 2. HTML Structure for Best Results
```html
<!-- Option 1: Using sections (recommended) -->
<section>
    <h1>Slide Title</h1>
    <p>Slide content...</p>
</section>

<!-- Option 2: Using headings (auto-split) -->
<h1>First Slide</h1>
<p>Content for first slide...</p>

<h2>Second Slide</h2>
<p>Content for second slide...</p>
```

### 3. File Management
- **ToBeProcessed/**: Drop new HTML files here
- **Processed/**: Processed JSON files (backup)
- **public/processed/**: JSON files served by the web app

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run process:slides` - Convert HTML files to slides
- `npm run lint` - Run ESLint

## Technology Stack

- **React 19** - UI framework
- **TypeScript** - Type safety
- **Vite** - Build tool and dev server
- **Tailwind CSS** - Styling
- **Cheerio** - HTML parsing for slide conversion
- **fs-extra** - File system utilities

## Example Usage

1. Create an HTML file in `../ToBeProcessed/my-presentation.html`
2. Run `npm run process:slides` to convert it to slides
3. Start the dev server with `npm run dev`
4. Select your file from the dropdown or use "Import All Files"
5. Navigate through your presentation!

## Contributing

Feel free to submit issues and enhancement requests!
