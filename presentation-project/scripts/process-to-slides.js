#!/usr/bin/env node

import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import cheerio from 'cheerio';

// __dirname replacement for ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function main() {
  const rootDir = path.resolve(__dirname, '..', '..'); // project root
  const toBeDir = path.join(rootDir, 'ToBeProcessed');
  const processedDir = path.join(rootDir, 'Processed');
  const publicProcessedDir = path.join(__dirname, '..', 'public', 'processed');

  await fs.ensureDir(toBeDir);
  await fs.ensureDir(processedDir);
  await fs.ensureDir(publicProcessedDir);

  const files = (await fs.readdir(toBeDir)).filter((f) => f.endsWith('.html'));

  const index = [];
  for (const file of files) {
    const filePath = path.join(toBeDir, file);
    const html = await fs.readFile(filePath, 'utf-8');
    const slides = parseHtmlToSlides(html);

    const jsonName = file.replace(/\.html$/i, '.json');
    const processedPath = path.join(processedDir, jsonName);
    const publicPath = path.join(publicProcessedDir, jsonName);

    await fs.writeJson(processedPath, slides, { spaces: 2 });
    await fs.writeJson(publicPath, slides, { spaces: 2 });

    index.push(jsonName);
  }

  // write index
  await fs.writeJson(path.join(processedDir, 'index.json'), index, { spaces: 2 });
  await fs.writeJson(path.join(publicProcessedDir, 'index.json'), index, { spaces: 2 });

  console.log(`Processed ${files.length} file(s) into slides.`);
}

function parseHtmlToSlides(html) {
  const $ = cheerio.load(html);
  const slideElems = $('section').length ? $('section').toArray() : $('body').children().toArray();
  const slides = [];

  let current = { title: '', content: '' };
  slideElems.forEach((el) => {
    if (el.tagName === 'section') {
      const titleElem = $(el).find('h1, h2, h3').first();
      slides.push({
        title: titleElem.text() || 'Slide',
        content: $(el).html() || '',
      });
    } else {
      // Break on headings if no section tags
      if (/h[1-3]/i.test(el.tagName)) {
        if (current.content) {
          slides.push(current);
          current = { title: '', content: '' };
        }
        current.title = $(el).text();
      } else {
        current.content += $.html(el);
      }
    }
  });
  if (current.content) {
    slides.push(current);
  }
  return slides;
}

main().catch((err) => {
  console.error(err);
  process.exit(1);
});
