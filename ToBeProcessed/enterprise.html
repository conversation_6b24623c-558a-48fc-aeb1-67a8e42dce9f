
<!DOCTYPE html>
<html lang="">
  <head><script>window.__params={project_id:"38f2eb4f-0cf7-4d84-9caa-86b74c6137b9",slide_id:""}</script>
    <meta charset="UTF-8">
    <link rel="icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Genspark</title>
    <script type="module" crossorigin>var wv=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var j3=wv((tn,nn)=>{(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const s of i.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();/**
* @vue/shared v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function ya(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const et={},so=[],Pn=()=>{},_v=()=>!1,xs=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),xa=e=>e.startsWith("onUpdate:"),Ct=Object.assign,wa=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Cv=Object.prototype.hasOwnProperty,je=(e,t)=>Cv.call(e,t),xe=Array.isArray,lo=e=>ws(e)==="[object Map]",_f=e=>ws(e)==="[object Set]",Ee=e=>typeof e=="function",ft=e=>typeof e=="string",Kn=e=>typeof e=="symbol",ot=e=>e!==null&&typeof e=="object",Cf=e=>(ot(e)||Ee(e))&&Ee(e.then)&&Ee(e.catch),Sf=Object.prototype.toString,ws=e=>Sf.call(e),Sv=e=>ws(e).slice(8,-1),Ef=e=>ws(e)==="[object Object]",_a=e=>ft(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,No=ya(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),_s=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Ev=/-(\w)/g,dr=_s(e=>e.replace(Ev,(t,n)=>n?n.toUpperCase():"")),Tv=/\B([A-Z])/g,jr=_s(e=>e.replace(Tv,"-$1").toLowerCase()),Tf=_s(e=>e.charAt(0).toUpperCase()+e.slice(1)),Js=_s(e=>e?`on${Tf(e)}`:""),cr=(e,t)=>!Object.is(e,t),Qs=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},$f=(e,t,n,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})},$v=e=>{const t=parseFloat(e);return isNaN(t)?e:t},Pv=e=>{const t=ft(e)?Number(e):NaN;return isNaN(t)?e:t};let Tc;const Cs=()=>Tc||(Tc=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Tt(e){if(xe(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],o=ft(r)?Iv(r):Tt(r);if(o)for(const i in o)t[i]=o[i]}return t}else if(ft(e)||ot(e))return e}const Ov=/;(?![^(]*\))/g,kv=/:([^]+)/,Av=/\/\*[^]*?\*\//g;function Iv(e){const t={};return e.replace(Av,"").split(Ov).forEach(n=>{if(n){const r=n.split(kv);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function Wn(e){let t="";if(ft(e))t=e;else if(xe(e))for(let n=0;n<e.length;n++){const r=Wn(e[n]);r&&(t+=r+" ")}else if(ot(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const Rv="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Lv=ya(Rv);function Pf(e){return!!e||e===""}const Of=e=>!!(e&&e.__v_isRef===!0),Ve=e=>ft(e)?e:e==null?"":xe(e)||ot(e)&&(e.toString===Sf||!Ee(e.toString))?Of(e)?Ve(e.value):JSON.stringify(e,kf,2):String(e),kf=(e,t)=>Of(t)?kf(e,t.value):lo(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[r,o],i)=>(n[el(r,i)+" =>"]=o,n),{})}:_f(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>el(n))}:Kn(t)?el(t):ot(t)&&!xe(t)&&!Ef(t)?String(t):t,el=(e,t="")=>{var n;return Kn(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Jt;class Af{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Jt,!t&&Jt&&(this.index=(Jt.scopes||(Jt.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=Jt;try{return Jt=this,t()}finally{Jt=n}}}on(){Jt=this}off(){Jt=this.parent}stop(t){if(this._active){this._active=!1;let n,r;for(n=0,r=this.effects.length;n<r;n++)this.effects[n].stop();for(this.effects.length=0,n=0,r=this.cleanups.length;n<r;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,r=this.scopes.length;n<r;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const o=this.parent.scopes.pop();o&&o!==this&&(this.parent.scopes[this.index]=o,o.index=this.index)}this.parent=void 0}}}function Mv(e){return new Af(e)}function Fv(){return Jt}let rt;const tl=new WeakSet;class If{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Jt&&Jt.active&&Jt.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,tl.has(this)&&(tl.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Lf(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,$c(this),Mf(this);const t=rt,n=gn;rt=this,gn=!0;try{return this.fn()}finally{Ff(this),rt=t,gn=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Ea(t);this.deps=this.depsTail=void 0,$c(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?tl.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Al(this)&&this.run()}get dirty(){return Al(this)}}let Rf=0,Do,zo;function Lf(e,t=!1){if(e.flags|=8,t){e.next=zo,zo=e;return}e.next=Do,Do=e}function Ca(){Rf++}function Sa(){if(--Rf>0)return;if(zo){let t=zo;for(zo=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Do;){let t=Do;for(Do=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(r){e||(e=r)}t=n}}if(e)throw e}function Mf(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Ff(e){let t,n=e.depsTail,r=n;for(;r;){const o=r.prevDep;r.version===-1?(r===n&&(n=o),Ea(r),Nv(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=o}e.deps=t,e.depsTail=n}function Al(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Nf(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Nf(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Ko))return;e.globalVersion=Ko;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!Al(e)){e.flags&=-3;return}const n=rt,r=gn;rt=e,gn=!0;try{Mf(e);const o=e.fn(e._value);(t.version===0||cr(o,e._value))&&(e._value=o,t.version++)}catch(o){throw t.version++,o}finally{rt=n,gn=r,Ff(e),e.flags&=-3}}function Ea(e,t=!1){const{dep:n,prevSub:r,nextSub:o}=e;if(r&&(r.nextSub=o,e.prevSub=void 0),o&&(o.prevSub=r,e.nextSub=void 0),n.subs===e&&(n.subs=r,!r&&n.computed)){n.computed.flags&=-5;for(let i=n.computed.deps;i;i=i.nextDep)Ea(i,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Nv(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let gn=!0;const Df=[];function mr(){Df.push(gn),gn=!1}function br(){const e=Df.pop();gn=e===void 0?!0:e}function $c(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=rt;rt=void 0;try{t()}finally{rt=n}}}let Ko=0;class Dv{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Ta{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!rt||!gn||rt===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==rt)n=this.activeLink=new Dv(rt,this),rt.deps?(n.prevDep=rt.depsTail,rt.depsTail.nextDep=n,rt.depsTail=n):rt.deps=rt.depsTail=n,zf(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const r=n.nextDep;r.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=r),n.prevDep=rt.depsTail,n.nextDep=void 0,rt.depsTail.nextDep=n,rt.depsTail=n,rt.deps===n&&(rt.deps=r)}return n}trigger(t){this.version++,Ko++,this.notify(t)}notify(t){Ca();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Sa()}}}function zf(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let r=t.deps;r;r=r.nextDep)zf(r)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const Qi=new WeakMap,Rr=Symbol(""),Il=Symbol(""),Go=Symbol("");function Rt(e,t,n){if(gn&&rt){let r=Qi.get(e);r||Qi.set(e,r=new Map);let o=r.get(n);o||(r.set(n,o=new Ta),o.map=r,o.key=n),o.track()}}function zn(e,t,n,r,o,i){const s=Qi.get(e);if(!s){Ko++;return}const l=a=>{a&&a.trigger()};if(Ca(),t==="clear")s.forEach(l);else{const a=xe(e),c=a&&_a(n);if(a&&n==="length"){const u=Number(r);s.forEach((d,f)=>{(f==="length"||f===Go||!Kn(f)&&f>=u)&&l(d)})}else switch((n!==void 0||s.has(void 0))&&l(s.get(n)),c&&l(s.get(Go)),t){case"add":a?c&&l(s.get("length")):(l(s.get(Rr)),lo(e)&&l(s.get(Il)));break;case"delete":a||(l(s.get(Rr)),lo(e)&&l(s.get(Il)));break;case"set":lo(e)&&l(s.get(Rr));break}}Sa()}function zv(e,t){const n=Qi.get(e);return n&&n.get(t)}function Zr(e){const t=Me(e);return t===e?t:(Rt(t,"iterate",Go),ln(e)?t:t.map(Lt))}function Ss(e){return Rt(e=Me(e),"iterate",Go),e}const Bv={__proto__:null,[Symbol.iterator](){return nl(this,Symbol.iterator,Lt)},concat(...e){return Zr(this).concat(...e.map(t=>xe(t)?Zr(t):t))},entries(){return nl(this,"entries",e=>(e[1]=Lt(e[1]),e))},every(e,t){return Mn(this,"every",e,t,void 0,arguments)},filter(e,t){return Mn(this,"filter",e,t,n=>n.map(Lt),arguments)},find(e,t){return Mn(this,"find",e,t,Lt,arguments)},findIndex(e,t){return Mn(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Mn(this,"findLast",e,t,Lt,arguments)},findLastIndex(e,t){return Mn(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Mn(this,"forEach",e,t,void 0,arguments)},includes(...e){return rl(this,"includes",e)},indexOf(...e){return rl(this,"indexOf",e)},join(e){return Zr(this).join(e)},lastIndexOf(...e){return rl(this,"lastIndexOf",e)},map(e,t){return Mn(this,"map",e,t,void 0,arguments)},pop(){return To(this,"pop")},push(...e){return To(this,"push",e)},reduce(e,...t){return Pc(this,"reduce",e,t)},reduceRight(e,...t){return Pc(this,"reduceRight",e,t)},shift(){return To(this,"shift")},some(e,t){return Mn(this,"some",e,t,void 0,arguments)},splice(...e){return To(this,"splice",e)},toReversed(){return Zr(this).toReversed()},toSorted(e){return Zr(this).toSorted(e)},toSpliced(...e){return Zr(this).toSpliced(...e)},unshift(...e){return To(this,"unshift",e)},values(){return nl(this,"values",Lt)}};function nl(e,t,n){const r=Ss(e),o=r[t]();return r!==e&&!ln(e)&&(o._next=o.next,o.next=()=>{const i=o._next();return i.value&&(i.value=n(i.value)),i}),o}const Hv=Array.prototype;function Mn(e,t,n,r,o,i){const s=Ss(e),l=s!==e&&!ln(e),a=s[t];if(a!==Hv[t]){const d=a.apply(e,i);return l?Lt(d):d}let c=n;s!==e&&(l?c=function(d,f){return n.call(this,Lt(d),f,e)}:n.length>2&&(c=function(d,f){return n.call(this,d,f,e)}));const u=a.call(s,c,r);return l&&o?o(u):u}function Pc(e,t,n,r){const o=Ss(e);let i=n;return o!==e&&(ln(e)?n.length>3&&(i=function(s,l,a){return n.call(this,s,l,a,e)}):i=function(s,l,a){return n.call(this,s,Lt(l),a,e)}),o[t](i,...r)}function rl(e,t,n){const r=Me(e);Rt(r,"iterate",Go);const o=r[t](...n);return(o===-1||o===!1)&&Oa(n[0])?(n[0]=Me(n[0]),r[t](...n)):o}function To(e,t,n=[]){mr(),Ca();const r=Me(e)[t].apply(e,n);return Sa(),br(),r}const jv=ya("__proto__,__v_isRef,__isVue"),Bf=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Kn));function Wv(e){Kn(e)||(e=String(e));const t=Me(this);return Rt(t,"has",e),t.hasOwnProperty(e)}class Hf{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,r){if(n==="__v_skip")return t.__v_skip;const o=this._isReadonly,i=this._isShallow;if(n==="__v_isReactive")return!o;if(n==="__v_isReadonly")return o;if(n==="__v_isShallow")return i;if(n==="__v_raw")return r===(o?i?Qv:Vf:i?Uf:Wf).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const s=xe(t);if(!o){let a;if(s&&(a=Bv[n]))return a;if(n==="hasOwnProperty")return Wv}const l=Reflect.get(t,n,wt(t)?t:r);return(Kn(n)?Bf.has(n):jv(n))||(o||Rt(t,"get",n),i)?l:wt(l)?s&&_a(n)?l:l.value:ot(l)?o?An(l):yr(l):l}}class jf extends Hf{constructor(t=!1){super(!1,t)}set(t,n,r,o){let i=t[n];if(!this._isShallow){const a=Nr(i);if(!ln(r)&&!Nr(r)&&(i=Me(i),r=Me(r)),!xe(t)&&wt(i)&&!wt(r))return a?!1:(i.value=r,!0)}const s=xe(t)&&_a(n)?Number(n)<t.length:je(t,n),l=Reflect.set(t,n,r,wt(t)?t:o);return t===Me(o)&&(s?cr(r,i)&&zn(t,"set",n,r):zn(t,"add",n,r)),l}deleteProperty(t,n){const r=je(t,n);t[n];const o=Reflect.deleteProperty(t,n);return o&&r&&zn(t,"delete",n,void 0),o}has(t,n){const r=Reflect.has(t,n);return(!Kn(n)||!Bf.has(n))&&Rt(t,"has",n),r}ownKeys(t){return Rt(t,"iterate",xe(t)?"length":Rr),Reflect.ownKeys(t)}}class Uv extends Hf{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Vv=new jf,Kv=new Uv,Gv=new jf(!0),Rl=e=>e,xi=e=>Reflect.getPrototypeOf(e);function Yv(e,t,n){return function(...r){const o=this.__v_raw,i=Me(o),s=lo(i),l=e==="entries"||e===Symbol.iterator&&s,a=e==="keys"&&s,c=o[e](...r),u=n?Rl:t?Ml:Lt;return!t&&Rt(i,"iterate",a?Il:Rr),{next(){const{value:d,done:f}=c.next();return f?{value:d,done:f}:{value:l?[u(d[0]),u(d[1])]:u(d),done:f}},[Symbol.iterator](){return this}}}}function wi(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Xv(e,t){const n={get(o){const i=this.__v_raw,s=Me(i),l=Me(o);e||(cr(o,l)&&Rt(s,"get",o),Rt(s,"get",l));const{has:a}=xi(s),c=t?Rl:e?Ml:Lt;if(a.call(s,o))return c(i.get(o));if(a.call(s,l))return c(i.get(l));i!==s&&i.get(o)},get size(){const o=this.__v_raw;return!e&&Rt(Me(o),"iterate",Rr),Reflect.get(o,"size",o)},has(o){const i=this.__v_raw,s=Me(i),l=Me(o);return e||(cr(o,l)&&Rt(s,"has",o),Rt(s,"has",l)),o===l?i.has(o):i.has(o)||i.has(l)},forEach(o,i){const s=this,l=s.__v_raw,a=Me(l),c=t?Rl:e?Ml:Lt;return!e&&Rt(a,"iterate",Rr),l.forEach((u,d)=>o.call(i,c(u),c(d),s))}};return Ct(n,e?{add:wi("add"),set:wi("set"),delete:wi("delete"),clear:wi("clear")}:{add(o){!t&&!ln(o)&&!Nr(o)&&(o=Me(o));const i=Me(this);return xi(i).has.call(i,o)||(i.add(o),zn(i,"add",o,o)),this},set(o,i){!t&&!ln(i)&&!Nr(i)&&(i=Me(i));const s=Me(this),{has:l,get:a}=xi(s);let c=l.call(s,o);c||(o=Me(o),c=l.call(s,o));const u=a.call(s,o);return s.set(o,i),c?cr(i,u)&&zn(s,"set",o,i):zn(s,"add",o,i),this},delete(o){const i=Me(this),{has:s,get:l}=xi(i);let a=s.call(i,o);a||(o=Me(o),a=s.call(i,o)),l&&l.call(i,o);const c=i.delete(o);return a&&zn(i,"delete",o,void 0),c},clear(){const o=Me(this),i=o.size!==0,s=o.clear();return i&&zn(o,"clear",void 0,void 0),s}}),["keys","values","entries",Symbol.iterator].forEach(o=>{n[o]=Yv(o,e,t)}),n}function $a(e,t){const n=Xv(e,t);return(r,o,i)=>o==="__v_isReactive"?!e:o==="__v_isReadonly"?e:o==="__v_raw"?r:Reflect.get(je(n,o)&&o in r?n:r,o,i)}const qv={get:$a(!1,!1)},Zv={get:$a(!1,!0)},Jv={get:$a(!0,!1)},Wf=new WeakMap,Uf=new WeakMap,Vf=new WeakMap,Qv=new WeakMap;function em(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function tm(e){return e.__v_skip||!Object.isExtensible(e)?0:em(Sv(e))}function yr(e){return Nr(e)?e:Pa(e,!1,Vv,qv,Wf)}function nm(e){return Pa(e,!1,Gv,Zv,Uf)}function An(e){return Pa(e,!0,Kv,Jv,Vf)}function Pa(e,t,n,r,o){if(!ot(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=o.get(e);if(i)return i;const s=tm(e);if(s===0)return e;const l=new Proxy(e,s===2?r:n);return o.set(e,l),l}function ao(e){return Nr(e)?ao(e.__v_raw):!!(e&&e.__v_isReactive)}function Nr(e){return!!(e&&e.__v_isReadonly)}function ln(e){return!!(e&&e.__v_isShallow)}function Oa(e){return e?!!e.__v_raw:!1}function Me(e){const t=e&&e.__v_raw;return t?Me(t):e}function Ll(e){return!je(e,"__v_skip")&&Object.isExtensible(e)&&$f(e,"__v_skip",!0),e}const Lt=e=>ot(e)?yr(e):e,Ml=e=>ot(e)?An(e):e;function wt(e){return e?e.__v_isRef===!0:!1}function D(e){return Gf(e,!1)}function Kf(e){return Gf(e,!0)}function Gf(e,t){return wt(e)?e:new rm(e,t)}class rm{constructor(t,n){this.dep=new Ta,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:Me(t),this._value=n?t:Lt(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,r=this.__v_isShallow||ln(t)||Nr(t);t=r?t:Me(t),cr(t,n)&&(this._rawValue=t,this._value=r?t:Lt(t),this.dep.trigger())}}function Ce(e){return wt(e)?e.value:e}const om={get:(e,t,n)=>t==="__v_raw"?e:Ce(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const o=e[t];return wt(o)&&!wt(n)?(o.value=n,!0):Reflect.set(e,t,n,r)}};function Yf(e){return ao(e)?e:new Proxy(e,om)}class im{constructor(t,n,r){this._object=t,this._key=n,this._defaultValue=r,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return zv(Me(this._object),this._key)}}class sm{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function Qe(e,t,n){return wt(e)?e:Ee(e)?new sm(e):ot(e)&&arguments.length>1?lm(e,t,n):D(e)}function lm(e,t,n){const r=e[t];return wt(r)?r:new im(e,t,n)}class am{constructor(t,n,r){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Ta(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Ko-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=r}notify(){if(this.flags|=16,!(this.flags&8)&&rt!==this)return Lf(this,!0),!0}get value(){const t=this.dep.track();return Nf(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function cm(e,t,n=!1){let r,o;return Ee(e)?r=e:(r=e.get,o=e.set),new am(r,o,n)}const _i={},es=new WeakMap;let Pr;function um(e,t=!1,n=Pr){if(n){let r=es.get(n);r||es.set(n,r=[]),r.push(e)}}function dm(e,t,n=et){const{immediate:r,deep:o,once:i,scheduler:s,augmentJob:l,call:a}=n,c=b=>o?b:ln(b)||o===!1||o===0?Bn(b,1):Bn(b);let u,d,f,m,p=!1,g=!1;if(wt(e)?(d=()=>e.value,p=ln(e)):ao(e)?(d=()=>c(e),p=!0):xe(e)?(g=!0,p=e.some(b=>ao(b)||ln(b)),d=()=>e.map(b=>{if(wt(b))return b.value;if(ao(b))return c(b);if(Ee(b))return a?a(b,2):b()})):Ee(e)?t?d=a?()=>a(e,2):e:d=()=>{if(f){mr();try{f()}finally{br()}}const b=Pr;Pr=u;try{return a?a(e,3,[m]):e(m)}finally{Pr=b}}:d=Pn,t&&o){const b=d,C=o===!0?1/0:o;d=()=>Bn(b(),C)}const x=Fv(),h=()=>{u.stop(),x&&x.active&&wa(x.effects,u)};if(i&&t){const b=t;t=(...C)=>{b(...C),h()}}let y=g?new Array(e.length).fill(_i):_i;const P=b=>{if(!(!(u.flags&1)||!u.dirty&&!b))if(t){const C=u.run();if(o||p||(g?C.some((S,v)=>cr(S,y[v])):cr(C,y))){f&&f();const S=Pr;Pr=u;try{const v=[C,y===_i?void 0:g&&y[0]===_i?[]:y,m];a?a(t,3,v):t(...v),y=C}finally{Pr=S}}}else u.run()};return l&&l(P),u=new If(d),u.scheduler=s?()=>s(P,!1):P,m=b=>um(b,!1,u),f=u.onStop=()=>{const b=es.get(u);if(b){if(a)a(b,4);else for(const C of b)C();es.delete(u)}},t?r?P(!0):y=u.run():s?s(P.bind(null,!0),!0):u.run(),h.pause=u.pause.bind(u),h.resume=u.resume.bind(u),h.stop=h,h}function Bn(e,t=1/0,n){if(t<=0||!ot(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,wt(e))Bn(e.value,t,n);else if(xe(e))for(let r=0;r<e.length;r++)Bn(e[r],t,n);else if(_f(e)||lo(e))e.forEach(r=>{Bn(r,t,n)});else if(Ef(e)){for(const r in e)Bn(e[r],t,n);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&Bn(e[r],t,n)}return e}/**
* @vue/runtime-core v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function ui(e,t,n,r){try{return r?e(...r):e()}catch(o){Es(o,t,n)}}function vn(e,t,n,r){if(Ee(e)){const o=ui(e,t,n,r);return o&&Cf(o)&&o.catch(i=>{Es(i,t,n)}),o}if(xe(e)){const o=[];for(let i=0;i<e.length;i++)o.push(vn(e[i],t,n,r));return o}}function Es(e,t,n,r=!0){const o=t?t.vnode:null,{errorHandler:i,throwUnhandledErrorInProduction:s}=t&&t.appContext.config||et;if(t){let l=t.parent;const a=t.proxy,c=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const u=l.ec;if(u){for(let d=0;d<u.length;d++)if(u[d](e,a,c)===!1)return}l=l.parent}if(i){mr(),ui(i,null,10,[e,a,c]),br();return}}fm(e,n,o,r,s)}function fm(e,t,n,r=!0,o=!1){if(o)throw e;console.error(e)}const Ht=[];let Tn=-1;const co=[];let nr=null,no=0;const Xf=Promise.resolve();let ts=null;function qt(e){const t=ts||Xf;return e?t.then(this?e.bind(this):e):t}function hm(e){let t=Tn+1,n=Ht.length;for(;t<n;){const r=t+n>>>1,o=Ht[r],i=Yo(o);i<e||i===e&&o.flags&2?t=r+1:n=r}return t}function ka(e){if(!(e.flags&1)){const t=Yo(e),n=Ht[Ht.length-1];!n||!(e.flags&2)&&t>=Yo(n)?Ht.push(e):Ht.splice(hm(t),0,e),e.flags|=1,qf()}}function qf(){ts||(ts=Xf.then(Jf))}function pm(e){xe(e)?co.push(...e):nr&&e.id===-1?nr.splice(no+1,0,e):e.flags&1||(co.push(e),e.flags|=1),qf()}function Oc(e,t,n=Tn+1){for(;n<Ht.length;n++){const r=Ht[n];if(r&&r.flags&2){if(e&&r.id!==e.uid)continue;Ht.splice(n,1),n--,r.flags&4&&(r.flags&=-2),r(),r.flags&4||(r.flags&=-2)}}}function Zf(e){if(co.length){const t=[...new Set(co)].sort((n,r)=>Yo(n)-Yo(r));if(co.length=0,nr){nr.push(...t);return}for(nr=t,no=0;no<nr.length;no++){const n=nr[no];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}nr=null,no=0}}const Yo=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Jf(e){try{for(Tn=0;Tn<Ht.length;Tn++){const t=Ht[Tn];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),ui(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Tn<Ht.length;Tn++){const t=Ht[Tn];t&&(t.flags&=-2)}Tn=-1,Ht.length=0,Zf(),ts=null,(Ht.length||co.length)&&Jf()}}let At=null,Qf=null;function ns(e){const t=At;return At=e,Qf=e&&e.type.__scopeId||null,t}function ro(e,t=At,n){if(!t||e._n)return e;const r=(...o)=>{r._d&&Wc(-1);const i=ns(t);let s;try{s=e(...o)}finally{ns(i),r._d&&Wc(1)}return s};return r._n=!0,r._c=!0,r._d=!0,r}function Un(e,t){if(At===null)return e;const n=ks(At),r=e.dirs||(e.dirs=[]);for(let o=0;o<t.length;o++){let[i,s,l,a=et]=t[o];i&&(Ee(i)&&(i={mounted:i,updated:i}),i.deep&&Bn(s),r.push({dir:i,instance:n,value:s,oldValue:void 0,arg:l,modifiers:a}))}return e}function Sr(e,t,n,r){const o=e.dirs,i=t&&t.dirs;for(let s=0;s<o.length;s++){const l=o[s];i&&(l.oldValue=i[s].value);let a=l.dir[r];a&&(mr(),vn(a,n,8,[e.el,l,e,t]),br())}}const eh=Symbol("_vte"),th=e=>e.__isTeleport,Bo=e=>e&&(e.disabled||e.disabled===""),kc=e=>e&&(e.defer||e.defer===""),Ac=e=>typeof SVGElement<"u"&&e instanceof SVGElement,Ic=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,Fl=(e,t)=>{const n=e&&e.to;return ft(n)?t?t(n):null:n},nh={name:"Teleport",__isTeleport:!0,process(e,t,n,r,o,i,s,l,a,c){const{mc:u,pc:d,pbc:f,o:{insert:m,querySelector:p,createText:g,createComment:x}}=c,h=Bo(t.props);let{shapeFlag:y,children:P,dynamicChildren:b}=t;if(e==null){const C=t.el=g(""),S=t.anchor=g("");m(C,n,r),m(S,n,r);const v=(A,F)=>{y&16&&(o&&o.isCE&&(o.ce._teleportTarget=A),u(P,A,F,o,i,s,l,a))},E=()=>{const A=t.target=Fl(t.props,p),F=rh(A,t,g,m);A&&(s!=="svg"&&Ac(A)?s="svg":s!=="mathml"&&Ic(A)&&(s="mathml"),h||(v(A,F),Ui(t,!1)))};h&&(v(n,S),Ui(t,!0)),kc(t.props)?Bt(()=>{E(),t.el.__isMounted=!0},i):E()}else{if(kc(t.props)&&!e.el.__isMounted){Bt(()=>{nh.process(e,t,n,r,o,i,s,l,a,c),delete e.el.__isMounted},i);return}t.el=e.el,t.targetStart=e.targetStart;const C=t.anchor=e.anchor,S=t.target=e.target,v=t.targetAnchor=e.targetAnchor,E=Bo(e.props),A=E?n:S,F=E?C:v;if(s==="svg"||Ac(S)?s="svg":(s==="mathml"||Ic(S))&&(s="mathml"),b?(f(e.dynamicChildren,b,A,o,i,s,l),La(e,t,!0)):a||d(e,t,A,F,o,i,s,l,!1),h)E?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):Ci(t,n,C,c,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const B=t.target=Fl(t.props,p);B&&Ci(t,B,null,c,0)}else E&&Ci(t,S,v,c,1);Ui(t,h)}},remove(e,t,n,{um:r,o:{remove:o}},i){const{shapeFlag:s,children:l,anchor:a,targetStart:c,targetAnchor:u,target:d,props:f}=e;if(d&&(o(c),o(u)),i&&o(a),s&16){const m=i||!Bo(f);for(let p=0;p<l.length;p++){const g=l[p];r(g,t,n,m,!!g.dynamicChildren)}}},move:Ci,hydrate:gm};function Ci(e,t,n,{o:{insert:r},m:o},i=2){i===0&&r(e.targetAnchor,t,n);const{el:s,anchor:l,shapeFlag:a,children:c,props:u}=e,d=i===2;if(d&&r(s,t,n),(!d||Bo(u))&&a&16)for(let f=0;f<c.length;f++)o(c[f],t,n,2);d&&r(l,t,n)}function gm(e,t,n,r,o,i,{o:{nextSibling:s,parentNode:l,querySelector:a,insert:c,createText:u}},d){const f=t.target=Fl(t.props,a);if(f){const m=Bo(t.props),p=f._lpa||f.firstChild;if(t.shapeFlag&16)if(m)t.anchor=d(s(e),t,l(e),n,r,o,i),t.targetStart=p,t.targetAnchor=p&&s(p);else{t.anchor=s(e);let g=p;for(;g;){if(g&&g.nodeType===8){if(g.data==="teleport start anchor")t.targetStart=g;else if(g.data==="teleport anchor"){t.targetAnchor=g,f._lpa=t.targetAnchor&&s(t.targetAnchor);break}}g=s(g)}t.targetAnchor||rh(f,t,u,c),d(p&&s(p),t,f,n,r,o,i)}Ui(t,m)}return t.anchor&&s(t.anchor)}const Ts=nh;function Ui(e,t){const n=e.ctx;if(n&&n.ut){let r,o;for(t?(r=e.el,o=e.anchor):(r=e.targetStart,o=e.targetAnchor);r&&r!==o;)r.nodeType===1&&r.setAttribute("data-v-owner",n.uid),r=r.nextSibling;n.ut()}}function rh(e,t,n,r){const o=t.targetStart=n(""),i=t.targetAnchor=n("");return o[eh]=i,e&&(r(o,e),r(i,e)),i}const rr=Symbol("_leaveCb"),Si=Symbol("_enterCb");function oh(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return mt(()=>{e.isMounted=!0}),pt(()=>{e.isUnmounting=!0}),e}const rn=[Function,Array],ih={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:rn,onEnter:rn,onAfterEnter:rn,onEnterCancelled:rn,onBeforeLeave:rn,onLeave:rn,onAfterLeave:rn,onLeaveCancelled:rn,onBeforeAppear:rn,onAppear:rn,onAfterAppear:rn,onAppearCancelled:rn},sh=e=>{const t=e.subTree;return t.component?sh(t.component):t},vm={name:"BaseTransition",props:ih,setup(e,{slots:t}){const n=In(),r=oh();return()=>{const o=t.default&&Aa(t.default(),!0);if(!o||!o.length)return;const i=lh(o),s=Me(e),{mode:l}=s;if(r.isLeaving)return ol(i);const a=Rc(i);if(!a)return ol(i);let c=Xo(a,s,r,n,d=>c=d);a.type!==$t&&Dr(a,c);let u=n.subTree&&Rc(n.subTree);if(u&&u.type!==$t&&!Or(a,u)&&sh(n).type!==$t){let d=Xo(u,s,r,n);if(Dr(u,d),l==="out-in"&&a.type!==$t)return r.isLeaving=!0,d.afterLeave=()=>{r.isLeaving=!1,n.job.flags&8||n.update(),delete d.afterLeave,u=void 0},ol(i);l==="in-out"&&a.type!==$t?d.delayLeave=(f,m,p)=>{const g=ah(r,u);g[String(u.key)]=u,f[rr]=()=>{m(),f[rr]=void 0,delete c.delayedLeave,u=void 0},c.delayedLeave=()=>{p(),delete c.delayedLeave,u=void 0}}:u=void 0}else u&&(u=void 0);return i}}};function lh(e){let t=e[0];if(e.length>1){for(const n of e)if(n.type!==$t){t=n;break}}return t}const mm=vm;function ah(e,t){const{leavingVNodes:n}=e;let r=n.get(t.type);return r||(r=Object.create(null),n.set(t.type,r)),r}function Xo(e,t,n,r,o){const{appear:i,mode:s,persisted:l=!1,onBeforeEnter:a,onEnter:c,onAfterEnter:u,onEnterCancelled:d,onBeforeLeave:f,onLeave:m,onAfterLeave:p,onLeaveCancelled:g,onBeforeAppear:x,onAppear:h,onAfterAppear:y,onAppearCancelled:P}=t,b=String(e.key),C=ah(n,e),S=(A,F)=>{A&&vn(A,r,9,F)},v=(A,F)=>{const B=F[1];S(A,F),xe(A)?A.every(R=>R.length<=1)&&B():A.length<=1&&B()},E={mode:s,persisted:l,beforeEnter(A){let F=a;if(!n.isMounted)if(i)F=x||a;else return;A[rr]&&A[rr](!0);const B=C[b];B&&Or(e,B)&&B.el[rr]&&B.el[rr](),S(F,[A])},enter(A){let F=c,B=u,R=d;if(!n.isMounted)if(i)F=h||c,B=y||u,R=P||d;else return;let j=!1;const Q=A[Si]=H=>{j||(j=!0,H?S(R,[A]):S(B,[A]),E.delayedLeave&&E.delayedLeave(),A[Si]=void 0)};F?v(F,[A,Q]):Q()},leave(A,F){const B=String(e.key);if(A[Si]&&A[Si](!0),n.isUnmounting)return F();S(f,[A]);let R=!1;const j=A[rr]=Q=>{R||(R=!0,F(),Q?S(g,[A]):S(p,[A]),A[rr]=void 0,C[B]===e&&delete C[B])};C[B]=e,m?v(m,[A,j]):j()},clone(A){const F=Xo(A,t,n,r,o);return o&&o(F),F}};return E}function ol(e){if($s(e))return e=cn(e),e.children=null,e}function Rc(e){if(!$s(e))return th(e.type)&&e.children?lh(e.children):e;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&Ee(n.default))return n.default()}}function Dr(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Dr(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Aa(e,t=!1,n){let r=[],o=0;for(let i=0;i<e.length;i++){let s=e[i];const l=n==null?s.key:String(n)+String(s.key!=null?s.key:i);s.type===We?(s.patchFlag&128&&o++,r=r.concat(Aa(s.children,t,l))):(t||s.type!==$t)&&r.push(l!=null?cn(s,{key:l}):s)}if(o>1)for(let i=0;i<r.length;i++)r[i].patchFlag=-2;return r}/*! #__NO_SIDE_EFFECTS__ */function ge(e,t){return Ee(e)?Ct({name:e.name},t,{setup:e}):e}function ch(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function rs(e,t,n,r,o=!1){if(xe(e)){e.forEach((p,g)=>rs(p,t&&(xe(t)?t[g]:t),n,r,o));return}if(uo(r)&&!o){r.shapeFlag&512&&r.type.__asyncResolved&&r.component.subTree.component&&rs(e,t,n,r.component.subTree);return}const i=r.shapeFlag&4?ks(r.component):r.el,s=o?null:i,{i:l,r:a}=e,c=t&&t.r,u=l.refs===et?l.refs={}:l.refs,d=l.setupState,f=Me(d),m=d===et?()=>!1:p=>je(f,p);if(c!=null&&c!==a&&(ft(c)?(u[c]=null,m(c)&&(d[c]=null)):wt(c)&&(c.value=null)),Ee(a))ui(a,l,12,[s,u]);else{const p=ft(a),g=wt(a);if(p||g){const x=()=>{if(e.f){const h=p?m(a)?d[a]:u[a]:a.value;o?xe(h)&&wa(h,i):xe(h)?h.includes(i)||h.push(i):p?(u[a]=[i],m(a)&&(d[a]=u[a])):(a.value=[i],e.k&&(u[e.k]=a.value))}else p?(u[a]=s,m(a)&&(d[a]=s)):g&&(a.value=s,e.k&&(u[e.k]=s))};s?(x.id=-1,Bt(x,n)):x()}}}Cs().requestIdleCallback;Cs().cancelIdleCallback;const uo=e=>!!e.type.__asyncLoader,$s=e=>e.type.__isKeepAlive;function uh(e,t){fh(e,"a",t)}function dh(e,t){fh(e,"da",t)}function fh(e,t,n=Ft){const r=e.__wdc||(e.__wdc=()=>{let o=n;for(;o;){if(o.isDeactivated)return;o=o.parent}return e()});if(Ps(t,r,n),n){let o=n.parent;for(;o&&o.parent;)$s(o.parent.vnode)&&bm(r,t,n,o),o=o.parent}}function bm(e,t,n,r){const o=Ps(t,e,r,!0);zr(()=>{wa(r[t],o)},n)}function Ps(e,t,n=Ft,r=!1){if(n){const o=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...s)=>{mr();const l=di(n),a=vn(t,n,e,s);return l(),br(),a});return r?o.unshift(i):o.push(i),i}}const Gn=e=>(t,n=Ft)=>{(!Zo||e==="sp")&&Ps(e,(...r)=>t(...r),n)},xr=Gn("bm"),mt=Gn("m"),hh=Gn("bu"),Ia=Gn("u"),pt=Gn("bum"),zr=Gn("um"),ym=Gn("sp"),xm=Gn("rtg"),wm=Gn("rtc");function _m(e,t=Ft){Ps("ec",e,t)}const Cm=Symbol.for("v-ndc");function Lc(e,t,n,r){let o;const i=n,s=xe(e);if(s||ft(e)){const l=s&&ao(e);let a=!1;l&&(a=!ln(e),e=Ss(e)),o=new Array(e.length);for(let c=0,u=e.length;c<u;c++)o[c]=t(a?Lt(e[c]):e[c],c,void 0,i)}else if(typeof e=="number"){o=new Array(e);for(let l=0;l<e;l++)o[l]=t(l+1,l,void 0,i)}else if(ot(e))if(e[Symbol.iterator])o=Array.from(e,(l,a)=>t(l,a,void 0,i));else{const l=Object.keys(e);o=new Array(l.length);for(let a=0,c=l.length;a<c;a++){const u=l[a];o[a]=t(e[u],u,a,i)}}else o=[];return o}function Sm(e,t,n={},r,o){if(At.ce||At.parent&&uo(At.parent)&&At.parent.ce)return ye(),sr(We,null,[Ge("slot",n,r)],64);let i=e[t];i&&i._c&&(i._d=!1),ye();const s=i&&ph(i(n)),l=n.key||s&&s.key,a=sr(We,{key:(l&&!Kn(l)?l:`_${t}`)+""},s||[],s&&e._===1?64:-2);return a.scopeId&&(a.slotScopeIds=[a.scopeId+"-s"]),i&&i._c&&(i._d=!0),a}function ph(e){return e.some(t=>go(t)?!(t.type===$t||t.type===We&&!ph(t.children)):!0)?e:null}const Nl=e=>e?Lh(e)?ks(e):Nl(e.parent):null,Ho=Ct(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Nl(e.parent),$root:e=>Nl(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>vh(e),$forceUpdate:e=>e.f||(e.f=()=>{ka(e.update)}),$nextTick:e=>e.n||(e.n=qt.bind(e.proxy)),$watch:e=>Vm.bind(e)}),il=(e,t)=>e!==et&&!e.__isScriptSetup&&je(e,t),Em={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:r,data:o,props:i,accessCache:s,type:l,appContext:a}=e;let c;if(t[0]!=="$"){const m=s[t];if(m!==void 0)switch(m){case 1:return r[t];case 2:return o[t];case 4:return n[t];case 3:return i[t]}else{if(il(r,t))return s[t]=1,r[t];if(o!==et&&je(o,t))return s[t]=2,o[t];if((c=e.propsOptions[0])&&je(c,t))return s[t]=3,i[t];if(n!==et&&je(n,t))return s[t]=4,n[t];Dl&&(s[t]=0)}}const u=Ho[t];let d,f;if(u)return t==="$attrs"&&Rt(e.attrs,"get",""),u(e);if((d=l.__cssModules)&&(d=d[t]))return d;if(n!==et&&je(n,t))return s[t]=4,n[t];if(f=a.config.globalProperties,je(f,t))return f[t]},set({_:e},t,n){const{data:r,setupState:o,ctx:i}=e;return il(o,t)?(o[t]=n,!0):r!==et&&je(r,t)?(r[t]=n,!0):je(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:o,propsOptions:i}},s){let l;return!!n[s]||e!==et&&je(e,s)||il(t,s)||(l=i[0])&&je(l,s)||je(r,s)||je(Ho,s)||je(o.config.globalProperties,s)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:je(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Mc(e){return xe(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Dl=!0;function Tm(e){const t=vh(e),n=e.proxy,r=e.ctx;Dl=!1,t.beforeCreate&&Fc(t.beforeCreate,e,"bc");const{data:o,computed:i,methods:s,watch:l,provide:a,inject:c,created:u,beforeMount:d,mounted:f,beforeUpdate:m,updated:p,activated:g,deactivated:x,beforeDestroy:h,beforeUnmount:y,destroyed:P,unmounted:b,render:C,renderTracked:S,renderTriggered:v,errorCaptured:E,serverPrefetch:A,expose:F,inheritAttrs:B,components:R,directives:j,filters:Q}=t;if(c&&$m(c,r,null),s)for(const W in s){const re=s[W];Ee(re)&&(r[W]=re.bind(n))}if(o){const W=o.call(n,n);ot(W)&&(e.data=yr(W))}if(Dl=!0,i)for(const W in i){const re=i[W],ue=Ee(re)?re.bind(n,n):Ee(re.get)?re.get.bind(n,n):Pn,pe=!Ee(re)&&Ee(re.set)?re.set.bind(n):Pn,we=V({get:ue,set:pe});Object.defineProperty(r,W,{enumerable:!0,configurable:!0,get:()=>we.value,set:Te=>we.value=Te})}if(l)for(const W in l)gh(l[W],r,n,W);if(a){const W=Ee(a)?a.call(n):a;Reflect.ownKeys(W).forEach(re=>{ze(re,W[re])})}u&&Fc(u,e,"c");function te(W,re){xe(re)?re.forEach(ue=>W(ue.bind(n))):re&&W(re.bind(n))}if(te(xr,d),te(mt,f),te(hh,m),te(Ia,p),te(uh,g),te(dh,x),te(_m,E),te(wm,S),te(xm,v),te(pt,y),te(zr,b),te(ym,A),xe(F))if(F.length){const W=e.exposed||(e.exposed={});F.forEach(re=>{Object.defineProperty(W,re,{get:()=>n[re],set:ue=>n[re]=ue})})}else e.exposed||(e.exposed={});C&&e.render===Pn&&(e.render=C),B!=null&&(e.inheritAttrs=B),R&&(e.components=R),j&&(e.directives=j),A&&ch(e)}function $m(e,t,n=Pn){xe(e)&&(e=zl(e));for(const r in e){const o=e[r];let i;ot(o)?"default"in o?i=Se(o.from||r,o.default,!0):i=Se(o.from||r):i=Se(o),wt(i)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>i.value,set:s=>i.value=s}):t[r]=i}}function Fc(e,t,n){vn(xe(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,n)}function gh(e,t,n,r){let o=r.includes(".")?Ph(n,r):()=>n[r];if(ft(e)){const i=t[e];Ee(i)&&Ye(o,i)}else if(Ee(e))Ye(o,e.bind(n));else if(ot(e))if(xe(e))e.forEach(i=>gh(i,t,n,r));else{const i=Ee(e.handler)?e.handler.bind(n):t[e.handler];Ee(i)&&Ye(o,i,e)}}function vh(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:o,optionsCache:i,config:{optionMergeStrategies:s}}=e.appContext,l=i.get(t);let a;return l?a=l:!o.length&&!n&&!r?a=t:(a={},o.length&&o.forEach(c=>os(a,c,s,!0)),os(a,t,s)),ot(t)&&i.set(t,a),a}function os(e,t,n,r=!1){const{mixins:o,extends:i}=t;i&&os(e,i,n,!0),o&&o.forEach(s=>os(e,s,n,!0));for(const s in t)if(!(r&&s==="expose")){const l=Pm[s]||n&&n[s];e[s]=l?l(e[s],t[s]):t[s]}return e}const Pm={data:Nc,props:Dc,emits:Dc,methods:Ro,computed:Ro,beforeCreate:Dt,created:Dt,beforeMount:Dt,mounted:Dt,beforeUpdate:Dt,updated:Dt,beforeDestroy:Dt,beforeUnmount:Dt,destroyed:Dt,unmounted:Dt,activated:Dt,deactivated:Dt,errorCaptured:Dt,serverPrefetch:Dt,components:Ro,directives:Ro,watch:km,provide:Nc,inject:Om};function Nc(e,t){return t?e?function(){return Ct(Ee(e)?e.call(this,this):e,Ee(t)?t.call(this,this):t)}:t:e}function Om(e,t){return Ro(zl(e),zl(t))}function zl(e){if(xe(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Dt(e,t){return e?[...new Set([].concat(e,t))]:t}function Ro(e,t){return e?Ct(Object.create(null),e,t):t}function Dc(e,t){return e?xe(e)&&xe(t)?[...new Set([...e,...t])]:Ct(Object.create(null),Mc(e),Mc(t??{})):t}function km(e,t){if(!e)return t;if(!t)return e;const n=Ct(Object.create(null),e);for(const r in t)n[r]=Dt(e[r],t[r]);return n}function mh(){return{app:null,config:{isNativeTag:_v,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Am=0;function Im(e,t){return function(r,o=null){Ee(r)||(r=Ct({},r)),o!=null&&!ot(o)&&(o=null);const i=mh(),s=new WeakSet,l=[];let a=!1;const c=i.app={_uid:Am++,_component:r,_props:o,_container:null,_context:i,_instance:null,version:ub,get config(){return i.config},set config(u){},use(u,...d){return s.has(u)||(u&&Ee(u.install)?(s.add(u),u.install(c,...d)):Ee(u)&&(s.add(u),u(c,...d))),c},mixin(u){return i.mixins.includes(u)||i.mixins.push(u),c},component(u,d){return d?(i.components[u]=d,c):i.components[u]},directive(u,d){return d?(i.directives[u]=d,c):i.directives[u]},mount(u,d,f){if(!a){const m=c._ceVNode||Ge(r,o);return m.appContext=i,f===!0?f="svg":f===!1&&(f=void 0),e(m,u,f),a=!0,c._container=u,u.__vue_app__=c,ks(m.component)}},onUnmount(u){l.push(u)},unmount(){a&&(vn(l,c._instance,16),e(null,c._container),delete c._container.__vue_app__)},provide(u,d){return i.provides[u]=d,c},runWithContext(u){const d=fo;fo=c;try{return u()}finally{fo=d}}};return c}}let fo=null;function ze(e,t){if(Ft){let n=Ft.provides;const r=Ft.parent&&Ft.parent.provides;r===n&&(n=Ft.provides=Object.create(r)),n[e]=t}}function Se(e,t,n=!1){const r=Ft||At;if(r||fo){const o=fo?fo._context.provides:r?r.parent==null?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(o&&e in o)return o[e];if(arguments.length>1)return n&&Ee(t)?t.call(r&&r.proxy):t}}const bh={},yh=()=>Object.create(bh),xh=e=>Object.getPrototypeOf(e)===bh;function Rm(e,t,n,r=!1){const o={},i=yh();e.propsDefaults=Object.create(null),wh(e,t,o,i);for(const s in e.propsOptions[0])s in o||(o[s]=void 0);n?e.props=r?o:nm(o):e.type.props?e.props=o:e.props=i,e.attrs=i}function Lm(e,t,n,r){const{props:o,attrs:i,vnode:{patchFlag:s}}=e,l=Me(o),[a]=e.propsOptions;let c=!1;if((r||s>0)&&!(s&16)){if(s&8){const u=e.vnode.dynamicProps;for(let d=0;d<u.length;d++){let f=u[d];if(Os(e.emitsOptions,f))continue;const m=t[f];if(a)if(je(i,f))m!==i[f]&&(i[f]=m,c=!0);else{const p=dr(f);o[p]=Bl(a,l,p,m,e,!1)}else m!==i[f]&&(i[f]=m,c=!0)}}}else{wh(e,t,o,i)&&(c=!0);let u;for(const d in l)(!t||!je(t,d)&&((u=jr(d))===d||!je(t,u)))&&(a?n&&(n[d]!==void 0||n[u]!==void 0)&&(o[d]=Bl(a,l,d,void 0,e,!0)):delete o[d]);if(i!==l)for(const d in i)(!t||!je(t,d))&&(delete i[d],c=!0)}c&&zn(e.attrs,"set","")}function wh(e,t,n,r){const[o,i]=e.propsOptions;let s=!1,l;if(t)for(let a in t){if(No(a))continue;const c=t[a];let u;o&&je(o,u=dr(a))?!i||!i.includes(u)?n[u]=c:(l||(l={}))[u]=c:Os(e.emitsOptions,a)||(!(a in r)||c!==r[a])&&(r[a]=c,s=!0)}if(i){const a=Me(n),c=l||et;for(let u=0;u<i.length;u++){const d=i[u];n[d]=Bl(o,a,d,c[d],e,!je(c,d))}}return s}function Bl(e,t,n,r,o,i){const s=e[n];if(s!=null){const l=je(s,"default");if(l&&r===void 0){const a=s.default;if(s.type!==Function&&!s.skipFactory&&Ee(a)){const{propsDefaults:c}=o;if(n in c)r=c[n];else{const u=di(o);r=c[n]=a.call(null,t),u()}}else r=a;o.ce&&o.ce._setProp(n,r)}s[0]&&(i&&!l?r=!1:s[1]&&(r===""||r===jr(n))&&(r=!0))}return r}const Mm=new WeakMap;function _h(e,t,n=!1){const r=n?Mm:t.propsCache,o=r.get(e);if(o)return o;const i=e.props,s={},l=[];let a=!1;if(!Ee(e)){const u=d=>{a=!0;const[f,m]=_h(d,t,!0);Ct(s,f),m&&l.push(...m)};!n&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!i&&!a)return ot(e)&&r.set(e,so),so;if(xe(i))for(let u=0;u<i.length;u++){const d=dr(i[u]);zc(d)&&(s[d]=et)}else if(i)for(const u in i){const d=dr(u);if(zc(d)){const f=i[u],m=s[d]=xe(f)||Ee(f)?{type:f}:Ct({},f),p=m.type;let g=!1,x=!0;if(xe(p))for(let h=0;h<p.length;++h){const y=p[h],P=Ee(y)&&y.name;if(P==="Boolean"){g=!0;break}else P==="String"&&(x=!1)}else g=Ee(p)&&p.name==="Boolean";m[0]=g,m[1]=x,(g||je(m,"default"))&&l.push(d)}}const c=[s,l];return ot(e)&&r.set(e,c),c}function zc(e){return e[0]!=="$"&&!No(e)}const Ch=e=>e[0]==="_"||e==="$stable",Ra=e=>xe(e)?e.map($n):[$n(e)],Fm=(e,t,n)=>{if(t._n)return t;const r=ro((...o)=>Ra(t(...o)),n);return r._c=!1,r},Sh=(e,t,n)=>{const r=e._ctx;for(const o in e){if(Ch(o))continue;const i=e[o];if(Ee(i))t[o]=Fm(o,i,r);else if(i!=null){const s=Ra(i);t[o]=()=>s}}},Eh=(e,t)=>{const n=Ra(t);e.slots.default=()=>n},Th=(e,t,n)=>{for(const r in t)(n||r!=="_")&&(e[r]=t[r])},Nm=(e,t,n)=>{const r=e.slots=yh();if(e.vnode.shapeFlag&32){const o=t._;o?(Th(r,t,n),n&&$f(r,"_",o,!0)):Sh(t,r)}else t&&Eh(e,t)},Dm=(e,t,n)=>{const{vnode:r,slots:o}=e;let i=!0,s=et;if(r.shapeFlag&32){const l=t._;l?n&&l===1?i=!1:Th(o,t,n):(i=!t.$stable,Sh(t,o)),s=t}else t&&(Eh(e,t),s={default:1});if(i)for(const l in o)!Ch(l)&&s[l]==null&&delete o[l]},Bt=Jm;function zm(e){return Bm(e)}function Bm(e,t){const n=Cs();n.__VUE__=!0;const{insert:r,remove:o,patchProp:i,createElement:s,createText:l,createComment:a,setText:c,setElementText:u,parentNode:d,nextSibling:f,setScopeId:m=Pn,insertStaticContent:p}=e,g=($,k,z,J=null,K=null,q=null,w=void 0,_=null,O=!!k.dynamicChildren)=>{if($===k)return;$&&!Or($,k)&&(J=me($),Te($,K,q,!0),$=null),k.patchFlag===-2&&(O=!1,k.dynamicChildren=null);const{type:M,ref:ne,shapeFlag:Y}=k;switch(M){case _o:x($,k,z,J);break;case $t:h($,k,z,J);break;case Vi:$==null&&y(k,z,J,w);break;case We:R($,k,z,J,K,q,w,_,O);break;default:Y&1?C($,k,z,J,K,q,w,_,O):Y&6?j($,k,z,J,K,q,w,_,O):(Y&64||Y&128)&&M.process($,k,z,J,K,q,w,_,O,tt)}ne!=null&&K&&rs(ne,$&&$.ref,q,k||$,!k)},x=($,k,z,J)=>{if($==null)r(k.el=l(k.children),z,J);else{const K=k.el=$.el;k.children!==$.children&&c(K,k.children)}},h=($,k,z,J)=>{$==null?r(k.el=a(k.children||""),z,J):k.el=$.el},y=($,k,z,J)=>{[$.el,$.anchor]=p($.children,k,z,J,$.el,$.anchor)},P=({el:$,anchor:k},z,J)=>{let K;for(;$&&$!==k;)K=f($),r($,z,J),$=K;r(k,z,J)},b=({el:$,anchor:k})=>{let z;for(;$&&$!==k;)z=f($),o($),$=z;o(k)},C=($,k,z,J,K,q,w,_,O)=>{k.type==="svg"?w="svg":k.type==="math"&&(w="mathml"),$==null?S(k,z,J,K,q,w,_,O):A($,k,K,q,w,_,O)},S=($,k,z,J,K,q,w,_)=>{let O,M;const{props:ne,shapeFlag:Y,transition:I,dirs:N}=$;if(O=$.el=s($.type,q,ne&&ne.is,ne),Y&8?u(O,$.children):Y&16&&E($.children,O,null,J,K,sl($,q),w,_),N&&Sr($,null,J,"created"),v(O,$,$.scopeId,w,J),ne){for(const de in ne)de!=="value"&&!No(de)&&i(O,de,null,ne[de],q,J);"value"in ne&&i(O,"value",null,ne.value,q),(M=ne.onVnodeBeforeMount)&&wn(M,J,$)}N&&Sr($,null,J,"beforeMount");const ie=Hm(K,I);ie&&I.beforeEnter(O),r(O,k,z),((M=ne&&ne.onVnodeMounted)||ie||N)&&Bt(()=>{M&&wn(M,J,$),ie&&I.enter(O),N&&Sr($,null,J,"mounted")},K)},v=($,k,z,J,K)=>{if(z&&m($,z),J)for(let q=0;q<J.length;q++)m($,J[q]);if(K){let q=K.subTree;if(k===q||kh(q.type)&&(q.ssContent===k||q.ssFallback===k)){const w=K.vnode;v($,w,w.scopeId,w.slotScopeIds,K.parent)}}},E=($,k,z,J,K,q,w,_,O=0)=>{for(let M=O;M<$.length;M++){const ne=$[M]=_?or($[M]):$n($[M]);g(null,ne,k,z,J,K,q,w,_)}},A=($,k,z,J,K,q,w)=>{const _=k.el=$.el;let{patchFlag:O,dynamicChildren:M,dirs:ne}=k;O|=$.patchFlag&16;const Y=$.props||et,I=k.props||et;let N;if(z&&Er(z,!1),(N=I.onVnodeBeforeUpdate)&&wn(N,z,k,$),ne&&Sr(k,$,z,"beforeUpdate"),z&&Er(z,!0),(Y.innerHTML&&I.innerHTML==null||Y.textContent&&I.textContent==null)&&u(_,""),M?F($.dynamicChildren,M,_,z,J,sl(k,K),q):w||re($,k,_,null,z,J,sl(k,K),q,!1),O>0){if(O&16)B(_,Y,I,z,K);else if(O&2&&Y.class!==I.class&&i(_,"class",null,I.class,K),O&4&&i(_,"style",Y.style,I.style,K),O&8){const ie=k.dynamicProps;for(let de=0;de<ie.length;de++){const _e=ie[de],Xe=Y[_e],He=I[_e];(He!==Xe||_e==="value")&&i(_,_e,Xe,He,K,z)}}O&1&&$.children!==k.children&&u(_,k.children)}else!w&&M==null&&B(_,Y,I,z,K);((N=I.onVnodeUpdated)||ne)&&Bt(()=>{N&&wn(N,z,k,$),ne&&Sr(k,$,z,"updated")},J)},F=($,k,z,J,K,q,w)=>{for(let _=0;_<k.length;_++){const O=$[_],M=k[_],ne=O.el&&(O.type===We||!Or(O,M)||O.shapeFlag&70)?d(O.el):z;g(O,M,ne,null,J,K,q,w,!0)}},B=($,k,z,J,K)=>{if(k!==z){if(k!==et)for(const q in k)!No(q)&&!(q in z)&&i($,q,k[q],null,K,J);for(const q in z){if(No(q))continue;const w=z[q],_=k[q];w!==_&&q!=="value"&&i($,q,_,w,K,J)}"value"in z&&i($,"value",k.value,z.value,K)}},R=($,k,z,J,K,q,w,_,O)=>{const M=k.el=$?$.el:l(""),ne=k.anchor=$?$.anchor:l("");let{patchFlag:Y,dynamicChildren:I,slotScopeIds:N}=k;N&&(_=_?_.concat(N):N),$==null?(r(M,z,J),r(ne,z,J),E(k.children||[],z,ne,K,q,w,_,O)):Y>0&&Y&64&&I&&$.dynamicChildren?(F($.dynamicChildren,I,z,K,q,w,_),(k.key!=null||K&&k===K.subTree)&&La($,k,!0)):re($,k,z,ne,K,q,w,_,O)},j=($,k,z,J,K,q,w,_,O)=>{k.slotScopeIds=_,$==null?k.shapeFlag&512?K.ctx.activate(k,z,J,w,O):Q(k,z,J,K,q,w,O):H($,k,O)},Q=($,k,z,J,K,q,w)=>{const _=$.component=ob($,J,K);if($s($)&&(_.ctx.renderer=tt),ib(_,!1,w),_.asyncDep){if(K&&K.registerDep(_,te,w),!$.el){const O=_.subTree=Ge($t);h(null,O,k,z)}}else te(_,$,k,z,K,q,w)},H=($,k,z)=>{const J=k.component=$.component;if(qm($,k,z))if(J.asyncDep&&!J.asyncResolved){W(J,k,z);return}else J.next=k,J.update();else k.el=$.el,J.vnode=k},te=($,k,z,J,K,q,w)=>{const _=()=>{if($.isMounted){let{next:Y,bu:I,u:N,parent:ie,vnode:de}=$;{const vt=$h($);if(vt){Y&&(Y.el=de.el,W($,Y,w)),vt.asyncDep.then(()=>{$.isUnmounted||_()});return}}let _e=Y,Xe;Er($,!1),Y?(Y.el=de.el,W($,Y,w)):Y=de,I&&Qs(I),(Xe=Y.props&&Y.props.onVnodeBeforeUpdate)&&wn(Xe,ie,Y,de),Er($,!0);const He=Hc($),nt=$.subTree;$.subTree=He,g(nt,He,d(nt.el),me(nt),$,K,q),Y.el=He.el,_e===null&&Zm($,He.el),N&&Bt(N,K),(Xe=Y.props&&Y.props.onVnodeUpdated)&&Bt(()=>wn(Xe,ie,Y,de),K)}else{let Y;const{el:I,props:N}=k,{bm:ie,m:de,parent:_e,root:Xe,type:He}=$,nt=uo(k);Er($,!1),ie&&Qs(ie),!nt&&(Y=N&&N.onVnodeBeforeMount)&&wn(Y,_e,k),Er($,!0);{Xe.ce&&Xe.ce._injectChildStyle(He);const vt=$.subTree=Hc($);g(null,vt,z,J,$,K,q),k.el=vt.el}if(de&&Bt(de,K),!nt&&(Y=N&&N.onVnodeMounted)){const vt=k;Bt(()=>wn(Y,_e,vt),K)}(k.shapeFlag&256||_e&&uo(_e.vnode)&&_e.vnode.shapeFlag&256)&&$.a&&Bt($.a,K),$.isMounted=!0,k=z=J=null}};$.scope.on();const O=$.effect=new If(_);$.scope.off();const M=$.update=O.run.bind(O),ne=$.job=O.runIfDirty.bind(O);ne.i=$,ne.id=$.uid,O.scheduler=()=>ka(ne),Er($,!0),M()},W=($,k,z)=>{k.component=$;const J=$.vnode.props;$.vnode=k,$.next=null,Lm($,k.props,J,z),Dm($,k.children,z),mr(),Oc($),br()},re=($,k,z,J,K,q,w,_,O=!1)=>{const M=$&&$.children,ne=$?$.shapeFlag:0,Y=k.children,{patchFlag:I,shapeFlag:N}=k;if(I>0){if(I&128){pe(M,Y,z,J,K,q,w,_,O);return}else if(I&256){ue(M,Y,z,J,K,q,w,_,O);return}}N&8?(ne&16&&be(M,K,q),Y!==M&&u(z,Y)):ne&16?N&16?pe(M,Y,z,J,K,q,w,_,O):be(M,K,q,!0):(ne&8&&u(z,""),N&16&&E(Y,z,J,K,q,w,_,O))},ue=($,k,z,J,K,q,w,_,O)=>{$=$||so,k=k||so;const M=$.length,ne=k.length,Y=Math.min(M,ne);let I;for(I=0;I<Y;I++){const N=k[I]=O?or(k[I]):$n(k[I]);g($[I],N,z,null,K,q,w,_,O)}M>ne?be($,K,q,!0,!1,Y):E(k,z,J,K,q,w,_,O,Y)},pe=($,k,z,J,K,q,w,_,O)=>{let M=0;const ne=k.length;let Y=$.length-1,I=ne-1;for(;M<=Y&&M<=I;){const N=$[M],ie=k[M]=O?or(k[M]):$n(k[M]);if(Or(N,ie))g(N,ie,z,null,K,q,w,_,O);else break;M++}for(;M<=Y&&M<=I;){const N=$[Y],ie=k[I]=O?or(k[I]):$n(k[I]);if(Or(N,ie))g(N,ie,z,null,K,q,w,_,O);else break;Y--,I--}if(M>Y){if(M<=I){const N=I+1,ie=N<ne?k[N].el:J;for(;M<=I;)g(null,k[M]=O?or(k[M]):$n(k[M]),z,ie,K,q,w,_,O),M++}}else if(M>I)for(;M<=Y;)Te($[M],K,q,!0),M++;else{const N=M,ie=M,de=new Map;for(M=ie;M<=I;M++){const bt=k[M]=O?or(k[M]):$n(k[M]);bt.key!=null&&de.set(bt.key,M)}let _e,Xe=0;const He=I-ie+1;let nt=!1,vt=0;const Nt=new Array(He);for(M=0;M<He;M++)Nt[M]=0;for(M=N;M<=Y;M++){const bt=$[M];if(Xe>=He){Te(bt,K,q,!0);continue}let L;if(bt.key!=null)L=de.get(bt.key);else for(_e=ie;_e<=I;_e++)if(Nt[_e-ie]===0&&Or(bt,k[_e])){L=_e;break}L===void 0?Te(bt,K,q,!0):(Nt[L-ie]=M+1,L>=vt?vt=L:nt=!0,g(bt,k[L],z,null,K,q,w,_,O),Xe++)}const Et=nt?jm(Nt):so;for(_e=Et.length-1,M=He-1;M>=0;M--){const bt=ie+M,L=k[bt],ee=bt+1<ne?k[bt+1].el:J;Nt[M]===0?g(null,L,z,ee,K,q,w,_,O):nt&&(_e<0||M!==Et[_e]?we(L,z,ee,2):_e--)}}},we=($,k,z,J,K=null)=>{const{el:q,type:w,transition:_,children:O,shapeFlag:M}=$;if(M&6){we($.component.subTree,k,z,J);return}if(M&128){$.suspense.move(k,z,J);return}if(M&64){w.move($,k,z,tt);return}if(w===We){r(q,k,z);for(let Y=0;Y<O.length;Y++)we(O[Y],k,z,J);r($.anchor,k,z);return}if(w===Vi){P($,k,z);return}if(J!==2&&M&1&&_)if(J===0)_.beforeEnter(q),r(q,k,z),Bt(()=>_.enter(q),K);else{const{leave:Y,delayLeave:I,afterLeave:N}=_,ie=()=>r(q,k,z),de=()=>{Y(q,()=>{ie(),N&&N()})};I?I(q,ie,de):de()}else r(q,k,z)},Te=($,k,z,J=!1,K=!1)=>{const{type:q,props:w,ref:_,children:O,dynamicChildren:M,shapeFlag:ne,patchFlag:Y,dirs:I,cacheIndex:N}=$;if(Y===-2&&(K=!1),_!=null&&rs(_,null,z,$,!0),N!=null&&(k.renderCache[N]=void 0),ne&256){k.ctx.deactivate($);return}const ie=ne&1&&I,de=!uo($);let _e;if(de&&(_e=w&&w.onVnodeBeforeUnmount)&&wn(_e,k,$),ne&6)gt($.component,z,J);else{if(ne&128){$.suspense.unmount(z,J);return}ie&&Sr($,null,k,"beforeUnmount"),ne&64?$.type.remove($,k,z,tt,J):M&&!M.hasOnce&&(q!==We||Y>0&&Y&64)?be(M,k,z,!1,!0):(q===We&&Y&384||!K&&ne&16)&&be(O,k,z),J&&Ue($)}(de&&(_e=w&&w.onVnodeUnmounted)||ie)&&Bt(()=>{_e&&wn(_e,k,$),ie&&Sr($,null,k,"unmounted")},z)},Ue=$=>{const{type:k,el:z,anchor:J,transition:K}=$;if(k===We){st(z,J);return}if(k===Vi){b($);return}const q=()=>{o(z),K&&!K.persisted&&K.afterLeave&&K.afterLeave()};if($.shapeFlag&1&&K&&!K.persisted){const{leave:w,delayLeave:_}=K,O=()=>w(z,q);_?_($.el,q,O):O()}else q()},st=($,k)=>{let z;for(;$!==k;)z=f($),o($),$=z;o(k)},gt=($,k,z)=>{const{bum:J,scope:K,job:q,subTree:w,um:_,m:O,a:M}=$;Bc(O),Bc(M),J&&Qs(J),K.stop(),q&&(q.flags|=8,Te(w,$,k,z)),_&&Bt(_,k),Bt(()=>{$.isUnmounted=!0},k),k&&k.pendingBranch&&!k.isUnmounted&&$.asyncDep&&!$.asyncResolved&&$.suspenseId===k.pendingId&&(k.deps--,k.deps===0&&k.resolve())},be=($,k,z,J=!1,K=!1,q=0)=>{for(let w=q;w<$.length;w++)Te($[w],k,z,J,K)},me=$=>{if($.shapeFlag&6)return me($.component.subTree);if($.shapeFlag&128)return $.suspense.next();const k=f($.anchor||$.el),z=k&&k[eh];return z?f(z):k};let St=!1;const $e=($,k,z)=>{$==null?k._vnode&&Te(k._vnode,null,null,!0):g(k._vnode||null,$,k,null,null,null,z),k._vnode=$,St||(St=!0,Oc(),Zf(),St=!1)},tt={p:g,um:Te,m:we,r:Ue,mt:Q,mc:E,pc:re,pbc:F,n:me,o:e};return{render:$e,hydrate:void 0,createApp:Im($e)}}function sl({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Er({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Hm(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function La(e,t,n=!1){const r=e.children,o=t.children;if(xe(r)&&xe(o))for(let i=0;i<r.length;i++){const s=r[i];let l=o[i];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=o[i]=or(o[i]),l.el=s.el),!n&&l.patchFlag!==-2&&La(s,l)),l.type===_o&&(l.el=s.el)}}function jm(e){const t=e.slice(),n=[0];let r,o,i,s,l;const a=e.length;for(r=0;r<a;r++){const c=e[r];if(c!==0){if(o=n[n.length-1],e[o]<c){t[r]=o,n.push(r);continue}for(i=0,s=n.length-1;i<s;)l=i+s>>1,e[n[l]]<c?i=l+1:s=l;c<e[n[i]]&&(i>0&&(t[r]=n[i-1]),n[i]=r)}}for(i=n.length,s=n[i-1];i-- >0;)n[i]=s,s=t[s];return n}function $h(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:$h(t)}function Bc(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Wm=Symbol.for("v-scx"),Um=()=>Se(Wm);function Wr(e,t){return Ma(e,null,t)}function Ye(e,t,n){return Ma(e,t,n)}function Ma(e,t,n=et){const{immediate:r,deep:o,flush:i,once:s}=n,l=Ct({},n),a=t&&r||!t&&i!=="post";let c;if(Zo){if(i==="sync"){const m=Um();c=m.__watcherHandles||(m.__watcherHandles=[])}else if(!a){const m=()=>{};return m.stop=Pn,m.resume=Pn,m.pause=Pn,m}}const u=Ft;l.call=(m,p,g)=>vn(m,u,p,g);let d=!1;i==="post"?l.scheduler=m=>{Bt(m,u&&u.suspense)}:i!=="sync"&&(d=!0,l.scheduler=(m,p)=>{p?m():ka(m)}),l.augmentJob=m=>{t&&(m.flags|=4),d&&(m.flags|=2,u&&(m.id=u.uid,m.i=u))};const f=dm(e,t,l);return Zo&&(c?c.push(f):a&&f()),f}function Vm(e,t,n){const r=this.proxy,o=ft(e)?e.includes(".")?Ph(r,e):()=>r[e]:e.bind(r,r);let i;Ee(t)?i=t:(i=t.handler,n=t);const s=di(this),l=Ma(o,i.bind(r),n);return s(),l}function Ph(e,t){const n=t.split(".");return()=>{let r=e;for(let o=0;o<n.length&&r;o++)r=r[n[o]];return r}}const Km=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${dr(t)}Modifiers`]||e[`${jr(t)}Modifiers`];function Gm(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||et;let o=n;const i=t.startsWith("update:"),s=i&&Km(r,t.slice(7));s&&(s.trim&&(o=n.map(u=>ft(u)?u.trim():u)),s.number&&(o=n.map($v)));let l,a=r[l=Js(t)]||r[l=Js(dr(t))];!a&&i&&(a=r[l=Js(jr(t))]),a&&vn(a,e,6,o);const c=r[l+"Once"];if(c){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,vn(c,e,6,o)}}function Oh(e,t,n=!1){const r=t.emitsCache,o=r.get(e);if(o!==void 0)return o;const i=e.emits;let s={},l=!1;if(!Ee(e)){const a=c=>{const u=Oh(c,t,!0);u&&(l=!0,Ct(s,u))};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}return!i&&!l?(ot(e)&&r.set(e,null),null):(xe(i)?i.forEach(a=>s[a]=null):Ct(s,i),ot(e)&&r.set(e,s),s)}function Os(e,t){return!e||!xs(t)?!1:(t=t.slice(2).replace(/Once$/,""),je(e,t[0].toLowerCase()+t.slice(1))||je(e,jr(t))||je(e,t))}function Hc(e){const{type:t,vnode:n,proxy:r,withProxy:o,propsOptions:[i],slots:s,attrs:l,emit:a,render:c,renderCache:u,props:d,data:f,setupState:m,ctx:p,inheritAttrs:g}=e,x=ns(e);let h,y;try{if(n.shapeFlag&4){const b=o||r,C=b;h=$n(c.call(C,b,u,d,m,f,p)),y=l}else{const b=t;h=$n(b.length>1?b(d,{attrs:l,slots:s,emit:a}):b(d,null)),y=t.props?l:Ym(l)}}catch(b){jo.length=0,Es(b,e,1),h=Ge($t)}let P=h;if(y&&g!==!1){const b=Object.keys(y),{shapeFlag:C}=P;b.length&&C&7&&(i&&b.some(xa)&&(y=Xm(y,i)),P=cn(P,y,!1,!0))}return n.dirs&&(P=cn(P,null,!1,!0),P.dirs=P.dirs?P.dirs.concat(n.dirs):n.dirs),n.transition&&Dr(P,n.transition),h=P,ns(x),h}const Ym=e=>{let t;for(const n in e)(n==="class"||n==="style"||xs(n))&&((t||(t={}))[n]=e[n]);return t},Xm=(e,t)=>{const n={};for(const r in e)(!xa(r)||!(r.slice(9)in t))&&(n[r]=e[r]);return n};function qm(e,t,n){const{props:r,children:o,component:i}=e,{props:s,children:l,patchFlag:a}=t,c=i.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&a>=0){if(a&1024)return!0;if(a&16)return r?jc(r,s,c):!!s;if(a&8){const u=t.dynamicProps;for(let d=0;d<u.length;d++){const f=u[d];if(s[f]!==r[f]&&!Os(c,f))return!0}}}else return(o||l)&&(!l||!l.$stable)?!0:r===s?!1:r?s?jc(r,s,c):!0:!!s;return!1}function jc(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let o=0;o<r.length;o++){const i=r[o];if(t[i]!==e[i]&&!Os(n,i))return!0}return!1}function Zm({vnode:e,parent:t},n){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=n,t=t.parent;else break}}const kh=e=>e.__isSuspense;function Jm(e,t){t&&t.pendingBranch?xe(e)?t.effects.push(...e):t.effects.push(e):pm(e)}const We=Symbol.for("v-fgt"),_o=Symbol.for("v-txt"),$t=Symbol.for("v-cmt"),Vi=Symbol.for("v-stc"),jo=[];let Qt=null;function ye(e=!1){jo.push(Qt=e?null:[])}function Qm(){jo.pop(),Qt=jo[jo.length-1]||null}let qo=1;function Wc(e,t=!1){qo+=e,e<0&&Qt&&t&&(Qt.hasOnce=!0)}function Ah(e){return e.dynamicChildren=qo>0?Qt||so:null,Qm(),qo>0&&Qt&&Qt.push(e),e}function Ae(e,t,n,r,o,i){return Ah(ae(e,t,n,r,o,i,!0))}function sr(e,t,n,r,o){return Ah(Ge(e,t,n,r,o,!0))}function go(e){return e?e.__v_isVNode===!0:!1}function Or(e,t){return e.type===t.type&&e.key===t.key}const Ih=({key:e})=>e??null,Ki=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?ft(e)||wt(e)||Ee(e)?{i:At,r:e,k:t,f:!!n}:e:null);function ae(e,t=null,n=null,r=0,o=null,i=e===We?0:1,s=!1,l=!1){const a={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Ih(t),ref:t&&Ki(t),scopeId:Qf,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:r,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:At};return l?(Fa(a,n),i&128&&e.normalize(a)):n&&(a.shapeFlag|=ft(n)?8:16),qo>0&&!s&&Qt&&(a.patchFlag>0||i&6)&&a.patchFlag!==32&&Qt.push(a),a}const Ge=eb;function eb(e,t=null,n=null,r=0,o=null,i=!1){if((!e||e===Cm)&&(e=$t),go(e)){const l=cn(e,t,!0);return n&&Fa(l,n),qo>0&&!i&&Qt&&(l.shapeFlag&6?Qt[Qt.indexOf(e)]=l:Qt.push(l)),l.patchFlag=-2,l}if(cb(e)&&(e=e.__vccOpts),t){t=tb(t);let{class:l,style:a}=t;l&&!ft(l)&&(t.class=Wn(l)),ot(a)&&(Oa(a)&&!xe(a)&&(a=Ct({},a)),t.style=Tt(a))}const s=ft(e)?1:kh(e)?128:th(e)?64:ot(e)?4:Ee(e)?2:0;return ae(e,t,n,r,o,s,i,!0)}function tb(e){return e?Oa(e)||xh(e)?Ct({},e):e:null}function cn(e,t,n=!1,r=!1){const{props:o,ref:i,patchFlag:s,children:l,transition:a}=e,c=t?Ur(o||{},t):o,u={__v_isVNode:!0,__v_skip:!0,type:e.type,props:c,key:c&&Ih(c),ref:t&&t.ref?n&&i?xe(i)?i.concat(Ki(t)):[i,Ki(t)]:Ki(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==We?s===-1?16:s|16:s,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:a,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&cn(e.ssContent),ssFallback:e.ssFallback&&cn(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return a&&r&&Dr(u,a.clone(u)),u}function vo(e=" ",t=0){return Ge(_o,null,e,t)}function Rh(e,t){const n=Ge(Vi,null,e);return n.staticCount=t,n}function Kt(e="",t=!1){return t?(ye(),sr($t,null,e)):Ge($t,null,e)}function $n(e){return e==null||typeof e=="boolean"?Ge($t):xe(e)?Ge(We,null,e.slice()):go(e)?or(e):Ge(_o,null,String(e))}function or(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:cn(e)}function Fa(e,t){let n=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(xe(t))n=16;else if(typeof t=="object")if(r&65){const o=t.default;o&&(o._c&&(o._d=!1),Fa(e,o()),o._c&&(o._d=!0));return}else{n=32;const o=t._;!o&&!xh(t)?t._ctx=At:o===3&&At&&(At.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else Ee(t)?(t={default:t,_ctx:At},n=32):(t=String(t),r&64?(n=16,t=[vo(t)]):n=8);e.children=t,e.shapeFlag|=n}function Ur(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const o in r)if(o==="class")t.class!==r.class&&(t.class=Wn([t.class,r.class]));else if(o==="style")t.style=Tt([t.style,r.style]);else if(xs(o)){const i=t[o],s=r[o];s&&i!==s&&!(xe(i)&&i.includes(s))&&(t[o]=i?[].concat(i,s):s)}else o!==""&&(t[o]=r[o])}return t}function wn(e,t,n,r=null){vn(e,t,7,[n,r])}const nb=mh();let rb=0;function ob(e,t,n){const r=e.type,o=(t?t.appContext:e.appContext)||nb,i={uid:rb++,vnode:e,type:r,parent:t,appContext:o,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Af(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(o.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:_h(r,o),emitsOptions:Oh(r,o),emit:null,emitted:null,propsDefaults:et,inheritAttrs:r.inheritAttrs,ctx:et,data:et,props:et,attrs:et,slots:et,refs:et,setupState:et,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=Gm.bind(null,i),e.ce&&e.ce(i),i}let Ft=null;const In=()=>Ft||At;let is,Hl;{const e=Cs(),t=(n,r)=>{let o;return(o=e[n])||(o=e[n]=[]),o.push(r),i=>{o.length>1?o.forEach(s=>s(i)):o[0](i)}};is=t("__VUE_INSTANCE_SETTERS__",n=>Ft=n),Hl=t("__VUE_SSR_SETTERS__",n=>Zo=n)}const di=e=>{const t=Ft;return is(e),e.scope.on(),()=>{e.scope.off(),is(t)}},Uc=()=>{Ft&&Ft.scope.off(),is(null)};function Lh(e){return e.vnode.shapeFlag&4}let Zo=!1;function ib(e,t=!1,n=!1){t&&Hl(t);const{props:r,children:o}=e.vnode,i=Lh(e);Rm(e,r,i,t),Nm(e,o,n);const s=i?sb(e,t):void 0;return t&&Hl(!1),s}function sb(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Em);const{setup:r}=n;if(r){mr();const o=e.setupContext=r.length>1?ab(e):null,i=di(e),s=ui(r,e,0,[e.props,o]),l=Cf(s);if(br(),i(),(l||e.sp)&&!uo(e)&&ch(e),l){if(s.then(Uc,Uc),t)return s.then(a=>{Vc(e,a)}).catch(a=>{Es(a,e,0)});e.asyncDep=s}else Vc(e,s)}else Mh(e)}function Vc(e,t,n){Ee(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ot(t)&&(e.setupState=Yf(t)),Mh(e)}function Mh(e,t,n){const r=e.type;e.render||(e.render=r.render||Pn);{const o=di(e);mr();try{Tm(e)}finally{br(),o()}}}const lb={get(e,t){return Rt(e,"get",""),e[t]}};function ab(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,lb),slots:e.slots,emit:e.emit,expose:t}}function ks(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Yf(Ll(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Ho)return Ho[n](e)},has(t,n){return n in t||n in Ho}})):e.proxy}function cb(e){return Ee(e)&&"__vccOpts"in e}const V=(e,t)=>cm(e,t,Zo);function T(e,t,n){const r=arguments.length;return r===2?ot(t)&&!xe(t)?go(t)?Ge(e,null,[t]):Ge(e,t):Ge(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):r===3&&go(n)&&(n=[n]),Ge(e,t,n))}const ub="3.5.13";/**
* @vue/runtime-dom v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let jl;const Kc=typeof window<"u"&&window.trustedTypes;if(Kc)try{jl=Kc.createPolicy("vue",{createHTML:e=>e})}catch{}const Fh=jl?e=>jl.createHTML(e):e=>e,db="http://www.w3.org/2000/svg",fb="http://www.w3.org/1998/Math/MathML",Dn=typeof document<"u"?document:null,Gc=Dn&&Dn.createElement("template"),hb={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const o=t==="svg"?Dn.createElementNS(db,e):t==="mathml"?Dn.createElementNS(fb,e):n?Dn.createElement(e,{is:n}):Dn.createElement(e);return e==="select"&&r&&r.multiple!=null&&o.setAttribute("multiple",r.multiple),o},createText:e=>Dn.createTextNode(e),createComment:e=>Dn.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Dn.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,o,i){const s=n?n.previousSibling:t.lastChild;if(o&&(o===i||o.nextSibling))for(;t.insertBefore(o.cloneNode(!0),n),!(o===i||!(o=o.nextSibling)););else{Gc.innerHTML=Fh(r==="svg"?`<svg>${e}</svg>`:r==="mathml"?`<math>${e}</math>`:e);const l=Gc.content;if(r==="svg"||r==="mathml"){const a=l.firstChild;for(;a.firstChild;)l.appendChild(a.firstChild);l.removeChild(a)}t.insertBefore(l,n)}return[s?s.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Zn="transition",$o="animation",mo=Symbol("_vtc"),Nh={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},Dh=Ct({},ih,Nh),pb=e=>(e.displayName="Transition",e.props=Dh,e),mn=pb((e,{slots:t})=>T(mm,zh(e),t)),Tr=(e,t=[])=>{xe(e)?e.forEach(n=>n(...t)):e&&e(...t)},Yc=e=>e?xe(e)?e.some(t=>t.length>1):e.length>1:!1;function zh(e){const t={};for(const R in e)R in Nh||(t[R]=e[R]);if(e.css===!1)return t;const{name:n="v",type:r,duration:o,enterFromClass:i=`${n}-enter-from`,enterActiveClass:s=`${n}-enter-active`,enterToClass:l=`${n}-enter-to`,appearFromClass:a=i,appearActiveClass:c=s,appearToClass:u=l,leaveFromClass:d=`${n}-leave-from`,leaveActiveClass:f=`${n}-leave-active`,leaveToClass:m=`${n}-leave-to`}=e,p=gb(o),g=p&&p[0],x=p&&p[1],{onBeforeEnter:h,onEnter:y,onEnterCancelled:P,onLeave:b,onLeaveCancelled:C,onBeforeAppear:S=h,onAppear:v=y,onAppearCancelled:E=P}=t,A=(R,j,Q,H)=>{R._enterCancelled=H,tr(R,j?u:l),tr(R,j?c:s),Q&&Q()},F=(R,j)=>{R._isLeaving=!1,tr(R,d),tr(R,m),tr(R,f),j&&j()},B=R=>(j,Q)=>{const H=R?v:y,te=()=>A(j,R,Q);Tr(H,[j,te]),Xc(()=>{tr(j,R?a:i),En(j,R?u:l),Yc(H)||qc(j,r,g,te)})};return Ct(t,{onBeforeEnter(R){Tr(h,[R]),En(R,i),En(R,s)},onBeforeAppear(R){Tr(S,[R]),En(R,a),En(R,c)},onEnter:B(!1),onAppear:B(!0),onLeave(R,j){R._isLeaving=!0;const Q=()=>F(R,j);En(R,d),R._enterCancelled?(En(R,f),Wl()):(Wl(),En(R,f)),Xc(()=>{R._isLeaving&&(tr(R,d),En(R,m),Yc(b)||qc(R,r,x,Q))}),Tr(b,[R,Q])},onEnterCancelled(R){A(R,!1,void 0,!0),Tr(P,[R])},onAppearCancelled(R){A(R,!0,void 0,!0),Tr(E,[R])},onLeaveCancelled(R){F(R),Tr(C,[R])}})}function gb(e){if(e==null)return null;if(ot(e))return[ll(e.enter),ll(e.leave)];{const t=ll(e);return[t,t]}}function ll(e){return Pv(e)}function En(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[mo]||(e[mo]=new Set)).add(t)}function tr(e,t){t.split(/\s+/).forEach(r=>r&&e.classList.remove(r));const n=e[mo];n&&(n.delete(t),n.size||(e[mo]=void 0))}function Xc(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let vb=0;function qc(e,t,n,r){const o=e._endId=++vb,i=()=>{o===e._endId&&r()};if(n!=null)return setTimeout(i,n);const{type:s,timeout:l,propCount:a}=Bh(e,t);if(!s)return r();const c=s+"end";let u=0;const d=()=>{e.removeEventListener(c,f),i()},f=m=>{m.target===e&&++u>=a&&d()};setTimeout(()=>{u<a&&d()},l+1),e.addEventListener(c,f)}function Bh(e,t){const n=window.getComputedStyle(e),r=p=>(n[p]||"").split(", "),o=r(`${Zn}Delay`),i=r(`${Zn}Duration`),s=Zc(o,i),l=r(`${$o}Delay`),a=r(`${$o}Duration`),c=Zc(l,a);let u=null,d=0,f=0;t===Zn?s>0&&(u=Zn,d=s,f=i.length):t===$o?c>0&&(u=$o,d=c,f=a.length):(d=Math.max(s,c),u=d>0?s>c?Zn:$o:null,f=u?u===Zn?i.length:a.length:0);const m=u===Zn&&/\b(transform|all)(,|$)/.test(r(`${Zn}Property`).toString());return{type:u,timeout:d,propCount:f,hasTransform:m}}function Zc(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,r)=>Jc(n)+Jc(e[r])))}function Jc(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function Wl(){return document.body.offsetHeight}function mb(e,t,n){const r=e[mo];r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const ss=Symbol("_vod"),Hh=Symbol("_vsh"),Jo={beforeMount(e,{value:t},{transition:n}){e[ss]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):Po(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:r}){!t!=!n&&(r?t?(r.beforeEnter(e),Po(e,!0),r.enter(e)):r.leave(e,()=>{Po(e,!1)}):Po(e,t))},beforeUnmount(e,{value:t}){Po(e,t)}};function Po(e,t){e.style.display=t?e[ss]:"none",e[Hh]=!t}const bb=Symbol(""),yb=/(^|;)\s*display\s*:/;function xb(e,t,n){const r=e.style,o=ft(n);let i=!1;if(n&&!o){if(t)if(ft(t))for(const s of t.split(";")){const l=s.slice(0,s.indexOf(":")).trim();n[l]==null&&Gi(r,l,"")}else for(const s in t)n[s]==null&&Gi(r,s,"");for(const s in n)s==="display"&&(i=!0),Gi(r,s,n[s])}else if(o){if(t!==n){const s=r[bb];s&&(n+=";"+s),r.cssText=n,i=yb.test(n)}}else t&&e.removeAttribute("style");ss in e&&(e[ss]=i?r.display:"",e[Hh]&&(r.display="none"))}const Qc=/\s*!important$/;function Gi(e,t,n){if(xe(n))n.forEach(r=>Gi(e,t,r));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const r=wb(e,t);Qc.test(n)?e.setProperty(jr(r),n.replace(Qc,""),"important"):e[r]=n}}const eu=["Webkit","Moz","ms"],al={};function wb(e,t){const n=al[t];if(n)return n;let r=dr(t);if(r!=="filter"&&r in e)return al[t]=r;r=Tf(r);for(let o=0;o<eu.length;o++){const i=eu[o]+r;if(i in e)return al[t]=i}return t}const tu="http://www.w3.org/1999/xlink";function nu(e,t,n,r,o,i=Lv(t)){r&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(tu,t.slice(6,t.length)):e.setAttributeNS(tu,t,n):n==null||i&&!Pf(n)?e.removeAttribute(t):e.setAttribute(t,i?"":Kn(n)?String(n):n)}function ru(e,t,n,r,o){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Fh(n):n);return}const i=e.tagName;if(t==="value"&&i!=="PROGRESS"&&!i.includes("-")){const l=i==="OPTION"?e.getAttribute("value")||"":e.value,a=n==null?e.type==="checkbox"?"on":"":String(n);(l!==a||!("_value"in e))&&(e.value=a),n==null&&e.removeAttribute(t),e._value=n;return}let s=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=Pf(n):n==null&&l==="string"?(n="",s=!0):l==="number"&&(n=0,s=!0)}try{e[t]=n}catch{}s&&e.removeAttribute(o||t)}function _b(e,t,n,r){e.addEventListener(t,n,r)}function Cb(e,t,n,r){e.removeEventListener(t,n,r)}const ou=Symbol("_vei");function Sb(e,t,n,r,o=null){const i=e[ou]||(e[ou]={}),s=i[t];if(r&&s)s.value=r;else{const[l,a]=Eb(t);if(r){const c=i[t]=Pb(r,o);_b(e,l,c,a)}else s&&(Cb(e,l,s,a),i[t]=void 0)}}const iu=/(?:Once|Passive|Capture)$/;function Eb(e){let t;if(iu.test(e)){t={};let r;for(;r=e.match(iu);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):jr(e.slice(2)),t]}let cl=0;const Tb=Promise.resolve(),$b=()=>cl||(Tb.then(()=>cl=0),cl=Date.now());function Pb(e,t){const n=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=n.attached)return;vn(Ob(r,n.value),t,5,[r])};return n.value=e,n.attached=$b(),n}function Ob(e,t){if(xe(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(r=>o=>!o._stopped&&r&&r(o))}else return t}const su=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,kb=(e,t,n,r,o,i)=>{const s=o==="svg";t==="class"?mb(e,r,s):t==="style"?xb(e,n,r):xs(t)?xa(t)||Sb(e,t,n,r,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Ab(e,t,r,s))?(ru(e,t,r),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&nu(e,t,r,s,i,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!ft(r))?ru(e,dr(t),r,i,t):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),nu(e,t,r,s))};function Ab(e,t,n,r){if(r)return!!(t==="innerHTML"||t==="textContent"||t in e&&su(t)&&Ee(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const o=e.tagName;if(o==="IMG"||o==="VIDEO"||o==="CANVAS"||o==="SOURCE")return!1}return su(t)&&ft(n)?!1:t in e}const jh=new WeakMap,Wh=new WeakMap,ls=Symbol("_moveCb"),lu=Symbol("_enterCb"),Ib=e=>(delete e.props.mode,e),Rb=Ib({name:"TransitionGroup",props:Ct({},Dh,{tag:String,moveClass:String}),setup(e,{slots:t}){const n=In(),r=oh();let o,i;return Ia(()=>{if(!o.length)return;const s=e.moveClass||`${e.name||"v"}-move`;if(!Db(o[0].el,n.vnode.el,s))return;o.forEach(Mb),o.forEach(Fb);const l=o.filter(Nb);Wl(),l.forEach(a=>{const c=a.el,u=c.style;En(c,s),u.transform=u.webkitTransform=u.transitionDuration="";const d=c[ls]=f=>{f&&f.target!==c||(!f||/transform$/.test(f.propertyName))&&(c.removeEventListener("transitionend",d),c[ls]=null,tr(c,s))};c.addEventListener("transitionend",d)})}),()=>{const s=Me(e),l=zh(s);let a=s.tag||We;if(o=[],i)for(let c=0;c<i.length;c++){const u=i[c];u.el&&u.el instanceof Element&&(o.push(u),Dr(u,Xo(u,l,r,n)),jh.set(u,u.el.getBoundingClientRect()))}i=t.default?Aa(t.default()):[];for(let c=0;c<i.length;c++){const u=i[c];u.key!=null&&Dr(u,Xo(u,l,r,n))}return Ge(a,null,i)}}}),Lb=Rb;function Mb(e){const t=e.el;t[ls]&&t[ls](),t[lu]&&t[lu]()}function Fb(e){Wh.set(e,e.el.getBoundingClientRect())}function Nb(e){const t=jh.get(e),n=Wh.get(e),r=t.left-n.left,o=t.top-n.top;if(r||o){const i=e.el.style;return i.transform=i.webkitTransform=`translate(${r}px,${o}px)`,i.transitionDuration="0s",e}}function Db(e,t,n){const r=e.cloneNode(),o=e[mo];o&&o.forEach(l=>{l.split(/\s+/).forEach(a=>a&&r.classList.remove(a))}),n.split(/\s+/).forEach(l=>l&&r.classList.add(l)),r.style.display="none";const i=t.nodeType===1?t:t.parentNode;i.appendChild(r);const{hasTransform:s}=Bh(r);return i.removeChild(r),s}const zb=Ct({patchProp:kb},hb);let au;function Bb(){return au||(au=zm(zb))}const Uh=(...e)=>{const t=Bb().createApp(...e),{mount:n}=t;return t.mount=r=>{const o=jb(r);if(!o)return;const i=t._component;!Ee(i)&&!i.render&&!i.template&&(i.template=o.innerHTML),o.nodeType===1&&(o.textContent="");const s=n(o,!1,Hb(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),s},t};function Hb(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function jb(e){return ft(e)?document.querySelector(e):e}/*!
  * shared v10.0.7
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */const as=typeof window<"u",wr=(e,t=!1)=>t?Symbol.for(e):Symbol(e),Wb=(e,t,n)=>Ub({l:e,k:t,s:n}),Ub=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),ht=e=>typeof e=="number"&&isFinite(e),Vb=e=>Na(e)==="[object Date]",bo=e=>Na(e)==="[object RegExp]",As=e=>Ie(e)&&Object.keys(e).length===0,_t=Object.assign,Kb=Object.create,qe=(e=null)=>Kb(e);let cu;const kr=()=>cu||(cu=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:qe());function uu(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const Gb=Object.prototype.hasOwnProperty;function pn(e,t){return Gb.call(e,t)}const dt=Array.isArray,ct=e=>typeof e=="function",le=e=>typeof e=="string",Fe=e=>typeof e=="boolean",Be=e=>e!==null&&typeof e=="object",Yb=e=>Be(e)&&ct(e.then)&&ct(e.catch),Vh=Object.prototype.toString,Na=e=>Vh.call(e),Ie=e=>Na(e)==="[object Object]",Xb=e=>e==null?"":dt(e)||Ie(e)&&e.toString===Vh?JSON.stringify(e,null,2):String(e);function Da(e,t=""){return e.reduce((n,r,o)=>o===0?n+r:n+t+r,"")}function qb(e,t){typeof console<"u"&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const Ei=e=>!Be(e)||dt(e);function Yi(e,t){if(Ei(e)||Ei(t))throw new Error("Invalid value");const n=[{src:e,des:t}];for(;n.length;){const{src:r,des:o}=n.pop();Object.keys(r).forEach(i=>{i!=="__proto__"&&(Be(r[i])&&!Be(o[i])&&(o[i]=Array.isArray(r[i])?[]:qe()),Ei(o[i])||Ei(r[i])?o[i]=r[i]:n.push({src:r[i],des:o[i]}))})}}/*!
  * message-compiler v10.0.7
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */function Zb(e,t,n){return{line:e,column:t,offset:n}}function Ul(e,t,n){return{start:e,end:t}}const Ke={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14},Jb=17;function Is(e,t,n={}){const{domain:r,messages:o,args:i}=n,s=e,l=new SyntaxError(String(s));return l.code=e,t&&(l.location=t),l.domain=r,l}function Qb(e){throw e}const Fn=" ",e0="\r",zt=`
`,t0="\u2028",n0="\u2029";function r0(e){const t=e;let n=0,r=1,o=1,i=0;const s=v=>t[v]===e0&&t[v+1]===zt,l=v=>t[v]===zt,a=v=>t[v]===n0,c=v=>t[v]===t0,u=v=>s(v)||l(v)||a(v)||c(v),d=()=>n,f=()=>r,m=()=>o,p=()=>i,g=v=>s(v)||a(v)||c(v)?zt:t[v],x=()=>g(n),h=()=>g(n+i);function y(){return i=0,u(n)&&(r++,o=0),s(n)&&n++,n++,o++,t[n]}function P(){return s(n+i)&&i++,i++,t[n+i]}function b(){n=0,r=1,o=1,i=0}function C(v=0){i=v}function S(){const v=n+i;for(;v!==n;)y();i=0}return{index:d,line:f,column:m,peekOffset:p,charAt:g,currentChar:x,currentPeek:h,next:y,peek:P,reset:b,resetPeek:C,skipToPeek:S}}const Jn=void 0,o0=".",du="'",i0="tokenizer";function s0(e,t={}){const n=t.location!==!1,r=r0(e),o=()=>r.index(),i=()=>Zb(r.line(),r.column(),r.index()),s=i(),l=o(),a={currentType:13,offset:l,startLoc:s,endLoc:s,lastType:13,lastOffset:l,lastStartLoc:s,lastEndLoc:s,braceNest:0,inLinked:!1,text:""},c=()=>a,{onError:u}=t;function d(w,_,O,...M){const ne=c();if(_.column+=O,_.offset+=O,u){const Y=n?Ul(ne.startLoc,_):null,I=Is(w,Y,{domain:i0,args:M});u(I)}}function f(w,_,O){w.endLoc=i(),w.currentType=_;const M={type:_};return n&&(M.loc=Ul(w.startLoc,w.endLoc)),O!=null&&(M.value=O),M}const m=w=>f(w,13);function p(w,_){return w.currentChar()===_?(w.next(),_):(d(Ke.EXPECTED_TOKEN,i(),0,_),"")}function g(w){let _="";for(;w.currentPeek()===Fn||w.currentPeek()===zt;)_+=w.currentPeek(),w.peek();return _}function x(w){const _=g(w);return w.skipToPeek(),_}function h(w){if(w===Jn)return!1;const _=w.charCodeAt(0);return _>=97&&_<=122||_>=65&&_<=90||_===95}function y(w){if(w===Jn)return!1;const _=w.charCodeAt(0);return _>=48&&_<=57}function P(w,_){const{currentType:O}=_;if(O!==2)return!1;g(w);const M=h(w.currentPeek());return w.resetPeek(),M}function b(w,_){const{currentType:O}=_;if(O!==2)return!1;g(w);const M=w.currentPeek()==="-"?w.peek():w.currentPeek(),ne=y(M);return w.resetPeek(),ne}function C(w,_){const{currentType:O}=_;if(O!==2)return!1;g(w);const M=w.currentPeek()===du;return w.resetPeek(),M}function S(w,_){const{currentType:O}=_;if(O!==7)return!1;g(w);const M=w.currentPeek()===".";return w.resetPeek(),M}function v(w,_){const{currentType:O}=_;if(O!==8)return!1;g(w);const M=h(w.currentPeek());return w.resetPeek(),M}function E(w,_){const{currentType:O}=_;if(!(O===7||O===11))return!1;g(w);const M=w.currentPeek()===":";return w.resetPeek(),M}function A(w,_){const{currentType:O}=_;if(O!==9)return!1;const M=()=>{const Y=w.currentPeek();return Y==="{"?h(w.peek()):Y==="@"||Y==="|"||Y===":"||Y==="."||Y===Fn||!Y?!1:Y===zt?(w.peek(),M()):B(w,!1)},ne=M();return w.resetPeek(),ne}function F(w){g(w);const _=w.currentPeek()==="|";return w.resetPeek(),_}function B(w,_=!0){const O=(ne=!1,Y="")=>{const I=w.currentPeek();return I==="{"||I==="@"||!I?ne:I==="|"?!(Y===Fn||Y===zt):I===Fn?(w.peek(),O(!0,Fn)):I===zt?(w.peek(),O(!0,zt)):!0},M=O();return _&&w.resetPeek(),M}function R(w,_){const O=w.currentChar();return O===Jn?Jn:_(O)?(w.next(),O):null}function j(w){const _=w.charCodeAt(0);return _>=97&&_<=122||_>=65&&_<=90||_>=48&&_<=57||_===95||_===36}function Q(w){return R(w,j)}function H(w){const _=w.charCodeAt(0);return _>=97&&_<=122||_>=65&&_<=90||_>=48&&_<=57||_===95||_===36||_===45}function te(w){return R(w,H)}function W(w){const _=w.charCodeAt(0);return _>=48&&_<=57}function re(w){return R(w,W)}function ue(w){const _=w.charCodeAt(0);return _>=48&&_<=57||_>=65&&_<=70||_>=97&&_<=102}function pe(w){return R(w,ue)}function we(w){let _="",O="";for(;_=re(w);)O+=_;return O}function Te(w){let _="";for(;;){const O=w.currentChar();if(O==="{"||O==="}"||O==="@"||O==="|"||!O)break;if(O===Fn||O===zt)if(B(w))_+=O,w.next();else{if(F(w))break;_+=O,w.next()}else _+=O,w.next()}return _}function Ue(w){x(w);let _="",O="";for(;_=te(w);)O+=_;return w.currentChar()===Jn&&d(Ke.UNTERMINATED_CLOSING_BRACE,i(),0),O}function st(w){x(w);let _="";return w.currentChar()==="-"?(w.next(),_+=`-${we(w)}`):_+=we(w),w.currentChar()===Jn&&d(Ke.UNTERMINATED_CLOSING_BRACE,i(),0),_}function gt(w){return w!==du&&w!==zt}function be(w){x(w),p(w,"'");let _="",O="";for(;_=R(w,gt);)_==="\\"?O+=me(w):O+=_;const M=w.currentChar();return M===zt||M===Jn?(d(Ke.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,i(),0),M===zt&&(w.next(),p(w,"'")),O):(p(w,"'"),O)}function me(w){const _=w.currentChar();switch(_){case"\\":case"'":return w.next(),`\\${_}`;case"u":return St(w,_,4);case"U":return St(w,_,6);default:return d(Ke.UNKNOWN_ESCAPE_SEQUENCE,i(),0,_),""}}function St(w,_,O){p(w,_);let M="";for(let ne=0;ne<O;ne++){const Y=pe(w);if(!Y){d(Ke.INVALID_UNICODE_ESCAPE_SEQUENCE,i(),0,`\\${_}${M}${w.currentChar()}`);break}M+=Y}return`\\${_}${M}`}function $e(w){return w!=="{"&&w!=="}"&&w!==Fn&&w!==zt}function tt(w){x(w);let _="",O="";for(;_=R(w,$e);)O+=_;return O}function Ot(w){let _="",O="";for(;_=Q(w);)O+=_;return O}function $(w){const _=O=>{const M=w.currentChar();return M==="{"||M==="@"||M==="|"||M==="("||M===")"||!M||M===Fn?O:(O+=M,w.next(),_(O))};return _("")}function k(w){x(w);const _=p(w,"|");return x(w),_}function z(w,_){let O=null;switch(w.currentChar()){case"{":return _.braceNest>=1&&d(Ke.NOT_ALLOW_NEST_PLACEHOLDER,i(),0),w.next(),O=f(_,2,"{"),x(w),_.braceNest++,O;case"}":return _.braceNest>0&&_.currentType===2&&d(Ke.EMPTY_PLACEHOLDER,i(),0),w.next(),O=f(_,3,"}"),_.braceNest--,_.braceNest>0&&x(w),_.inLinked&&_.braceNest===0&&(_.inLinked=!1),O;case"@":return _.braceNest>0&&d(Ke.UNTERMINATED_CLOSING_BRACE,i(),0),O=J(w,_)||m(_),_.braceNest=0,O;default:{let ne=!0,Y=!0,I=!0;if(F(w))return _.braceNest>0&&d(Ke.UNTERMINATED_CLOSING_BRACE,i(),0),O=f(_,1,k(w)),_.braceNest=0,_.inLinked=!1,O;if(_.braceNest>0&&(_.currentType===4||_.currentType===5||_.currentType===6))return d(Ke.UNTERMINATED_CLOSING_BRACE,i(),0),_.braceNest=0,K(w,_);if(ne=P(w,_))return O=f(_,4,Ue(w)),x(w),O;if(Y=b(w,_))return O=f(_,5,st(w)),x(w),O;if(I=C(w,_))return O=f(_,6,be(w)),x(w),O;if(!ne&&!Y&&!I)return O=f(_,12,tt(w)),d(Ke.INVALID_TOKEN_IN_PLACEHOLDER,i(),0,O.value),x(w),O;break}}return O}function J(w,_){const{currentType:O}=_;let M=null;const ne=w.currentChar();switch((O===7||O===8||O===11||O===9)&&(ne===zt||ne===Fn)&&d(Ke.INVALID_LINKED_FORMAT,i(),0),ne){case"@":return w.next(),M=f(_,7,"@"),_.inLinked=!0,M;case".":return x(w),w.next(),f(_,8,".");case":":return x(w),w.next(),f(_,9,":");default:return F(w)?(M=f(_,1,k(w)),_.braceNest=0,_.inLinked=!1,M):S(w,_)||E(w,_)?(x(w),J(w,_)):v(w,_)?(x(w),f(_,11,Ot(w))):A(w,_)?(x(w),ne==="{"?z(w,_)||M:f(_,10,$(w))):(O===7&&d(Ke.INVALID_LINKED_FORMAT,i(),0),_.braceNest=0,_.inLinked=!1,K(w,_))}}function K(w,_){let O={type:13};if(_.braceNest>0)return z(w,_)||m(_);if(_.inLinked)return J(w,_)||m(_);switch(w.currentChar()){case"{":return z(w,_)||m(_);case"}":return d(Ke.UNBALANCED_CLOSING_BRACE,i(),0),w.next(),f(_,3,"}");case"@":return J(w,_)||m(_);default:{if(F(w))return O=f(_,1,k(w)),_.braceNest=0,_.inLinked=!1,O;if(B(w))return f(_,0,Te(w));break}}return O}function q(){const{currentType:w,offset:_,startLoc:O,endLoc:M}=a;return a.lastType=w,a.lastOffset=_,a.lastStartLoc=O,a.lastEndLoc=M,a.offset=o(),a.startLoc=i(),r.currentChar()===Jn?f(a,13):K(r,a)}return{nextToken:q,currentOffset:o,currentPosition:i,context:c}}const l0="parser",a0=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function c0(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const r=parseInt(t||n,16);return r<=55295||r>=57344?String.fromCodePoint(r):"�"}}}function u0(e={}){const t=e.location!==!1,{onError:n}=e;function r(h,y,P,b,...C){const S=h.currentPosition();if(S.offset+=b,S.column+=b,n){const v=t?Ul(P,S):null,E=Is(y,v,{domain:l0,args:C});n(E)}}function o(h,y,P){const b={type:h};return t&&(b.start=y,b.end=y,b.loc={start:P,end:P}),b}function i(h,y,P,b){t&&(h.end=y,h.loc&&(h.loc.end=P))}function s(h,y){const P=h.context(),b=o(3,P.offset,P.startLoc);return b.value=y,i(b,h.currentOffset(),h.currentPosition()),b}function l(h,y){const P=h.context(),{lastOffset:b,lastStartLoc:C}=P,S=o(5,b,C);return S.index=parseInt(y,10),h.nextToken(),i(S,h.currentOffset(),h.currentPosition()),S}function a(h,y){const P=h.context(),{lastOffset:b,lastStartLoc:C}=P,S=o(4,b,C);return S.key=y,h.nextToken(),i(S,h.currentOffset(),h.currentPosition()),S}function c(h,y){const P=h.context(),{lastOffset:b,lastStartLoc:C}=P,S=o(9,b,C);return S.value=y.replace(a0,c0),h.nextToken(),i(S,h.currentOffset(),h.currentPosition()),S}function u(h){const y=h.nextToken(),P=h.context(),{lastOffset:b,lastStartLoc:C}=P,S=o(8,b,C);return y.type!==11?(r(h,Ke.UNEXPECTED_EMPTY_LINKED_MODIFIER,P.lastStartLoc,0),S.value="",i(S,b,C),{nextConsumeToken:y,node:S}):(y.value==null&&r(h,Ke.UNEXPECTED_LEXICAL_ANALYSIS,P.lastStartLoc,0,_n(y)),S.value=y.value||"",i(S,h.currentOffset(),h.currentPosition()),{node:S})}function d(h,y){const P=h.context(),b=o(7,P.offset,P.startLoc);return b.value=y,i(b,h.currentOffset(),h.currentPosition()),b}function f(h){const y=h.context(),P=o(6,y.offset,y.startLoc);let b=h.nextToken();if(b.type===8){const C=u(h);P.modifier=C.node,b=C.nextConsumeToken||h.nextToken()}switch(b.type!==9&&r(h,Ke.UNEXPECTED_LEXICAL_ANALYSIS,y.lastStartLoc,0,_n(b)),b=h.nextToken(),b.type===2&&(b=h.nextToken()),b.type){case 10:b.value==null&&r(h,Ke.UNEXPECTED_LEXICAL_ANALYSIS,y.lastStartLoc,0,_n(b)),P.key=d(h,b.value||"");break;case 4:b.value==null&&r(h,Ke.UNEXPECTED_LEXICAL_ANALYSIS,y.lastStartLoc,0,_n(b)),P.key=a(h,b.value||"");break;case 5:b.value==null&&r(h,Ke.UNEXPECTED_LEXICAL_ANALYSIS,y.lastStartLoc,0,_n(b)),P.key=l(h,b.value||"");break;case 6:b.value==null&&r(h,Ke.UNEXPECTED_LEXICAL_ANALYSIS,y.lastStartLoc,0,_n(b)),P.key=c(h,b.value||"");break;default:{r(h,Ke.UNEXPECTED_EMPTY_LINKED_KEY,y.lastStartLoc,0);const C=h.context(),S=o(7,C.offset,C.startLoc);return S.value="",i(S,C.offset,C.startLoc),P.key=S,i(P,C.offset,C.startLoc),{nextConsumeToken:b,node:P}}}return i(P,h.currentOffset(),h.currentPosition()),{node:P}}function m(h){const y=h.context(),P=y.currentType===1?h.currentOffset():y.offset,b=y.currentType===1?y.endLoc:y.startLoc,C=o(2,P,b);C.items=[];let S=null;do{const A=S||h.nextToken();switch(S=null,A.type){case 0:A.value==null&&r(h,Ke.UNEXPECTED_LEXICAL_ANALYSIS,y.lastStartLoc,0,_n(A)),C.items.push(s(h,A.value||""));break;case 5:A.value==null&&r(h,Ke.UNEXPECTED_LEXICAL_ANALYSIS,y.lastStartLoc,0,_n(A)),C.items.push(l(h,A.value||""));break;case 4:A.value==null&&r(h,Ke.UNEXPECTED_LEXICAL_ANALYSIS,y.lastStartLoc,0,_n(A)),C.items.push(a(h,A.value||""));break;case 6:A.value==null&&r(h,Ke.UNEXPECTED_LEXICAL_ANALYSIS,y.lastStartLoc,0,_n(A)),C.items.push(c(h,A.value||""));break;case 7:{const F=f(h);C.items.push(F.node),S=F.nextConsumeToken||null;break}}}while(y.currentType!==13&&y.currentType!==1);const v=y.currentType===1?y.lastOffset:h.currentOffset(),E=y.currentType===1?y.lastEndLoc:h.currentPosition();return i(C,v,E),C}function p(h,y,P,b){const C=h.context();let S=b.items.length===0;const v=o(1,y,P);v.cases=[],v.cases.push(b);do{const E=m(h);S||(S=E.items.length===0),v.cases.push(E)}while(C.currentType!==13);return S&&r(h,Ke.MUST_HAVE_MESSAGES_IN_PLURAL,P,0),i(v,h.currentOffset(),h.currentPosition()),v}function g(h){const y=h.context(),{offset:P,startLoc:b}=y,C=m(h);return y.currentType===13?C:p(h,P,b,C)}function x(h){const y=s0(h,_t({},e)),P=y.context(),b=o(0,P.offset,P.startLoc);return t&&b.loc&&(b.loc.source=h),b.body=g(y),e.onCacheKey&&(b.cacheKey=e.onCacheKey(h)),P.currentType!==13&&r(y,Ke.UNEXPECTED_LEXICAL_ANALYSIS,P.lastStartLoc,0,h[P.offset]||""),i(b,y.currentOffset(),y.currentPosition()),b}return{parse:x}}function _n(e){if(e.type===13)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function d0(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:i=>(n.helpers.add(i),i)}}function fu(e,t){for(let n=0;n<e.length;n++)za(e[n],t)}function za(e,t){switch(e.type){case 1:fu(e.cases,t),t.helper("plural");break;case 2:fu(e.items,t);break;case 6:{za(e.key,t),t.helper("linked"),t.helper("type");break}case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named");break}}function f0(e,t={}){const n=d0(e);n.helper("normalize"),e.body&&za(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function h0(e){const t=e.body;return t.type===2?hu(t):t.cases.forEach(n=>hu(n)),e}function hu(e){if(e.items.length===1){const t=e.items[0];(t.type===3||t.type===9)&&(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const r=e.items[n];if(!(r.type===3||r.type===9)||r.value==null)break;t.push(r.value)}if(t.length===e.items.length){e.static=Da(t);for(let n=0;n<e.items.length;n++){const r=e.items[n];(r.type===3||r.type===9)&&delete r.value}}}}function oo(e){switch(e.t=e.type,e.type){case 0:{const t=e;oo(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let r=0;r<n.length;r++)oo(n[r]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let r=0;r<n.length;r++)oo(n[r]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;oo(t.key),t.k=t.key,delete t.key,t.modifier&&(oo(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}}delete e.type}function p0(e,t){const{filename:n,breakLineCode:r,needIndent:o}=t,i=t.location!==!1,s={filename:n,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:r,needIndent:o,indentLevel:0};i&&e.loc&&(s.source=e.loc.source);const l=()=>s;function a(g,x){s.code+=g}function c(g,x=!0){const h=x?r:"";a(o?h+"  ".repeat(g):h)}function u(g=!0){const x=++s.indentLevel;g&&c(x)}function d(g=!0){const x=--s.indentLevel;g&&c(x)}function f(){c(s.indentLevel)}return{context:l,push:a,indent:u,deindent:d,newline:f,helper:g=>`_${g}`,needIndent:()=>s.needIndent}}function g0(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),yo(e,t.key),t.modifier?(e.push(", "),yo(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}function v0(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const o=t.items.length;for(let i=0;i<o&&(yo(e,t.items[i]),i!==o-1);i++)e.push(", ");e.deindent(r()),e.push("])")}function m0(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const o=t.cases.length;for(let i=0;i<o&&(yo(e,t.cases[i]),i!==o-1);i++)e.push(", ");e.deindent(r()),e.push("])")}}function b0(e,t){t.body?yo(e,t.body):e.push("null")}function yo(e,t){const{helper:n}=e;switch(t.type){case 0:b0(e,t);break;case 1:m0(e,t);break;case 2:v0(e,t);break;case 6:g0(e,t);break;case 8:e.push(JSON.stringify(t.value),t);break;case 7:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t);break;case 9:e.push(JSON.stringify(t.value),t);break;case 3:e.push(JSON.stringify(t.value),t);break}}const y0=(e,t={})=>{const n=le(t.mode)?t.mode:"normal",r=le(t.filename)?t.filename:"message.intl";t.sourceMap;const o=t.breakLineCode!=null?t.breakLineCode:n==="arrow"?";":`
`,i=t.needIndent?t.needIndent:n!=="arrow",s=e.helpers||[],l=p0(e,{filename:r,breakLineCode:o,needIndent:i});l.push(n==="normal"?"function __msg__ (ctx) {":"(ctx) => {"),l.indent(i),s.length>0&&(l.push(`const { ${Da(s.map(u=>`${u}: _${u}`),", ")} } = ctx`),l.newline()),l.push("return "),yo(l,e),l.deindent(i),l.push("}"),delete e.helpers;const{code:a,map:c}=l.context();return{ast:e,code:a,map:c?c.toJSON():void 0}};function x0(e,t={}){const n=_t({},t),r=!!n.jit,o=!!n.minify,i=n.optimize==null?!0:n.optimize,l=u0(n).parse(e);return r?(i&&h0(l),o&&oo(l),{ast:l,code:""}):(f0(l,n),y0(l,n))}/*!
  * core-base v10.0.7
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */function w0(){typeof __INTLIFY_PROD_DEVTOOLS__!="boolean"&&(kr().__INTLIFY_PROD_DEVTOOLS__=!1),typeof __INTLIFY_DROP_MESSAGE_COMPILER__!="boolean"&&(kr().__INTLIFY_DROP_MESSAGE_COMPILER__=!1)}function On(e){return Be(e)&&Ba(e)===0&&(pn(e,"b")||pn(e,"body"))}const Kh=["b","body"];function _0(e){return _r(e,Kh)}const Gh=["c","cases"];function C0(e){return _r(e,Gh,[])}const Yh=["s","static"];function S0(e){return _r(e,Yh)}const Xh=["i","items"];function E0(e){return _r(e,Xh,[])}const qh=["t","type"];function Ba(e){return _r(e,qh)}const Zh=["v","value"];function Ti(e,t){const n=_r(e,Zh);if(n!=null)return n;throw Qo(t)}const Jh=["m","modifier"];function T0(e){return _r(e,Jh)}const Qh=["k","key"];function $0(e){const t=_r(e,Qh);if(t)return t;throw Qo(6)}function _r(e,t,n){for(let r=0;r<t.length;r++){const o=t[r];if(pn(e,o)&&e[o]!=null)return e[o]}return n}const ep=[...Kh,...Gh,...Yh,...Xh,...Qh,...Jh,...Zh,...qh];function Qo(e){return new Error(`unhandled node type: ${e}`)}function ul(e){return n=>P0(n,e)}function P0(e,t){const n=_0(t);if(n==null)throw Qo(0);if(Ba(n)===1){const i=C0(n);return e.plural(i.reduce((s,l)=>[...s,pu(e,l)],[]))}else return pu(e,n)}function pu(e,t){const n=S0(t);if(n!=null)return e.type==="text"?n:e.normalize([n]);{const r=E0(t).reduce((o,i)=>[...o,Vl(e,i)],[]);return e.normalize(r)}}function Vl(e,t){const n=Ba(t);switch(n){case 3:return Ti(t,n);case 9:return Ti(t,n);case 4:{const r=t;if(pn(r,"k")&&r.k)return e.interpolate(e.named(r.k));if(pn(r,"key")&&r.key)return e.interpolate(e.named(r.key));throw Qo(n)}case 5:{const r=t;if(pn(r,"i")&&ht(r.i))return e.interpolate(e.list(r.i));if(pn(r,"index")&&ht(r.index))return e.interpolate(e.list(r.index));throw Qo(n)}case 6:{const r=t,o=T0(r),i=$0(r);return e.linked(Vl(e,i),o?Vl(e,o):void 0,e.type)}case 7:return Ti(t,n);case 8:return Ti(t,n);default:throw new Error(`unhandled node on format message part: ${n}`)}}const O0=e=>e;let $i=qe();function k0(e,t={}){let n=!1;const r=t.onError||Qb;return t.onError=o=>{n=!0,r(o)},{...x0(e,t),detectError:n}}function A0(e,t){if(!__INTLIFY_DROP_MESSAGE_COMPILER__&&le(e)){Fe(t.warnHtmlMessage)&&t.warnHtmlMessage;const r=(t.onCacheKey||O0)(e),o=$i[r];if(o)return o;const{ast:i,detectError:s}=k0(e,{...t,location:!1,jit:!0}),l=ul(i);return s?l:$i[r]=l}else{const n=e.cacheKey;if(n){const r=$i[n];return r||($i[n]=ul(e))}else return ul(e)}}let ei=null;function I0(e){ei=e}function R0(e,t,n){ei&&ei.emit("i18n:init",{timestamp:Date.now(),i18n:e,version:t,meta:n})}const L0=M0("function:translate");function M0(e){return t=>ei&&ei.emit(e,t)}const Hn={INVALID_ARGUMENT:Jb,INVALID_DATE_ARGUMENT:18,INVALID_ISO_DATE_ARGUMENT:19,NOT_SUPPORT_LOCALE_PROMISE_VALUE:21,NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:22,NOT_SUPPORT_LOCALE_TYPE:23},F0=24;function jn(e){return Is(e,null,void 0)}function Ha(e,t){return t.locale!=null?gu(t.locale):gu(e.locale)}let dl;function gu(e){if(le(e))return e;if(ct(e)){if(e.resolvedOnce&&dl!=null)return dl;if(e.constructor.name==="Function"){const t=e();if(Yb(t))throw jn(Hn.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return dl=t}else throw jn(Hn.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}else throw jn(Hn.NOT_SUPPORT_LOCALE_TYPE)}function N0(e,t,n){return[...new Set([n,...dt(t)?t:Be(t)?Object.keys(t):le(t)?[t]:[n]])]}function tp(e,t,n){const r=le(n)?n:ti,o=e;o.__localeChainCache||(o.__localeChainCache=new Map);let i=o.__localeChainCache.get(r);if(!i){i=[];let s=[n];for(;dt(s);)s=vu(i,s,t);const l=dt(t)||!Ie(t)?t:t.default?t.default:null;s=le(l)?[l]:l,dt(s)&&vu(i,s,!1),o.__localeChainCache.set(r,i)}return i}function vu(e,t,n){let r=!0;for(let o=0;o<t.length&&Fe(r);o++){const i=t[o];le(i)&&(r=D0(e,t[o],n))}return r}function D0(e,t,n){let r;const o=t.split("-");do{const i=o.join("-");r=z0(e,i,n),o.splice(-1,1)}while(o.length&&r===!0);return r}function z0(e,t,n){let r=!1;if(!e.includes(t)&&(r=!0,t)){r=t[t.length-1]!=="!";const o=t.replace(/!/g,"");e.push(o),(dt(n)||Ie(n))&&n[o]&&(r=n[o])}return r}const Cr=[];Cr[0]={w:[0],i:[3,0],"[":[4],o:[7]};Cr[1]={w:[1],".":[2],"[":[4],o:[7]};Cr[2]={w:[2],i:[3,0],0:[3,0]};Cr[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]};Cr[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]};Cr[5]={"'":[4,0],o:8,l:[5,0]};Cr[6]={'"':[4,0],o:8,l:[6,0]};const B0=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function H0(e){return B0.test(e)}function j0(e){const t=e.charCodeAt(0),n=e.charCodeAt(e.length-1);return t===n&&(t===34||t===39)?e.slice(1,-1):e}function W0(e){if(e==null)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function U0(e){const t=e.trim();return e.charAt(0)==="0"&&isNaN(parseInt(e))?!1:H0(t)?j0(t):"*"+t}function V0(e){const t=[];let n=-1,r=0,o=0,i,s,l,a,c,u,d;const f=[];f[0]=()=>{s===void 0?s=l:s+=l},f[1]=()=>{s!==void 0&&(t.push(s),s=void 0)},f[2]=()=>{f[0](),o++},f[3]=()=>{if(o>0)o--,r=4,f[0]();else{if(o=0,s===void 0||(s=U0(s),s===!1))return!1;f[1]()}};function m(){const p=e[n+1];if(r===5&&p==="'"||r===6&&p==='"')return n++,l="\\"+p,f[0](),!0}for(;r!==null;)if(n++,i=e[n],!(i==="\\"&&m())){if(a=W0(i),d=Cr[r],c=d[a]||d.l||8,c===8||(r=c[0],c[1]!==void 0&&(u=f[c[1]],u&&(l=i,u()===!1))))return;if(r===7)return t}}const mu=new Map;function K0(e,t){return Be(e)?e[t]:null}function G0(e,t){if(!Be(e))return null;let n=mu.get(t);if(n||(n=V0(t),n&&mu.set(t,n)),!n)return null;const r=n.length;let o=e,i=0;for(;i<r;){const s=n[i];if(ep.includes(s)&&On(o))return null;const l=o[s];if(l===void 0||ct(o))return null;o=l,i++}return o}const Y0="10.0.7",Rs=-1,ti="en-US",bu="",yu=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;function X0(){return{upper:(e,t)=>t==="text"&&le(e)?e.toUpperCase():t==="vnode"&&Be(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>t==="text"&&le(e)?e.toLowerCase():t==="vnode"&&Be(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>t==="text"&&le(e)?yu(e):t==="vnode"&&Be(e)&&"__v_isVNode"in e?yu(e.children):e}}let np;function q0(e){np=e}let rp;function Z0(e){rp=e}let op;function J0(e){op=e}let ip=null;const Q0=e=>{ip=e},e1=()=>ip;let sp=null;const xu=e=>{sp=e},t1=()=>sp;let wu=0;function n1(e={}){const t=ct(e.onWarn)?e.onWarn:qb,n=le(e.version)?e.version:Y0,r=le(e.locale)||ct(e.locale)?e.locale:ti,o=ct(r)?ti:r,i=dt(e.fallbackLocale)||Ie(e.fallbackLocale)||le(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:o,s=Ie(e.messages)?e.messages:fl(o),l=Ie(e.datetimeFormats)?e.datetimeFormats:fl(o),a=Ie(e.numberFormats)?e.numberFormats:fl(o),c=_t(qe(),e.modifiers,X0()),u=e.pluralRules||qe(),d=ct(e.missing)?e.missing:null,f=Fe(e.missingWarn)||bo(e.missingWarn)?e.missingWarn:!0,m=Fe(e.fallbackWarn)||bo(e.fallbackWarn)?e.fallbackWarn:!0,p=!!e.fallbackFormat,g=!!e.unresolving,x=ct(e.postTranslation)?e.postTranslation:null,h=Ie(e.processor)?e.processor:null,y=Fe(e.warnHtmlMessage)?e.warnHtmlMessage:!0,P=!!e.escapeParameter,b=ct(e.messageCompiler)?e.messageCompiler:np,C=ct(e.messageResolver)?e.messageResolver:rp||K0,S=ct(e.localeFallbacker)?e.localeFallbacker:op||N0,v=Be(e.fallbackContext)?e.fallbackContext:void 0,E=e,A=Be(E.__datetimeFormatters)?E.__datetimeFormatters:new Map,F=Be(E.__numberFormatters)?E.__numberFormatters:new Map,B=Be(E.__meta)?E.__meta:{};wu++;const R={version:n,cid:wu,locale:r,fallbackLocale:i,messages:s,modifiers:c,pluralRules:u,missing:d,missingWarn:f,fallbackWarn:m,fallbackFormat:p,unresolving:g,postTranslation:x,processor:h,warnHtmlMessage:y,escapeParameter:P,messageCompiler:b,messageResolver:C,localeFallbacker:S,fallbackContext:v,onWarn:t,__meta:B};return R.datetimeFormats=l,R.numberFormats=a,R.__datetimeFormatters=A,R.__numberFormatters=F,__INTLIFY_PROD_DEVTOOLS__&&R0(R,n,B),R}const fl=e=>({[e]:qe()});function ja(e,t,n,r,o){const{missing:i,onWarn:s}=e;if(i!==null){const l=i(e,n,t,o);return le(l)?l:t}else return t}function Oo(e,t,n){const r=e;r.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}function r1(e,t){return e===t?!1:e.split("-")[0]===t.split("-")[0]}function o1(e,t){const n=t.indexOf(e);if(n===-1)return!1;for(let r=n+1;r<t.length;r++)if(r1(e,t[r]))return!0;return!1}function _u(e,...t){const{datetimeFormats:n,unresolving:r,fallbackLocale:o,onWarn:i,localeFallbacker:s}=e,{__datetimeFormatters:l}=e,[a,c,u,d]=Kl(...t),f=Fe(u.missingWarn)?u.missingWarn:e.missingWarn;Fe(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn;const m=!!u.part,p=Ha(e,u),g=s(e,o,p);if(!le(a)||a==="")return new Intl.DateTimeFormat(p,d).format(c);let x={},h,y=null;const P="datetime format";for(let S=0;S<g.length&&(h=g[S],x=n[h]||{},y=x[a],!Ie(y));S++)ja(e,a,h,f,P);if(!Ie(y)||!le(h))return r?Rs:a;let b=`${h}__${a}`;As(d)||(b=`${b}__${JSON.stringify(d)}`);let C=l.get(b);return C||(C=new Intl.DateTimeFormat(h,_t({},y,d)),l.set(b,C)),m?C.formatToParts(c):C.format(c)}const lp=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function Kl(...e){const[t,n,r,o]=e,i=qe();let s=qe(),l;if(le(t)){const a=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!a)throw jn(Hn.INVALID_ISO_DATE_ARGUMENT);const c=a[3]?a[3].trim().startsWith("T")?`${a[1].trim()}${a[3].trim()}`:`${a[1].trim()}T${a[3].trim()}`:a[1].trim();l=new Date(c);try{l.toISOString()}catch{throw jn(Hn.INVALID_ISO_DATE_ARGUMENT)}}else if(Vb(t)){if(isNaN(t.getTime()))throw jn(Hn.INVALID_DATE_ARGUMENT);l=t}else if(ht(t))l=t;else throw jn(Hn.INVALID_ARGUMENT);return le(n)?i.key=n:Ie(n)&&Object.keys(n).forEach(a=>{lp.includes(a)?s[a]=n[a]:i[a]=n[a]}),le(r)?i.locale=r:Ie(r)&&(s=r),Ie(o)&&(s=o),[i.key||"",l,i,s]}function Cu(e,t,n){const r=e;for(const o in n){const i=`${t}__${o}`;r.__datetimeFormatters.has(i)&&r.__datetimeFormatters.delete(i)}}function Su(e,...t){const{numberFormats:n,unresolving:r,fallbackLocale:o,onWarn:i,localeFallbacker:s}=e,{__numberFormatters:l}=e,[a,c,u,d]=Gl(...t),f=Fe(u.missingWarn)?u.missingWarn:e.missingWarn;Fe(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn;const m=!!u.part,p=Ha(e,u),g=s(e,o,p);if(!le(a)||a==="")return new Intl.NumberFormat(p,d).format(c);let x={},h,y=null;const P="number format";for(let S=0;S<g.length&&(h=g[S],x=n[h]||{},y=x[a],!Ie(y));S++)ja(e,a,h,f,P);if(!Ie(y)||!le(h))return r?Rs:a;let b=`${h}__${a}`;As(d)||(b=`${b}__${JSON.stringify(d)}`);let C=l.get(b);return C||(C=new Intl.NumberFormat(h,_t({},y,d)),l.set(b,C)),m?C.formatToParts(c):C.format(c)}const ap=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function Gl(...e){const[t,n,r,o]=e,i=qe();let s=qe();if(!ht(t))throw jn(Hn.INVALID_ARGUMENT);const l=t;return le(n)?i.key=n:Ie(n)&&Object.keys(n).forEach(a=>{ap.includes(a)?s[a]=n[a]:i[a]=n[a]}),le(r)?i.locale=r:Ie(r)&&(s=r),Ie(o)&&(s=o),[i.key||"",l,i,s]}function Eu(e,t,n){const r=e;for(const o in n){const i=`${t}__${o}`;r.__numberFormatters.has(i)&&r.__numberFormatters.delete(i)}}const i1=e=>e,s1=e=>"",l1="text",a1=e=>e.length===0?"":Da(e),c1=Xb;function Tu(e,t){return e=Math.abs(e),t===2?e?e>1?1:0:1:e?Math.min(e,2):0}function u1(e){const t=ht(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(ht(e.named.count)||ht(e.named.n))?ht(e.named.count)?e.named.count:ht(e.named.n)?e.named.n:t:t}function d1(e,t){t.count||(t.count=e),t.n||(t.n=e)}function f1(e={}){const t=e.locale,n=u1(e),r=Be(e.pluralRules)&&le(t)&&ct(e.pluralRules[t])?e.pluralRules[t]:Tu,o=Be(e.pluralRules)&&le(t)&&ct(e.pluralRules[t])?Tu:void 0,i=h=>h[r(n,h.length,o)],s=e.list||[],l=h=>s[h],a=e.named||qe();ht(e.pluralIndex)&&d1(n,a);const c=h=>a[h];function u(h,y){const P=ct(e.messages)?e.messages(h,!!y):Be(e.messages)?e.messages[h]:!1;return P||(e.parent?e.parent.message(h):s1)}const d=h=>e.modifiers?e.modifiers[h]:i1,f=Ie(e.processor)&&ct(e.processor.normalize)?e.processor.normalize:a1,m=Ie(e.processor)&&ct(e.processor.interpolate)?e.processor.interpolate:c1,p=Ie(e.processor)&&le(e.processor.type)?e.processor.type:l1,x={list:l,named:c,plural:i,linked:(h,...y)=>{const[P,b]=y;let C="text",S="";y.length===1?Be(P)?(S=P.modifier||S,C=P.type||C):le(P)&&(S=P||S):y.length===2&&(le(P)&&(S=P||S),le(b)&&(C=b||C));const v=u(h,!0)(x),E=C==="vnode"&&dt(v)&&S?v[0]:v;return S?d(S)(E,C):E},message:u,type:p,interpolate:m,normalize:f,values:_t(qe(),s,a)};return x}const $u=()=>"",sn=e=>ct(e);function Pu(e,...t){const{fallbackFormat:n,postTranslation:r,unresolving:o,messageCompiler:i,fallbackLocale:s,messages:l}=e,[a,c]=Yl(...t),u=Fe(c.missingWarn)?c.missingWarn:e.missingWarn,d=Fe(c.fallbackWarn)?c.fallbackWarn:e.fallbackWarn,f=Fe(c.escapeParameter)?c.escapeParameter:e.escapeParameter,m=!!c.resolvedMessage,p=le(c.default)||Fe(c.default)?Fe(c.default)?i?a:()=>a:c.default:n?i?a:()=>a:null,g=n||p!=null&&(le(p)||ct(p)),x=Ha(e,c);f&&h1(c);let[h,y,P]=m?[a,x,l[x]||qe()]:cp(e,a,x,s,d,u),b=h,C=a;if(!m&&!(le(b)||On(b)||sn(b))&&g&&(b=p,C=b),!m&&(!(le(b)||On(b)||sn(b))||!le(y)))return o?Rs:a;let S=!1;const v=()=>{S=!0},E=sn(b)?b:up(e,a,y,b,C,v);if(S)return b;const A=v1(e,y,P,c),F=f1(A),B=p1(e,E,F),R=r?r(B,a):B;if(__INTLIFY_PROD_DEVTOOLS__){const j={timestamp:Date.now(),key:le(a)?a:sn(b)?b.key:"",locale:y||(sn(b)?b.locale:""),format:le(b)?b:sn(b)?b.source:"",message:R};j.meta=_t({},e.__meta,e1()||{}),L0(j)}return R}function h1(e){dt(e.list)?e.list=e.list.map(t=>le(t)?uu(t):t):Be(e.named)&&Object.keys(e.named).forEach(t=>{le(e.named[t])&&(e.named[t]=uu(e.named[t]))})}function cp(e,t,n,r,o,i){const{messages:s,onWarn:l,messageResolver:a,localeFallbacker:c}=e,u=c(e,r,n);let d=qe(),f,m=null;const p="translate";for(let g=0;g<u.length&&(f=u[g],d=s[f]||qe(),(m=a(d,t))===null&&(m=d[t]),!(le(m)||On(m)||sn(m)));g++)if(!o1(f,u)){const x=ja(e,t,f,i,p);x!==t&&(m=x)}return[m,f,d]}function up(e,t,n,r,o,i){const{messageCompiler:s,warnHtmlMessage:l}=e;if(sn(r)){const c=r;return c.locale=c.locale||n,c.key=c.key||t,c}if(s==null){const c=()=>r;return c.locale=n,c.key=t,c}const a=s(r,g1(e,n,o,r,l,i));return a.locale=n,a.key=t,a.source=r,a}function p1(e,t,n){return t(n)}function Yl(...e){const[t,n,r]=e,o=qe();if(!le(t)&&!ht(t)&&!sn(t)&&!On(t))throw jn(Hn.INVALID_ARGUMENT);const i=ht(t)?String(t):(sn(t),t);return ht(n)?o.plural=n:le(n)?o.default=n:Ie(n)&&!As(n)?o.named=n:dt(n)&&(o.list=n),ht(r)?o.plural=r:le(r)?o.default=r:Ie(r)&&_t(o,r),[i,o]}function g1(e,t,n,r,o,i){return{locale:t,key:n,warnHtmlMessage:o,onError:s=>{throw i&&i(s),s},onCacheKey:s=>Wb(t,n,s)}}function v1(e,t,n,r){const{modifiers:o,pluralRules:i,messageResolver:s,fallbackLocale:l,fallbackWarn:a,missingWarn:c,fallbackContext:u}=e,f={locale:t,modifiers:o,pluralRules:i,messages:(m,p)=>{let g=s(n,m);if(g==null&&(u||p)){const[,,x]=cp(u||e,m,t,l,a,c);g=s(x,m)}if(le(g)||On(g)){let x=!1;const y=up(e,m,t,g,m,()=>{x=!0});return x?$u:y}else return sn(g)?g:$u}};return e.processor&&(f.processor=e.processor),r.list&&(f.list=r.list),r.named&&(f.named=r.named),ht(r.plural)&&(f.pluralIndex=r.plural),f}w0();/*!
  * vue-i18n v10.0.7
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */const m1="10.0.7";function b1(){typeof __VUE_I18N_FULL_INSTALL__!="boolean"&&(kr().__VUE_I18N_FULL_INSTALL__=!0),typeof __VUE_I18N_LEGACY_API__!="boolean"&&(kr().__VUE_I18N_LEGACY_API__=!0),typeof __INTLIFY_DROP_MESSAGE_COMPILER__!="boolean"&&(kr().__INTLIFY_DROP_MESSAGE_COMPILER__=!1),typeof __INTLIFY_PROD_DEVTOOLS__!="boolean"&&(kr().__INTLIFY_PROD_DEVTOOLS__=!1)}const jt={UNEXPECTED_RETURN_TYPE:F0,INVALID_ARGUMENT:25,MUST_BE_CALL_SETUP_TOP:26,NOT_INSTALLED:27,REQUIRED_VALUE:28,INVALID_VALUE:29,NOT_INSTALLED_WITH_PROVIDE:31,UNEXPECTED_ERROR:32};function Zt(e,...t){return Is(e,null,void 0)}const Xl=wr("__translateVNode"),ql=wr("__datetimeParts"),Zl=wr("__numberParts"),dp=wr("__setPluralRules"),fp=wr("__injectWithOption"),Jl=wr("__dispose");function ni(e){if(!Be(e)||On(e))return e;for(const t in e)if(pn(e,t))if(!t.includes("."))Be(e[t])&&ni(e[t]);else{const n=t.split("."),r=n.length-1;let o=e,i=!1;for(let s=0;s<r;s++){if(n[s]==="__proto__")throw new Error(`unsafe key: ${n[s]}`);if(n[s]in o||(o[n[s]]=qe()),!Be(o[n[s]])){i=!0;break}o=o[n[s]]}if(i||(On(o)?ep.includes(n[r])||delete e[t]:(o[n[r]]=e[t],delete e[t])),!On(o)){const s=o[n[r]];Be(s)&&ni(s)}}return e}function Wa(e,t){const{messages:n,__i18n:r,messageResolver:o,flatJson:i}=t,s=Ie(n)?n:dt(r)?qe():{[e]:qe()};if(dt(r)&&r.forEach(l=>{if("locale"in l&&"resource"in l){const{locale:a,resource:c}=l;a?(s[a]=s[a]||qe(),Yi(c,s[a])):Yi(c,s)}else le(l)&&Yi(JSON.parse(l),s)}),o==null&&i)for(const l in s)pn(s,l)&&ni(s[l]);return s}function hp(e){return e.type}function pp(e,t,n){let r=Be(t.messages)?t.messages:qe();"__i18nGlobal"in n&&(r=Wa(e.locale.value,{messages:r,__i18n:n.__i18nGlobal}));const o=Object.keys(r);o.length&&o.forEach(i=>{e.mergeLocaleMessage(i,r[i])});{if(Be(t.datetimeFormats)){const i=Object.keys(t.datetimeFormats);i.length&&i.forEach(s=>{e.mergeDateTimeFormat(s,t.datetimeFormats[s])})}if(Be(t.numberFormats)){const i=Object.keys(t.numberFormats);i.length&&i.forEach(s=>{e.mergeNumberFormat(s,t.numberFormats[s])})}}}function Ou(e){return Ge(_o,null,e,0)}const ku="__INTLIFY_META__",Au=()=>[],y1=()=>!1;let Iu=0;function Ru(e){return(t,n,r,o)=>e(n,r,In()||void 0,o)}const x1=()=>{const e=In();let t=null;return e&&(t=hp(e)[ku])?{[ku]:t}:null};function Ua(e={}){const{__root:t,__injectWithOption:n}=e,r=t===void 0,o=e.flatJson,i=as?D:Kf;let s=Fe(e.inheritLocale)?e.inheritLocale:!0;const l=i(t&&s?t.locale.value:le(e.locale)?e.locale:ti),a=i(t&&s?t.fallbackLocale.value:le(e.fallbackLocale)||dt(e.fallbackLocale)||Ie(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:l.value),c=i(Wa(l.value,e)),u=i(Ie(e.datetimeFormats)?e.datetimeFormats:{[l.value]:{}}),d=i(Ie(e.numberFormats)?e.numberFormats:{[l.value]:{}});let f=t?t.missingWarn:Fe(e.missingWarn)||bo(e.missingWarn)?e.missingWarn:!0,m=t?t.fallbackWarn:Fe(e.fallbackWarn)||bo(e.fallbackWarn)?e.fallbackWarn:!0,p=t?t.fallbackRoot:Fe(e.fallbackRoot)?e.fallbackRoot:!0,g=!!e.fallbackFormat,x=ct(e.missing)?e.missing:null,h=ct(e.missing)?Ru(e.missing):null,y=ct(e.postTranslation)?e.postTranslation:null,P=t?t.warnHtmlMessage:Fe(e.warnHtmlMessage)?e.warnHtmlMessage:!0,b=!!e.escapeParameter;const C=t?t.modifiers:Ie(e.modifiers)?e.modifiers:{};let S=e.pluralRules||t&&t.pluralRules,v;v=(()=>{r&&xu(null);const I={version:m1,locale:l.value,fallbackLocale:a.value,messages:c.value,modifiers:C,pluralRules:S,missing:h===null?void 0:h,missingWarn:f,fallbackWarn:m,fallbackFormat:g,unresolving:!0,postTranslation:y===null?void 0:y,warnHtmlMessage:P,escapeParameter:b,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};I.datetimeFormats=u.value,I.numberFormats=d.value,I.__datetimeFormatters=Ie(v)?v.__datetimeFormatters:void 0,I.__numberFormatters=Ie(v)?v.__numberFormatters:void 0;const N=n1(I);return r&&xu(N),N})(),Oo(v,l.value,a.value);function A(){return[l.value,a.value,c.value,u.value,d.value]}const F=V({get:()=>l.value,set:I=>{l.value=I,v.locale=l.value}}),B=V({get:()=>a.value,set:I=>{a.value=I,v.fallbackLocale=a.value,Oo(v,l.value,I)}}),R=V(()=>c.value),j=V(()=>u.value),Q=V(()=>d.value);function H(){return ct(y)?y:null}function te(I){y=I,v.postTranslation=I}function W(){return x}function re(I){I!==null&&(h=Ru(I)),x=I,v.missing=h}const ue=(I,N,ie,de,_e,Xe)=>{A();let He;try{__INTLIFY_PROD_DEVTOOLS__,r||(v.fallbackContext=t?t1():void 0),He=I(v)}finally{__INTLIFY_PROD_DEVTOOLS__,r||(v.fallbackContext=void 0)}if(ie!=="translate exists"&&ht(He)&&He===Rs||ie==="translate exists"&&!He){const[nt,vt]=N();return t&&p?de(t):_e(nt)}else{if(Xe(He))return He;throw Zt(jt.UNEXPECTED_RETURN_TYPE)}};function pe(...I){return ue(N=>Reflect.apply(Pu,null,[N,...I]),()=>Yl(...I),"translate",N=>Reflect.apply(N.t,N,[...I]),N=>N,N=>le(N))}function we(...I){const[N,ie,de]=I;if(de&&!Be(de))throw Zt(jt.INVALID_ARGUMENT);return pe(N,ie,_t({resolvedMessage:!0},de||{}))}function Te(...I){return ue(N=>Reflect.apply(_u,null,[N,...I]),()=>Kl(...I),"datetime format",N=>Reflect.apply(N.d,N,[...I]),()=>bu,N=>le(N))}function Ue(...I){return ue(N=>Reflect.apply(Su,null,[N,...I]),()=>Gl(...I),"number format",N=>Reflect.apply(N.n,N,[...I]),()=>bu,N=>le(N))}function st(I){return I.map(N=>le(N)||ht(N)||Fe(N)?Ou(String(N)):N)}const be={normalize:st,interpolate:I=>I,type:"vnode"};function me(...I){return ue(N=>{let ie;const de=N;try{de.processor=be,ie=Reflect.apply(Pu,null,[de,...I])}finally{de.processor=null}return ie},()=>Yl(...I),"translate",N=>N[Xl](...I),N=>[Ou(N)],N=>dt(N))}function St(...I){return ue(N=>Reflect.apply(Su,null,[N,...I]),()=>Gl(...I),"number format",N=>N[Zl](...I),Au,N=>le(N)||dt(N))}function $e(...I){return ue(N=>Reflect.apply(_u,null,[N,...I]),()=>Kl(...I),"datetime format",N=>N[ql](...I),Au,N=>le(N)||dt(N))}function tt(I){S=I,v.pluralRules=S}function Ot(I,N){return ue(()=>{if(!I)return!1;const ie=le(N)?N:l.value,de=z(ie),_e=v.messageResolver(de,I);return On(_e)||sn(_e)||le(_e)},()=>[I],"translate exists",ie=>Reflect.apply(ie.te,ie,[I,N]),y1,ie=>Fe(ie))}function $(I){let N=null;const ie=tp(v,a.value,l.value);for(let de=0;de<ie.length;de++){const _e=c.value[ie[de]]||{},Xe=v.messageResolver(_e,I);if(Xe!=null){N=Xe;break}}return N}function k(I){const N=$(I);return N??(t?t.tm(I)||{}:{})}function z(I){return c.value[I]||{}}function J(I,N){if(o){const ie={[I]:N};for(const de in ie)pn(ie,de)&&ni(ie[de]);N=ie[I]}c.value[I]=N,v.messages=c.value}function K(I,N){c.value[I]=c.value[I]||{};const ie={[I]:N};if(o)for(const de in ie)pn(ie,de)&&ni(ie[de]);N=ie[I],Yi(N,c.value[I]),v.messages=c.value}function q(I){return u.value[I]||{}}function w(I,N){u.value[I]=N,v.datetimeFormats=u.value,Cu(v,I,N)}function _(I,N){u.value[I]=_t(u.value[I]||{},N),v.datetimeFormats=u.value,Cu(v,I,N)}function O(I){return d.value[I]||{}}function M(I,N){d.value[I]=N,v.numberFormats=d.value,Eu(v,I,N)}function ne(I,N){d.value[I]=_t(d.value[I]||{},N),v.numberFormats=d.value,Eu(v,I,N)}Iu++,t&&as&&(Ye(t.locale,I=>{s&&(l.value=I,v.locale=I,Oo(v,l.value,a.value))}),Ye(t.fallbackLocale,I=>{s&&(a.value=I,v.fallbackLocale=I,Oo(v,l.value,a.value))}));const Y={id:Iu,locale:F,fallbackLocale:B,get inheritLocale(){return s},set inheritLocale(I){s=I,I&&t&&(l.value=t.locale.value,a.value=t.fallbackLocale.value,Oo(v,l.value,a.value))},get availableLocales(){return Object.keys(c.value).sort()},messages:R,get modifiers(){return C},get pluralRules(){return S||{}},get isGlobal(){return r},get missingWarn(){return f},set missingWarn(I){f=I,v.missingWarn=f},get fallbackWarn(){return m},set fallbackWarn(I){m=I,v.fallbackWarn=m},get fallbackRoot(){return p},set fallbackRoot(I){p=I},get fallbackFormat(){return g},set fallbackFormat(I){g=I,v.fallbackFormat=g},get warnHtmlMessage(){return P},set warnHtmlMessage(I){P=I,v.warnHtmlMessage=I},get escapeParameter(){return b},set escapeParameter(I){b=I,v.escapeParameter=I},t:pe,getLocaleMessage:z,setLocaleMessage:J,mergeLocaleMessage:K,getPostTranslationHandler:H,setPostTranslationHandler:te,getMissingHandler:W,setMissingHandler:re,[dp]:tt};return Y.datetimeFormats=j,Y.numberFormats=Q,Y.rt=we,Y.te=Ot,Y.tm=k,Y.d=Te,Y.n=Ue,Y.getDateTimeFormat=q,Y.setDateTimeFormat=w,Y.mergeDateTimeFormat=_,Y.getNumberFormat=O,Y.setNumberFormat=M,Y.mergeNumberFormat=ne,Y[fp]=n,Y[Xl]=me,Y[ql]=$e,Y[Zl]=St,Y}function w1(e){const t=le(e.locale)?e.locale:ti,n=le(e.fallbackLocale)||dt(e.fallbackLocale)||Ie(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:t,r=ct(e.missing)?e.missing:void 0,o=Fe(e.silentTranslationWarn)||bo(e.silentTranslationWarn)?!e.silentTranslationWarn:!0,i=Fe(e.silentFallbackWarn)||bo(e.silentFallbackWarn)?!e.silentFallbackWarn:!0,s=Fe(e.fallbackRoot)?e.fallbackRoot:!0,l=!!e.formatFallbackMessages,a=Ie(e.modifiers)?e.modifiers:{},c=e.pluralizationRules,u=ct(e.postTranslation)?e.postTranslation:void 0,d=le(e.warnHtmlInMessage)?e.warnHtmlInMessage!=="off":!0,f=!!e.escapeParameterHtml,m=Fe(e.sync)?e.sync:!0;let p=e.messages;if(Ie(e.sharedMessages)){const C=e.sharedMessages;p=Object.keys(C).reduce((v,E)=>{const A=v[E]||(v[E]={});return _t(A,C[E]),v},p||{})}const{__i18n:g,__root:x,__injectWithOption:h}=e,y=e.datetimeFormats,P=e.numberFormats,b=e.flatJson;return{locale:t,fallbackLocale:n,messages:p,flatJson:b,datetimeFormats:y,numberFormats:P,missing:r,missingWarn:o,fallbackWarn:i,fallbackRoot:s,fallbackFormat:l,modifiers:a,pluralRules:c,postTranslation:u,warnHtmlMessage:d,escapeParameter:f,messageResolver:e.messageResolver,inheritLocale:m,__i18n:g,__root:x,__injectWithOption:h}}function Ql(e={}){const t=Ua(w1(e)),{__extender:n}=e,r={id:t.id,get locale(){return t.locale.value},set locale(o){t.locale.value=o},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(o){t.fallbackLocale.value=o},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get missing(){return t.getMissingHandler()},set missing(o){t.setMissingHandler(o)},get silentTranslationWarn(){return Fe(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(o){t.missingWarn=Fe(o)?!o:o},get silentFallbackWarn(){return Fe(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(o){t.fallbackWarn=Fe(o)?!o:o},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(o){t.fallbackFormat=o},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(o){t.setPostTranslationHandler(o)},get sync(){return t.inheritLocale},set sync(o){t.inheritLocale=o},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(o){t.warnHtmlMessage=o!=="off"},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(o){t.escapeParameter=o},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t(...o){return Reflect.apply(t.t,t,[...o])},rt(...o){return Reflect.apply(t.rt,t,[...o])},tc(...o){const[i,s,l]=o,a={plural:1};let c=null,u=null;if(!le(i))throw Zt(jt.INVALID_ARGUMENT);const d=i;return le(s)?a.locale=s:ht(s)?a.plural=s:dt(s)?c=s:Ie(s)&&(u=s),le(l)?a.locale=l:dt(l)?c=l:Ie(l)&&(u=l),Reflect.apply(t.t,t,[d,c||u||{},a])},te(o,i){return t.te(o,i)},tm(o){return t.tm(o)},getLocaleMessage(o){return t.getLocaleMessage(o)},setLocaleMessage(o,i){t.setLocaleMessage(o,i)},mergeLocaleMessage(o,i){t.mergeLocaleMessage(o,i)},d(...o){return Reflect.apply(t.d,t,[...o])},getDateTimeFormat(o){return t.getDateTimeFormat(o)},setDateTimeFormat(o,i){t.setDateTimeFormat(o,i)},mergeDateTimeFormat(o,i){t.mergeDateTimeFormat(o,i)},n(...o){return Reflect.apply(t.n,t,[...o])},getNumberFormat(o){return t.getNumberFormat(o)},setNumberFormat(o,i){t.setNumberFormat(o,i)},mergeNumberFormat(o,i){t.mergeNumberFormat(o,i)}};return r.__extender=n,r}function _1(e,t,n){return{beforeCreate(){const r=In();if(!r)throw Zt(jt.UNEXPECTED_ERROR);const o=this.$options;if(o.i18n){const i=o.i18n;if(o.__i18n&&(i.__i18n=o.__i18n),i.__root=t,this===this.$root)this.$i18n=Lu(e,i);else{i.__injectWithOption=!0,i.__extender=n.__vueI18nExtend,this.$i18n=Ql(i);const s=this.$i18n;s.__extender&&(s.__disposer=s.__extender(this.$i18n))}}else if(o.__i18n)if(this===this.$root)this.$i18n=Lu(e,o);else{this.$i18n=Ql({__i18n:o.__i18n,__injectWithOption:!0,__extender:n.__vueI18nExtend,__root:t});const i=this.$i18n;i.__extender&&(i.__disposer=i.__extender(this.$i18n))}else this.$i18n=e;o.__i18nGlobal&&pp(t,o,o),this.$t=(...i)=>this.$i18n.t(...i),this.$rt=(...i)=>this.$i18n.rt(...i),this.$tc=(...i)=>this.$i18n.tc(...i),this.$te=(i,s)=>this.$i18n.te(i,s),this.$d=(...i)=>this.$i18n.d(...i),this.$n=(...i)=>this.$i18n.n(...i),this.$tm=i=>this.$i18n.tm(i),n.__setInstance(r,this.$i18n)},mounted(){},unmounted(){const r=In();if(!r)throw Zt(jt.UNEXPECTED_ERROR);const o=this.$i18n;delete this.$t,delete this.$rt,delete this.$tc,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,o.__disposer&&(o.__disposer(),delete o.__disposer,delete o.__extender),n.__deleteInstance(r),delete this.$i18n}}}function Lu(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[dp](t.pluralizationRules||e.pluralizationRules);const n=Wa(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(n).forEach(r=>e.mergeLocaleMessage(r,n[r])),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach(r=>e.mergeDateTimeFormat(r,t.datetimeFormats[r])),t.numberFormats&&Object.keys(t.numberFormats).forEach(r=>e.mergeNumberFormat(r,t.numberFormats[r])),e}const Va={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>e==="parent"||e==="global",default:"parent"},i18n:{type:Object}};function C1({slots:e},t){return t.length===1&&t[0]==="default"?(e.default?e.default():[]).reduce((r,o)=>[...r,...o.type===We?o.children:[o]],[]):t.reduce((n,r)=>{const o=e[r];return o&&(n[r]=o()),n},qe())}function gp(){return We}const S1=ge({name:"i18n-t",props:_t({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>ht(e)||!isNaN(e)}},Va),setup(e,t){const{slots:n,attrs:r}=t,o=e.i18n||Ls({useScope:e.scope,__useComponent:!0});return()=>{const i=Object.keys(n).filter(d=>d!=="_"),s=qe();e.locale&&(s.locale=e.locale),e.plural!==void 0&&(s.plural=le(e.plural)?+e.plural:e.plural);const l=C1(t,i),a=o[Xl](e.keypath,l,s),c=_t(qe(),r),u=le(e.tag)||Be(e.tag)?e.tag:gp();return T(u,c,a)}}}),Mu=S1;function E1(e){return dt(e)&&!le(e[0])}function vp(e,t,n,r){const{slots:o,attrs:i}=t;return()=>{const s={part:!0};let l=qe();e.locale&&(s.locale=e.locale),le(e.format)?s.key=e.format:Be(e.format)&&(le(e.format.key)&&(s.key=e.format.key),l=Object.keys(e.format).reduce((f,m)=>n.includes(m)?_t(qe(),f,{[m]:e.format[m]}):f,qe()));const a=r(e.value,s,l);let c=[s.key];dt(a)?c=a.map((f,m)=>{const p=o[f.type],g=p?p({[f.type]:f.value,index:m,parts:a}):[f.value];return E1(g)&&(g[0].key=`${f.type}-${m}`),g}):le(a)&&(c=[a]);const u=_t(qe(),i),d=le(e.tag)||Be(e.tag)?e.tag:gp();return T(d,u,c)}}const T1=ge({name:"i18n-n",props:_t({value:{type:Number,required:!0},format:{type:[String,Object]}},Va),setup(e,t){const n=e.i18n||Ls({useScope:e.scope,__useComponent:!0});return vp(e,t,ap,(...r)=>n[Zl](...r))}}),Fu=T1,$1=ge({name:"i18n-d",props:_t({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},Va),setup(e,t){const n=e.i18n||Ls({useScope:e.scope,__useComponent:!0});return vp(e,t,lp,(...r)=>n[ql](...r))}}),Nu=$1;function P1(e,t){const n=e;if(e.mode==="composition")return n.__getInstance(t)||e.global;{const r=n.__getInstance(t);return r!=null?r.__composer:e.global.__composer}}function O1(e){const t=s=>{const{instance:l,value:a}=s;if(!l||!l.$)throw Zt(jt.UNEXPECTED_ERROR);const c=P1(e,l.$),u=Du(a);return[Reflect.apply(c.t,c,[...zu(u)]),c]};return{created:(s,l)=>{const[a,c]=t(l);as&&e.global===c&&(s.__i18nWatcher=Ye(c.locale,()=>{l.instance&&l.instance.$forceUpdate()})),s.__composer=c,s.textContent=a},unmounted:s=>{as&&s.__i18nWatcher&&(s.__i18nWatcher(),s.__i18nWatcher=void 0,delete s.__i18nWatcher),s.__composer&&(s.__composer=void 0,delete s.__composer)},beforeUpdate:(s,{value:l})=>{if(s.__composer){const a=s.__composer,c=Du(l);s.textContent=Reflect.apply(a.t,a,[...zu(c)])}},getSSRProps:s=>{const[l]=t(s);return{textContent:l}}}}function Du(e){if(le(e))return{path:e};if(Ie(e)){if(!("path"in e))throw Zt(jt.REQUIRED_VALUE,"path");return e}else throw Zt(jt.INVALID_VALUE)}function zu(e){const{path:t,locale:n,args:r,choice:o,plural:i}=e,s={},l=r||{};return le(n)&&(s.locale=n),ht(o)&&(s.plural=o),ht(i)&&(s.plural=i),[t,l,s]}function k1(e,t,...n){const r=Ie(n[0])?n[0]:{};(Fe(r.globalInstall)?r.globalInstall:!0)&&([Mu.name,"I18nT"].forEach(i=>e.component(i,Mu)),[Fu.name,"I18nN"].forEach(i=>e.component(i,Fu)),[Nu.name,"I18nD"].forEach(i=>e.component(i,Nu))),e.directive("t",O1(t))}const A1=wr("global-vue-i18n");function I1(e={},t){const n=__VUE_I18N_LEGACY_API__&&Fe(e.legacy)?e.legacy:__VUE_I18N_LEGACY_API__,r=Fe(e.globalInjection)?e.globalInjection:!0,o=new Map,[i,s]=R1(e,n),l=wr("");function a(f){return o.get(f)||null}function c(f,m){o.set(f,m)}function u(f){o.delete(f)}const d={get mode(){return __VUE_I18N_LEGACY_API__&&n?"legacy":"composition"},async install(f,...m){if(f.__VUE_I18N_SYMBOL__=l,f.provide(f.__VUE_I18N_SYMBOL__,d),Ie(m[0])){const x=m[0];d.__composerExtend=x.__composerExtend,d.__vueI18nExtend=x.__vueI18nExtend}let p=null;!n&&r&&(p=H1(f,d.global)),__VUE_I18N_FULL_INSTALL__&&k1(f,d,...m),__VUE_I18N_LEGACY_API__&&n&&f.mixin(_1(s,s.__composer,d));const g=f.unmount;f.unmount=()=>{p&&p(),d.dispose(),g()}},get global(){return s},dispose(){i.stop()},__instances:o,__getInstance:a,__setInstance:c,__deleteInstance:u};return d}function Ls(e={}){const t=In();if(t==null)throw Zt(jt.MUST_BE_CALL_SETUP_TOP);if(!t.isCE&&t.appContext.app!=null&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw Zt(jt.NOT_INSTALLED);const n=L1(t),r=F1(n),o=hp(t),i=M1(e,o);if(i==="global")return pp(r,e,o),r;if(i==="parent"){let a=N1(n,t,e.__useComponent);return a==null&&(a=r),a}const s=n;let l=s.__getInstance(t);if(l==null){const a=_t({},e);"__i18n"in o&&(a.__i18n=o.__i18n),r&&(a.__root=r),l=Ua(a),s.__composerExtend&&(l[Jl]=s.__composerExtend(l)),z1(s,t,l),s.__setInstance(t,l)}return l}function R1(e,t,n){const r=Mv(),o=__VUE_I18N_LEGACY_API__&&t?r.run(()=>Ql(e)):r.run(()=>Ua(e));if(o==null)throw Zt(jt.UNEXPECTED_ERROR);return[r,o]}function L1(e){const t=Se(e.isCE?A1:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw Zt(e.isCE?jt.NOT_INSTALLED_WITH_PROVIDE:jt.UNEXPECTED_ERROR);return t}function M1(e,t){return As(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}function F1(e){return e.mode==="composition"?e.global:e.global.__composer}function N1(e,t,n=!1){let r=null;const o=t.root;let i=D1(t,n);for(;i!=null;){const s=e;if(e.mode==="composition")r=s.__getInstance(i);else if(__VUE_I18N_LEGACY_API__){const l=s.__getInstance(i);l!=null&&(r=l.__composer,n&&r&&!r[fp]&&(r=null))}if(r!=null||o===i)break;i=i.parent}return r}function D1(e,t=!1){return e==null?null:t&&e.vnode.ctx||e.parent}function z1(e,t,n){mt(()=>{},t),zr(()=>{const r=n;e.__deleteInstance(t);const o=r[Jl];o&&(o(),delete r[Jl])},t)}const B1=["locale","fallbackLocale","availableLocales"],Bu=["t","rt","d","n","tm","te"];function H1(e,t){const n=Object.create(null);return B1.forEach(o=>{const i=Object.getOwnPropertyDescriptor(t,o);if(!i)throw Zt(jt.UNEXPECTED_ERROR);const s=wt(i.value)?{get(){return i.value.value},set(l){i.value.value=l}}:{get(){return i.get&&i.get()}};Object.defineProperty(n,o,s)}),e.config.globalProperties.$i18n=n,Bu.forEach(o=>{const i=Object.getOwnPropertyDescriptor(t,o);if(!i||!i.value)throw Zt(jt.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${o}`,i)}),()=>{delete e.config.globalProperties.$i18n,Bu.forEach(o=>{delete e.config.globalProperties[`$${o}`]})}}b1();q0(A0);Z0(G0);J0(tp);if(__INTLIFY_PROD_DEVTOOLS__){const e=kr();e.__INTLIFY__=!0,I0(e.__INTLIFY_DEVTOOLS_GLOBAL_HOOK__)}function j1(e){let t=".",n="__",r="--",o;if(e){let p=e.blockPrefix;p&&(t=p),p=e.elementPrefix,p&&(n=p),p=e.modifierPrefix,p&&(r=p)}const i={install(p){o=p.c;const g=p.context;g.bem={},g.bem.b=null,g.bem.els=null}};function s(p){let g,x;return{before(h){g=h.bem.b,x=h.bem.els,h.bem.els=null},after(h){h.bem.b=g,h.bem.els=x},$({context:h,props:y}){return p=typeof p=="string"?p:p({context:h,props:y}),h.bem.b=p,`${(y==null?void 0:y.bPrefix)||t}${h.bem.b}`}}}function l(p){let g;return{before(x){g=x.bem.els},after(x){x.bem.els=g},$({context:x,props:h}){return p=typeof p=="string"?p:p({context:x,props:h}),x.bem.els=p.split(",").map(y=>y.trim()),x.bem.els.map(y=>`${(h==null?void 0:h.bPrefix)||t}${x.bem.b}${n}${y}`).join(", ")}}}function a(p){return{$({context:g,props:x}){p=typeof p=="string"?p:p({context:g,props:x});const h=p.split(",").map(b=>b.trim());function y(b){return h.map(C=>`&${(x==null?void 0:x.bPrefix)||t}${g.bem.b}${b!==void 0?`${n}${b}`:""}${r}${C}`).join(", ")}const P=g.bem.els;return P!==null?y(P[0]):y()}}}function c(p){return{$({context:g,props:x}){p=typeof p=="string"?p:p({context:g,props:x});const h=g.bem.els;return`&:not(${(x==null?void 0:x.bPrefix)||t}${g.bem.b}${h!==null&&h.length>0?`${n}${h[0]}`:""}${r}${p})`}}}return Object.assign(i,{cB:(...p)=>o(s(p[0]),p[1],p[2]),cE:(...p)=>o(l(p[0]),p[1],p[2]),cM:(...p)=>o(a(p[0]),p[1],p[2]),cNotM:(...p)=>o(c(p[0]),p[1],p[2])}),i}function W1(e){let t=0;for(let n=0;n<e.length;++n)e[n]==="&"&&++t;return t}const mp=/\s*,(?![^(]*\))\s*/g,U1=/\s+/g;function V1(e,t){const n=[];return t.split(mp).forEach(r=>{let o=W1(r);if(o){if(o===1){e.forEach(s=>{n.push(r.replace("&",s))});return}}else{e.forEach(s=>{n.push((s&&s+" ")+r)});return}let i=[r];for(;o--;){const s=[];i.forEach(l=>{e.forEach(a=>{s.push(l.replace("&",a))})}),i=s}i.forEach(s=>n.push(s))}),n}function K1(e,t){const n=[];return t.split(mp).forEach(r=>{e.forEach(o=>{n.push((o&&o+" ")+r)})}),n}function G1(e){let t=[""];return e.forEach(n=>{n=n&&n.trim(),n&&(n.includes("&")?t=V1(t,n):t=K1(t,n))}),t.join(", ").replace(U1," ")}function Hu(e){if(!e)return;const t=e.parentElement;t&&t.removeChild(e)}function Ms(e,t){return(t??document.head).querySelector(`style[cssr-id="${e}"]`)}function Y1(e){const t=document.createElement("style");return t.setAttribute("cssr-id",e),t}function Pi(e){return e?/^\s*@(s|m)/.test(e):!1}const X1=/[A-Z]/g;function bp(e){return e.replace(X1,t=>"-"+t.toLowerCase())}function q1(e,t="  "){return typeof e=="object"&&e!==null?` {
`+Object.entries(e).map(n=>t+`  ${bp(n[0])}: ${n[1]};`).join(`
`)+`
`+t+"}":`: ${e};`}function Z1(e,t,n){return typeof e=="function"?e({context:t.context,props:n}):e}function ju(e,t,n,r){if(!t)return"";const o=Z1(t,n,r);if(!o)return"";if(typeof o=="string")return`${e} {
${o}
}`;const i=Object.keys(o);if(i.length===0)return n.config.keepEmptyBlock?e+` {
}`:"";const s=e?[e+" {"]:[];return i.forEach(l=>{const a=o[l];if(l==="raw"){s.push(`
`+a+`
`);return}l=bp(l),a!=null&&s.push(`  ${l}${q1(a)}`)}),e&&s.push("}"),s.join(`
`)}function ea(e,t,n){e&&e.forEach(r=>{if(Array.isArray(r))ea(r,t,n);else if(typeof r=="function"){const o=r(t);Array.isArray(o)?ea(o,t,n):o&&n(o)}else r&&n(r)})}function yp(e,t,n,r,o){const i=e.$;let s="";if(!i||typeof i=="string")Pi(i)?s=i:t.push(i);else if(typeof i=="function"){const c=i({context:r.context,props:o});Pi(c)?s=c:t.push(c)}else if(i.before&&i.before(r.context),!i.$||typeof i.$=="string")Pi(i.$)?s=i.$:t.push(i.$);else if(i.$){const c=i.$({context:r.context,props:o});Pi(c)?s=c:t.push(c)}const l=G1(t),a=ju(l,e.props,r,o);s?n.push(`${s} {`):a.length&&n.push(a),e.children&&ea(e.children,{context:r.context,props:o},c=>{if(typeof c=="string"){const u=ju(l,{raw:c},r,o);n.push(u)}else yp(c,t,n,r,o)}),t.pop(),s&&n.push("}"),i&&i.after&&i.after(r.context)}function J1(e,t,n){const r=[];return yp(e,[],r,t,n),r.join(`

`)}function ri(e){for(var t=0,n,r=0,o=e.length;o>=4;++r,o-=4)n=e.charCodeAt(r)&255|(e.charCodeAt(++r)&255)<<8|(e.charCodeAt(++r)&255)<<16|(e.charCodeAt(++r)&255)<<24,n=(n&65535)*1540483477+((n>>>16)*59797<<16),n^=n>>>24,t=(n&65535)*1540483477+((n>>>16)*59797<<16)^(t&65535)*1540483477+((t>>>16)*59797<<16);switch(o){case 3:t^=(e.charCodeAt(r+2)&255)<<16;case 2:t^=(e.charCodeAt(r+1)&255)<<8;case 1:t^=e.charCodeAt(r)&255,t=(t&65535)*1540483477+((t>>>16)*59797<<16)}return t^=t>>>13,t=(t&65535)*1540483477+((t>>>16)*59797<<16),((t^t>>>15)>>>0).toString(36)}typeof window<"u"&&(window.__cssrContext={});function Q1(e,t,n,r){const{els:o}=t;if(n===void 0)o.forEach(Hu),t.els=[];else{const i=Ms(n,r);i&&o.includes(i)&&(Hu(i),t.els=o.filter(s=>s!==i))}}function Wu(e,t){e.push(t)}function ey(e,t,n,r,o,i,s,l,a){let c;if(n===void 0&&(c=t.render(r),n=ri(c)),a){a.adapter(n,c??t.render(r));return}l===void 0&&(l=document.head);const u=Ms(n,l);if(u!==null&&!i)return u;const d=u??Y1(n);if(c===void 0&&(c=t.render(r)),d.textContent=c,u!==null)return u;if(s){const f=l.querySelector(`meta[name="${s}"]`);if(f)return l.insertBefore(d,f),Wu(t.els,d),d}return o?l.insertBefore(d,l.querySelector("style, link")):l.appendChild(d),Wu(t.els,d),d}function ty(e){return J1(this,this.instance,e)}function ny(e={}){const{id:t,ssr:n,props:r,head:o=!1,force:i=!1,anchorMetaName:s,parent:l}=e;return ey(this.instance,this,t,r,o,i,s,l,n)}function ry(e={}){const{id:t,parent:n}=e;Q1(this.instance,this,t,n)}const Oi=function(e,t,n,r){return{instance:e,$:t,props:n,children:r,els:[],render:ty,mount:ny,unmount:ry}},oy=function(e,t,n,r){return Array.isArray(t)?Oi(e,{$:null},null,t):Array.isArray(n)?Oi(e,t,null,n):Array.isArray(r)?Oi(e,t,n,r):Oi(e,t,n,null)};function xp(e={}){const t={c:(...n)=>oy(t,...n),use:(n,...r)=>n.install(t,...r),find:Ms,context:{},config:e};return t}function iy(e,t){if(e===void 0)return!1;if(t){const{context:{ids:n}}=t;return n.has(e)}return Ms(e)!==null}const sy="n",oi=`.${sy}-`,ly="__",ay="--",wp=xp(),_p=j1({blockPrefix:oi,elementPrefix:ly,modifierPrefix:ay});wp.use(_p);const{c:U,find:U3}=wp,{cB:oe,cE:Z,cM:X,cNotM:ur}=_p;function Cp(e){return U(({props:{bPrefix:t}})=>`${t||oi}modal, ${t||oi}drawer`,[e])}function cy(e){return U(({props:{bPrefix:t}})=>`${t||oi}popover`,[e])}function Sp(e){return U(({props:{bPrefix:t}})=>`&${t||oi}modal`,e)}const uy=(...e)=>U(">",[oe(...e)]);function he(e,t){return e+(t==="default"?"":t.replace(/^[a-z]/,n=>n.toUpperCase()))}let cs=[];const Ep=new WeakMap;function dy(){cs.forEach(e=>e(...Ep.get(e))),cs=[]}function fy(e,...t){Ep.set(e,t),!cs.includes(e)&&cs.push(e)===1&&requestAnimationFrame(dy)}function Uu(e,t){let{target:n}=e;for(;n;){if(n.dataset&&n.dataset[t]!==void 0)return!0;n=n.parentElement}return!1}function xo(e){return e.composedPath()[0]||null}function Vu(e){return typeof e=="string"?e.endsWith("px")?Number(e.slice(0,e.length-2)):Number(e):e}function lr(e,t){const n=e.trim().split(/\s+/g),r={top:n[0]};switch(n.length){case 1:r.right=n[0],r.bottom=n[0],r.left=n[0];break;case 2:r.right=n[1],r.left=n[1],r.bottom=n[0];break;case 3:r.right=n[1],r.bottom=n[2],r.left=n[1];break;case 4:r.right=n[1],r.bottom=n[2],r.left=n[3];break;default:throw new Error("[seemly/getMargin]:"+e+" is not a valid value.")}return r}const Ku={aliceblue:"#F0F8FF",antiquewhite:"#FAEBD7",aqua:"#0FF",aquamarine:"#7FFFD4",azure:"#F0FFFF",beige:"#F5F5DC",bisque:"#FFE4C4",black:"#000",blanchedalmond:"#FFEBCD",blue:"#00F",blueviolet:"#8A2BE2",brown:"#A52A2A",burlywood:"#DEB887",cadetblue:"#5F9EA0",chartreuse:"#7FFF00",chocolate:"#D2691E",coral:"#FF7F50",cornflowerblue:"#6495ED",cornsilk:"#FFF8DC",crimson:"#DC143C",cyan:"#0FF",darkblue:"#00008B",darkcyan:"#008B8B",darkgoldenrod:"#B8860B",darkgray:"#A9A9A9",darkgrey:"#A9A9A9",darkgreen:"#006400",darkkhaki:"#BDB76B",darkmagenta:"#8B008B",darkolivegreen:"#556B2F",darkorange:"#FF8C00",darkorchid:"#9932CC",darkred:"#8B0000",darksalmon:"#E9967A",darkseagreen:"#8FBC8F",darkslateblue:"#483D8B",darkslategray:"#2F4F4F",darkslategrey:"#2F4F4F",darkturquoise:"#00CED1",darkviolet:"#9400D3",deeppink:"#FF1493",deepskyblue:"#00BFFF",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1E90FF",firebrick:"#B22222",floralwhite:"#FFFAF0",forestgreen:"#228B22",fuchsia:"#F0F",gainsboro:"#DCDCDC",ghostwhite:"#F8F8FF",gold:"#FFD700",goldenrod:"#DAA520",gray:"#808080",grey:"#808080",green:"#008000",greenyellow:"#ADFF2F",honeydew:"#F0FFF0",hotpink:"#FF69B4",indianred:"#CD5C5C",indigo:"#4B0082",ivory:"#FFFFF0",khaki:"#F0E68C",lavender:"#E6E6FA",lavenderblush:"#FFF0F5",lawngreen:"#7CFC00",lemonchiffon:"#FFFACD",lightblue:"#ADD8E6",lightcoral:"#F08080",lightcyan:"#E0FFFF",lightgoldenrodyellow:"#FAFAD2",lightgray:"#D3D3D3",lightgrey:"#D3D3D3",lightgreen:"#90EE90",lightpink:"#FFB6C1",lightsalmon:"#FFA07A",lightseagreen:"#20B2AA",lightskyblue:"#87CEFA",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#B0C4DE",lightyellow:"#FFFFE0",lime:"#0F0",limegreen:"#32CD32",linen:"#FAF0E6",magenta:"#F0F",maroon:"#800000",mediumaquamarine:"#66CDAA",mediumblue:"#0000CD",mediumorchid:"#BA55D3",mediumpurple:"#9370DB",mediumseagreen:"#3CB371",mediumslateblue:"#7B68EE",mediumspringgreen:"#00FA9A",mediumturquoise:"#48D1CC",mediumvioletred:"#C71585",midnightblue:"#191970",mintcream:"#F5FFFA",mistyrose:"#FFE4E1",moccasin:"#FFE4B5",navajowhite:"#FFDEAD",navy:"#000080",oldlace:"#FDF5E6",olive:"#808000",olivedrab:"#6B8E23",orange:"#FFA500",orangered:"#FF4500",orchid:"#DA70D6",palegoldenrod:"#EEE8AA",palegreen:"#98FB98",paleturquoise:"#AFEEEE",palevioletred:"#DB7093",papayawhip:"#FFEFD5",peachpuff:"#FFDAB9",peru:"#CD853F",pink:"#FFC0CB",plum:"#DDA0DD",powderblue:"#B0E0E6",purple:"#800080",rebeccapurple:"#663399",red:"#F00",rosybrown:"#BC8F8F",royalblue:"#4169E1",saddlebrown:"#8B4513",salmon:"#FA8072",sandybrown:"#F4A460",seagreen:"#2E8B57",seashell:"#FFF5EE",sienna:"#A0522D",silver:"#C0C0C0",skyblue:"#87CEEB",slateblue:"#6A5ACD",slategray:"#708090",slategrey:"#708090",snow:"#FFFAFA",springgreen:"#00FF7F",steelblue:"#4682B4",tan:"#D2B48C",teal:"#008080",thistle:"#D8BFD8",tomato:"#FF6347",turquoise:"#40E0D0",violet:"#EE82EE",wheat:"#F5DEB3",white:"#FFF",whitesmoke:"#F5F5F5",yellow:"#FF0",yellowgreen:"#9ACD32",transparent:"#0000"};function hy(e,t,n){t/=100,n/=100;let r=(o,i=(o+e/60)%6)=>n-n*t*Math.max(Math.min(i,4-i,1),0);return[r(5)*255,r(3)*255,r(1)*255]}function py(e,t,n){t/=100,n/=100;let r=t*Math.min(n,1-n),o=(i,s=(i+e/30)%12)=>n-r*Math.max(Math.min(s-3,9-s,1),-1);return[o(0)*255,o(8)*255,o(4)*255]}const Rn="^\\s*",Ln="\\s*$",fr="\\s*((\\.\\d+)|(\\d+(\\.\\d*)?))%\\s*",en="\\s*((\\.\\d+)|(\\d+(\\.\\d*)?))\\s*",Ar="([0-9A-Fa-f])",Ir="([0-9A-Fa-f]{2})",Tp=new RegExp(`${Rn}hsl\\s*\\(${en},${fr},${fr}\\)${Ln}`),$p=new RegExp(`${Rn}hsv\\s*\\(${en},${fr},${fr}\\)${Ln}`),Pp=new RegExp(`${Rn}hsla\\s*\\(${en},${fr},${fr},${en}\\)${Ln}`),Op=new RegExp(`${Rn}hsva\\s*\\(${en},${fr},${fr},${en}\\)${Ln}`),gy=new RegExp(`${Rn}rgb\\s*\\(${en},${en},${en}\\)${Ln}`),vy=new RegExp(`${Rn}rgba\\s*\\(${en},${en},${en},${en}\\)${Ln}`),my=new RegExp(`${Rn}#${Ar}${Ar}${Ar}${Ln}`),by=new RegExp(`${Rn}#${Ir}${Ir}${Ir}${Ln}`),yy=new RegExp(`${Rn}#${Ar}${Ar}${Ar}${Ar}${Ln}`),xy=new RegExp(`${Rn}#${Ir}${Ir}${Ir}${Ir}${Ln}`);function Vt(e){return parseInt(e,16)}function wy(e){try{let t;if(t=Pp.exec(e))return[us(t[1]),ar(t[5]),ar(t[9]),Lr(t[13])];if(t=Tp.exec(e))return[us(t[1]),ar(t[5]),ar(t[9]),1];throw new Error(`[seemly/hsla]: Invalid color value ${e}.`)}catch(t){throw t}}function _y(e){try{let t;if(t=Op.exec(e))return[us(t[1]),ar(t[5]),ar(t[9]),Lr(t[13])];if(t=$p.exec(e))return[us(t[1]),ar(t[5]),ar(t[9]),1];throw new Error(`[seemly/hsva]: Invalid color value ${e}.`)}catch(t){throw t}}function Br(e){try{let t;if(t=by.exec(e))return[Vt(t[1]),Vt(t[2]),Vt(t[3]),1];if(t=gy.exec(e))return[Mt(t[1]),Mt(t[5]),Mt(t[9]),1];if(t=vy.exec(e))return[Mt(t[1]),Mt(t[5]),Mt(t[9]),Lr(t[13])];if(t=my.exec(e))return[Vt(t[1]+t[1]),Vt(t[2]+t[2]),Vt(t[3]+t[3]),1];if(t=xy.exec(e))return[Vt(t[1]),Vt(t[2]),Vt(t[3]),Lr(Vt(t[4])/255)];if(t=yy.exec(e))return[Vt(t[1]+t[1]),Vt(t[2]+t[2]),Vt(t[3]+t[3]),Lr(Vt(t[4]+t[4])/255)];if(e in Ku)return Br(Ku[e]);if(Tp.test(e)||Pp.test(e)){const[n,r,o,i]=wy(e);return[...py(n,r,o),i]}else if($p.test(e)||Op.test(e)){const[n,r,o,i]=_y(e);return[...hy(n,r,o),i]}throw new Error(`[seemly/rgba]: Invalid color value ${e}.`)}catch(t){throw t}}function Cy(e){return e>1?1:e<0?0:e}function ta(e,t,n,r){return`rgba(${Mt(e)}, ${Mt(t)}, ${Mt(n)}, ${Cy(r)})`}function hl(e,t,n,r,o){return Mt((e*t*(1-r)+n*r)/o)}function Ka(e,t){Array.isArray(e)||(e=Br(e)),Array.isArray(t)||(t=Br(t));const n=e[3],r=t[3],o=Lr(n+r-n*r);return ta(hl(e[0],n,t[0],r,o),hl(e[1],n,t[1],r,o),hl(e[2],n,t[2],r,o),o)}function Lo(e,t){const[n,r,o,i=1]=Array.isArray(e)?e:Br(e);return typeof t.alpha=="number"?ta(n,r,o,t.alpha):ta(n,r,o,i)}function ki(e,t){const[n,r,o,i=1]=Array.isArray(e)?e:Br(e),{lightness:s=1,alpha:l=1}=t;return Sy([n*s,r*s,o*s,i*l])}function Lr(e){const t=Math.round(Number(e)*100)/100;return t>1?1:t<0?0:t}function us(e){const t=Math.round(Number(e));return t>=360||t<0?0:t}function Mt(e){const t=Math.round(Number(e));return t>255?255:t<0?0:t}function ar(e){const t=Math.round(Number(e));return t>100?100:t<0?0:t}function Sy(e){const[t,n,r]=e;return 3 in e?`rgba(${Mt(t)}, ${Mt(n)}, ${Mt(r)}, ${Lr(e[3])})`:`rgba(${Mt(t)}, ${Mt(n)}, ${Mt(r)}, 1)`}function fi(e=8){return Math.random().toString(16).slice(2,2+e)}function Ey(e,t){const n=[];if(!t){for(let r=0;r<e;++r)n.push(r);return n}for(let r=0;r<e;++r)n.push(t(r));return n}function Xi(e){return e.composedPath()[0]}const Ty={mousemoveoutside:new WeakMap,clickoutside:new WeakMap};function $y(e,t,n){if(e==="mousemoveoutside"){const r=o=>{t.contains(Xi(o))||n(o)};return{mousemove:r,touchstart:r}}else if(e==="clickoutside"){let r=!1;const o=s=>{r=!t.contains(Xi(s))},i=s=>{r&&(t.contains(Xi(s))||n(s))};return{mousedown:o,mouseup:i,touchstart:o,touchend:i}}return console.error(`[evtd/create-trap-handler]: name \`${e}\` is invalid. This could be a bug of evtd.`),{}}function kp(e,t,n){const r=Ty[e];let o=r.get(t);o===void 0&&r.set(t,o=new WeakMap);let i=o.get(n);return i===void 0&&o.set(n,i=$y(e,t,n)),i}function Py(e,t,n,r){if(e==="mousemoveoutside"||e==="clickoutside"){const o=kp(e,t,n);return Object.keys(o).forEach(i=>{De(i,document,o[i],r)}),!0}return!1}function Oy(e,t,n,r){if(e==="mousemoveoutside"||e==="clickoutside"){const o=kp(e,t,n);return Object.keys(o).forEach(i=>{Ne(i,document,o[i],r)}),!0}return!1}function ky(){if(typeof window>"u")return{on:()=>{},off:()=>{}};const e=new WeakMap,t=new WeakMap;function n(){e.set(this,!0)}function r(){e.set(this,!0),t.set(this,!0)}function o(v,E,A){const F=v[E];return v[E]=function(){return A.apply(v,arguments),F.apply(v,arguments)},v}function i(v,E){v[E]=Event.prototype[E]}const s=new WeakMap,l=Object.getOwnPropertyDescriptor(Event.prototype,"currentTarget");function a(){var v;return(v=s.get(this))!==null&&v!==void 0?v:null}function c(v,E){l!==void 0&&Object.defineProperty(v,"currentTarget",{configurable:!0,enumerable:!0,get:E??l.get})}const u={bubble:{},capture:{}},d={};function f(){const v=function(E){const{type:A,eventPhase:F,bubbles:B}=E,R=Xi(E);if(F===2)return;const j=F===1?"capture":"bubble";let Q=R;const H=[];for(;Q===null&&(Q=window),H.push(Q),Q!==window;)Q=Q.parentNode||null;const te=u.capture[A],W=u.bubble[A];if(o(E,"stopPropagation",n),o(E,"stopImmediatePropagation",r),c(E,a),j==="capture"){if(te===void 0)return;for(let re=H.length-1;re>=0&&!e.has(E);--re){const ue=H[re],pe=te.get(ue);if(pe!==void 0){s.set(E,ue);for(const we of pe){if(t.has(E))break;we(E)}}if(re===0&&!B&&W!==void 0){const we=W.get(ue);if(we!==void 0)for(const Te of we){if(t.has(E))break;Te(E)}}}}else if(j==="bubble"){if(W===void 0)return;for(let re=0;re<H.length&&!e.has(E);++re){const ue=H[re],pe=W.get(ue);if(pe!==void 0){s.set(E,ue);for(const we of pe){if(t.has(E))break;we(E)}}}}i(E,"stopPropagation"),i(E,"stopImmediatePropagation"),c(E)};return v.displayName="evtdUnifiedHandler",v}function m(){const v=function(E){const{type:A,eventPhase:F}=E;if(F!==2)return;const B=d[A];B!==void 0&&B.forEach(R=>R(E))};return v.displayName="evtdUnifiedWindowEventHandler",v}const p=f(),g=m();function x(v,E){const A=u[v];return A[E]===void 0&&(A[E]=new Map,window.addEventListener(E,p,v==="capture")),A[E]}function h(v){return d[v]===void 0&&(d[v]=new Set,window.addEventListener(v,g)),d[v]}function y(v,E){let A=v.get(E);return A===void 0&&v.set(E,A=new Set),A}function P(v,E,A,F){const B=u[E][A];if(B!==void 0){const R=B.get(v);if(R!==void 0&&R.has(F))return!0}return!1}function b(v,E){const A=d[v];return!!(A!==void 0&&A.has(E))}function C(v,E,A,F){let B;if(typeof F=="object"&&F.once===!0?B=te=>{S(v,E,B,F),A(te)}:B=A,Py(v,E,B,F))return;const j=F===!0||typeof F=="object"&&F.capture===!0?"capture":"bubble",Q=x(j,v),H=y(Q,E);if(H.has(B)||H.add(B),E===window){const te=h(v);te.has(B)||te.add(B)}}function S(v,E,A,F){if(Oy(v,E,A,F))return;const R=F===!0||typeof F=="object"&&F.capture===!0,j=R?"capture":"bubble",Q=x(j,v),H=y(Q,E);if(E===window&&!P(E,R?"bubble":"capture",v,A)&&b(v,A)){const W=d[v];W.delete(A),W.size===0&&(window.removeEventListener(v,g),d[v]=void 0)}H.has(A)&&H.delete(A),H.size===0&&Q.delete(E),Q.size===0&&(window.removeEventListener(v,p,j==="capture"),u[j][v]=void 0)}return{on:C,off:S}}const{on:De,off:Ne}=ky();function Ay(e){const t=D(!!e.value);if(t.value)return An(t);const n=Ye(e,r=>{r&&(t.value=!0,n())});return An(t)}function an(e){const t=V(e),n=D(t.value);return Ye(t,r=>{n.value=r}),typeof e=="function"?n:{__v_isRef:!0,get value(){return n.value},set value(r){e.set(r)}}}function Ga(){return In()!==null}const Ya=typeof window<"u";let ho,Wo;const Iy=()=>{var e,t;ho=Ya?(t=(e=document)===null||e===void 0?void 0:e.fonts)===null||t===void 0?void 0:t.ready:void 0,Wo=!1,ho!==void 0?ho.then(()=>{Wo=!0}):Wo=!0};Iy();function Ry(e){if(Wo)return;let t=!1;mt(()=>{Wo||ho==null||ho.then(()=>{t||e()})}),pt(()=>{t=!0})}const Mo=D(null);function Gu(e){if(e.clientX>0||e.clientY>0)Mo.value={x:e.clientX,y:e.clientY};else{const{target:t}=e;if(t instanceof Element){const{left:n,top:r,width:o,height:i}=t.getBoundingClientRect();n>0||r>0?Mo.value={x:n+o/2,y:r+i/2}:Mo.value={x:0,y:0}}else Mo.value=null}}let Ai=0,Yu=!0;function Xa(){if(!Ya)return An(D(null));Ai===0&&De("click",document,Gu,!0);const e=()=>{Ai+=1};return Yu&&(Yu=Ga())?(xr(e),pt(()=>{Ai-=1,Ai===0&&Ne("click",document,Gu,!0)})):e(),An(Mo)}const Ly=D(void 0);let Ii=0;function Xu(){Ly.value=Date.now()}let qu=!0;function qa(e){if(!Ya)return An(D(!1));const t=D(!1);let n=null;function r(){n!==null&&window.clearTimeout(n)}function o(){r(),t.value=!0,n=window.setTimeout(()=>{t.value=!1},e)}Ii===0&&De("click",window,Xu,!0);const i=()=>{Ii+=1,De("click",window,o,!0)};return qu&&(qu=Ga())?(xr(i),pt(()=>{Ii-=1,Ii===0&&Ne("click",window,Xu,!0),Ne("click",window,o,!0),r()})):i(),An(t)}function Za(e,t){return Ye(e,n=>{n!==void 0&&(t.value=n)}),V(()=>e.value===void 0?t.value:e.value)}function hi(){const e=D(!1);return mt(()=>{e.value=!0}),An(e)}function My(e,t){return V(()=>{for(const n of t)if(e[n]!==void 0)return e[n];return e[t[t.length-1]]})}const Fy=(typeof window>"u"?!1:/iPad|iPhone|iPod/.test(navigator.platform)||navigator.platform==="MacIntel"&&navigator.maxTouchPoints>1)&&!window.MSStream;function Ny(){return Fy}function Dy(e={},t){const n=yr({ctrl:!1,command:!1,win:!1,shift:!1,tab:!1}),{keydown:r,keyup:o}=e,i=a=>{switch(a.key){case"Control":n.ctrl=!0;break;case"Meta":n.command=!0,n.win=!0;break;case"Shift":n.shift=!0;break;case"Tab":n.tab=!0;break}r!==void 0&&Object.keys(r).forEach(c=>{if(c!==a.key)return;const u=r[c];if(typeof u=="function")u(a);else{const{stop:d=!1,prevent:f=!1}=u;d&&a.stopPropagation(),f&&a.preventDefault(),u.handler(a)}})},s=a=>{switch(a.key){case"Control":n.ctrl=!1;break;case"Meta":n.command=!1,n.win=!1;break;case"Shift":n.shift=!1;break;case"Tab":n.tab=!1;break}o!==void 0&&Object.keys(o).forEach(c=>{if(c!==a.key)return;const u=o[c];if(typeof u=="function")u(a);else{const{stop:d=!1,prevent:f=!1}=u;d&&a.stopPropagation(),f&&a.preventDefault(),u.handler(a)}})},l=()=>{(t===void 0||t.value)&&(De("keydown",document,i),De("keyup",document,s)),t!==void 0&&Ye(t,a=>{a?(De("keydown",document,i),De("keyup",document,s)):(Ne("keydown",document,i),Ne("keyup",document,s))})};return Ga()?(xr(l),pt(()=>{(t===void 0||t.value)&&(Ne("keydown",document,i),Ne("keyup",document,s))})):l(),An(n)}const zy="n-internal-select-menu-body",Fs="n-drawer-body",Ns="n-modal-body",By="n-modal-provider",Ap="n-modal",pi="n-popover-body",Ip="__disabled__";function wo(e){const t=Se(Ns,null),n=Se(Fs,null),r=Se(pi,null),o=Se(zy,null),i=D();if(typeof document<"u"){i.value=document.fullscreenElement;const s=()=>{i.value=document.fullscreenElement};mt(()=>{De("fullscreenchange",document,s)}),pt(()=>{Ne("fullscreenchange",document,s)})}return an(()=>{var s;const{to:l}=e;return l!==void 0?l===!1?Ip:l===!0?i.value||"body":l:t!=null&&t.value?(s=t.value.$el)!==null&&s!==void 0?s:t.value:n!=null&&n.value?n.value:r!=null&&r.value?r.value:o!=null&&o.value?o.value:l??(i.value||"body")})}wo.tdkey=Ip;wo.propTo={type:[String,Object,Boolean],default:void 0};function Hy(e,t,n){const r=D(e.value);let o=null;return Ye(e,i=>{o!==null&&window.clearTimeout(o),i===!0?n&&!n.value?r.value=!0:o=window.setTimeout(()=>{r.value=!0},t):r.value=!1}),r}const Co=typeof document<"u"&&typeof window<"u",Ja=D(!1);function Zu(){Ja.value=!0}function Ju(){Ja.value=!1}let ko=0;function jy(){return Co&&(xr(()=>{ko||(window.addEventListener("compositionstart",Zu),window.addEventListener("compositionend",Ju)),ko++}),pt(()=>{ko<=1?(window.removeEventListener("compositionstart",Zu),window.removeEventListener("compositionend",Ju),ko=0):ko--})),Ja}let Jr=0,Qu="",ed="",td="",nd="";const rd=D("0px");function Wy(e){if(typeof document>"u")return;const t=document.documentElement;let n,r=!1;const o=()=>{t.style.marginRight=Qu,t.style.overflow=ed,t.style.overflowX=td,t.style.overflowY=nd,rd.value="0px"};mt(()=>{n=Ye(e,i=>{if(i){if(!Jr){const s=window.innerWidth-t.offsetWidth;s>0&&(Qu=t.style.marginRight,t.style.marginRight=`${s}px`,rd.value=`${s}px`),ed=t.style.overflow,td=t.style.overflowX,nd=t.style.overflowY,t.style.overflow="hidden",t.style.overflowX="hidden",t.style.overflowY="hidden"}r=!0,Jr++}else Jr--,Jr||o(),r=!1},{immediate:!0})}),pt(()=>{n==null||n(),r&&(Jr--,Jr||o(),r=!1)})}function Uy(e){const t={isDeactivated:!1};let n=!1;return uh(()=>{if(t.isDeactivated=!1,!n){n=!0;return}e()}),dh(()=>{t.isDeactivated=!0,n||(n=!0)}),t}function na(e,t,n="default"){const r=t[n];if(r===void 0)throw new Error(`[vueuc/${e}]: slot[${n}] is empty.`);return r()}function ra(e,t=!0,n=[]){return e.forEach(r=>{if(r!==null){if(typeof r!="object"){(typeof r=="string"||typeof r=="number")&&n.push(vo(String(r)));return}if(Array.isArray(r)){ra(r,t,n);return}if(r.type===We){if(r.children===null)return;Array.isArray(r.children)&&ra(r.children,t,n)}else r.type!==$t&&n.push(r)}}),n}function od(e,t,n="default"){const r=t[n];if(r===void 0)throw new Error(`[vueuc/${e}]: slot[${n}] is empty.`);const o=ra(r());if(o.length===1)return o[0];throw new Error(`[vueuc/${e}]: slot[${n}] should have exactly one child.`)}let Qn=null;function Rp(){if(Qn===null&&(Qn=document.getElementById("v-binder-view-measurer"),Qn===null)){Qn=document.createElement("div"),Qn.id="v-binder-view-measurer";const{style:e}=Qn;e.position="fixed",e.left="0",e.right="0",e.top="0",e.bottom="0",e.pointerEvents="none",e.visibility="hidden",document.body.appendChild(Qn)}return Qn.getBoundingClientRect()}function Vy(e,t){const n=Rp();return{top:t,left:e,height:0,width:0,right:n.width-e,bottom:n.height-t}}function pl(e){const t=e.getBoundingClientRect(),n=Rp();return{left:t.left-n.left,top:t.top-n.top,bottom:n.height+n.top-t.bottom,right:n.width+n.left-t.right,width:t.width,height:t.height}}function Ky(e){return e.nodeType===9?null:e.parentNode}function Lp(e){if(e===null)return null;const t=Ky(e);if(t===null)return null;if(t.nodeType===9)return document;if(t.nodeType===1){const{overflow:n,overflowX:r,overflowY:o}=getComputedStyle(t);if(/(auto|scroll|overlay)/.test(n+o+r))return t}return Lp(t)}const Mp=ge({name:"Binder",props:{syncTargetWithParent:Boolean,syncTarget:{type:Boolean,default:!0}},setup(e){var t;ze("VBinder",(t=In())===null||t===void 0?void 0:t.proxy);const n=Se("VBinder",null),r=D(null),o=h=>{r.value=h,n&&e.syncTargetWithParent&&n.setTargetRef(h)};let i=[];const s=()=>{let h=r.value;for(;h=Lp(h),h!==null;)i.push(h);for(const y of i)De("scroll",y,d,!0)},l=()=>{for(const h of i)Ne("scroll",h,d,!0);i=[]},a=new Set,c=h=>{a.size===0&&s(),a.has(h)||a.add(h)},u=h=>{a.has(h)&&a.delete(h),a.size===0&&l()},d=()=>{fy(f)},f=()=>{a.forEach(h=>h())},m=new Set,p=h=>{m.size===0&&De("resize",window,x),m.has(h)||m.add(h)},g=h=>{m.has(h)&&m.delete(h),m.size===0&&Ne("resize",window,x)},x=()=>{m.forEach(h=>h())};return pt(()=>{Ne("resize",window,x),l()}),{targetRef:r,setTargetRef:o,addScrollListener:c,removeScrollListener:u,addResizeListener:p,removeResizeListener:g}},render(){return na("binder",this.$slots)}}),Fp=ge({name:"Target",setup(){const{setTargetRef:e,syncTarget:t}=Se("VBinder");return{syncTarget:t,setTargetDirective:{mounted:e,updated:e}}},render(){const{syncTarget:e,setTargetDirective:t}=this;return e?Un(od("follower",this.$slots),[[t]]):od("follower",this.$slots)}}),Qr="@@mmoContext",Gy={mounted(e,{value:t}){e[Qr]={handler:void 0},typeof t=="function"&&(e[Qr].handler=t,De("mousemoveoutside",e,t))},updated(e,{value:t}){const n=e[Qr];typeof t=="function"?n.handler?n.handler!==t&&(Ne("mousemoveoutside",e,n.handler),n.handler=t,De("mousemoveoutside",e,t)):(e[Qr].handler=t,De("mousemoveoutside",e,t)):n.handler&&(Ne("mousemoveoutside",e,n.handler),n.handler=void 0)},unmounted(e){const{handler:t}=e[Qr];t&&Ne("mousemoveoutside",e,t),e[Qr].handler=void 0}},eo="@@coContext",oa={mounted(e,{value:t,modifiers:n}){e[eo]={handler:void 0},typeof t=="function"&&(e[eo].handler=t,De("clickoutside",e,t,{capture:n.capture}))},updated(e,{value:t,modifiers:n}){const r=e[eo];typeof t=="function"?r.handler?r.handler!==t&&(Ne("clickoutside",e,r.handler,{capture:n.capture}),r.handler=t,De("clickoutside",e,t,{capture:n.capture})):(e[eo].handler=t,De("clickoutside",e,t,{capture:n.capture})):r.handler&&(Ne("clickoutside",e,r.handler,{capture:n.capture}),r.handler=void 0)},unmounted(e,{modifiers:t}){const{handler:n}=e[eo];n&&Ne("clickoutside",e,n,{capture:t.capture}),e[eo].handler=void 0}};function Yy(e,t){console.error(`[vdirs/${e}]: ${t}`)}class Xy{constructor(){this.elementZIndex=new Map,this.nextZIndex=2e3}get elementCount(){return this.elementZIndex.size}ensureZIndex(t,n){const{elementZIndex:r}=this;if(n!==void 0){t.style.zIndex=`${n}`,r.delete(t);return}const{nextZIndex:o}=this;r.has(t)&&r.get(t)+1===this.nextZIndex||(t.style.zIndex=`${o}`,r.set(t,o),this.nextZIndex=o+1,this.squashState())}unregister(t,n){const{elementZIndex:r}=this;r.has(t)?r.delete(t):n===void 0&&Yy("z-index-manager/unregister-element","Element not found when unregistering."),this.squashState()}squashState(){const{elementCount:t}=this;t||(this.nextZIndex=2e3),this.nextZIndex-t>2500&&this.rearrange()}rearrange(){const t=Array.from(this.elementZIndex.entries());t.sort((n,r)=>n[1]-r[1]),this.nextZIndex=2e3,t.forEach(n=>{const r=n[0],o=this.nextZIndex++;`${o}`!==r.style.zIndex&&(r.style.zIndex=`${o}`)})}}const gl=new Xy,to="@@ziContext",Qa={mounted(e,t){const{value:n={}}=t,{zIndex:r,enabled:o}=n;e[to]={enabled:!!o,initialized:!1},o&&(gl.ensureZIndex(e,r),e[to].initialized=!0)},updated(e,t){const{value:n={}}=t,{zIndex:r,enabled:o}=n,i=e[to].enabled;o&&!i&&(gl.ensureZIndex(e,r),e[to].initialized=!0),e[to].enabled=!!o},unmounted(e,t){if(!e[to].initialized)return;const{value:n={}}=t,{zIndex:r}=n;gl.unregister(e,r)}},qy="@css-render/vue3-ssr";function Zy(e,t){return`<style cssr-id="${e}">
${t}
</style>`}function Jy(e,t,n){const{styles:r,ids:o}=n;o.has(e)||r!==null&&(o.add(e),r.push(Zy(e,t)))}const Qy=typeof document<"u";function gi(){if(Qy)return;const e=Se(qy,null);if(e!==null)return{adapter:(t,n)=>Jy(t,n,e),context:e}}function id(e,t){console.error(`[vueuc/${e}]: ${t}`)}const{c:Ri}=xp(),ex="vueuc-style";function sd(e){return typeof e=="string"?document.querySelector(e):e()}const Np=ge({name:"LazyTeleport",props:{to:{type:[String,Object],default:void 0},disabled:Boolean,show:{type:Boolean,required:!0}},setup(e){return{showTeleport:Ay(Qe(e,"show")),mergedTo:V(()=>{const{to:t}=e;return t??"body"})}},render(){return this.showTeleport?this.disabled?na("lazy-teleport",this.$slots):T(Ts,{disabled:this.disabled,to:this.mergedTo},na("lazy-teleport",this.$slots)):null}}),Li={top:"bottom",bottom:"top",left:"right",right:"left"},ld={start:"end",center:"center",end:"start"},vl={top:"height",bottom:"height",left:"width",right:"width"},tx={"bottom-start":"top left",bottom:"top center","bottom-end":"top right","top-start":"bottom left",top:"bottom center","top-end":"bottom right","right-start":"top left",right:"center left","right-end":"bottom left","left-start":"top right",left:"center right","left-end":"bottom right"},nx={"bottom-start":"bottom left",bottom:"bottom center","bottom-end":"bottom right","top-start":"top left",top:"top center","top-end":"top right","right-start":"top right",right:"center right","right-end":"bottom right","left-start":"top left",left:"center left","left-end":"bottom left"},rx={"bottom-start":"right","bottom-end":"left","top-start":"right","top-end":"left","right-start":"bottom","right-end":"top","left-start":"bottom","left-end":"top"},ad={top:!0,bottom:!1,left:!0,right:!1},cd={top:"end",bottom:"start",left:"end",right:"start"};function ox(e,t,n,r,o,i){if(!o||i)return{placement:e,top:0,left:0};const[s,l]=e.split("-");let a=l??"center",c={top:0,left:0};const u=(m,p,g)=>{let x=0,h=0;const y=n[m]-t[p]-t[m];return y>0&&r&&(g?h=ad[p]?y:-y:x=ad[p]?y:-y),{left:x,top:h}},d=s==="left"||s==="right";if(a!=="center"){const m=rx[e],p=Li[m],g=vl[m];if(n[g]>t[g]){if(t[m]+t[g]<n[g]){const x=(n[g]-t[g])/2;t[m]<x||t[p]<x?t[m]<t[p]?(a=ld[l],c=u(g,p,d)):c=u(g,m,d):a="center"}}else n[g]<t[g]&&t[p]<0&&t[m]>t[p]&&(a=ld[l])}else{const m=s==="bottom"||s==="top"?"left":"top",p=Li[m],g=vl[m],x=(n[g]-t[g])/2;(t[m]<x||t[p]<x)&&(t[m]>t[p]?(a=cd[m],c=u(g,m,d)):(a=cd[p],c=u(g,p,d)))}let f=s;return t[s]<n[vl[s]]&&t[s]<t[Li[s]]&&(f=Li[s]),{placement:a!=="center"?`${f}-${a}`:f,left:c.left,top:c.top}}function ix(e,t){return t?nx[e]:tx[e]}function sx(e,t,n,r,o,i){if(i)switch(e){case"bottom-start":return{top:`${Math.round(n.top-t.top+n.height)}px`,left:`${Math.round(n.left-t.left)}px`,transform:"translateY(-100%)"};case"bottom-end":return{top:`${Math.round(n.top-t.top+n.height)}px`,left:`${Math.round(n.left-t.left+n.width)}px`,transform:"translateX(-100%) translateY(-100%)"};case"top-start":return{top:`${Math.round(n.top-t.top)}px`,left:`${Math.round(n.left-t.left)}px`,transform:""};case"top-end":return{top:`${Math.round(n.top-t.top)}px`,left:`${Math.round(n.left-t.left+n.width)}px`,transform:"translateX(-100%)"};case"right-start":return{top:`${Math.round(n.top-t.top)}px`,left:`${Math.round(n.left-t.left+n.width)}px`,transform:"translateX(-100%)"};case"right-end":return{top:`${Math.round(n.top-t.top+n.height)}px`,left:`${Math.round(n.left-t.left+n.width)}px`,transform:"translateX(-100%) translateY(-100%)"};case"left-start":return{top:`${Math.round(n.top-t.top)}px`,left:`${Math.round(n.left-t.left)}px`,transform:""};case"left-end":return{top:`${Math.round(n.top-t.top+n.height)}px`,left:`${Math.round(n.left-t.left)}px`,transform:"translateY(-100%)"};case"top":return{top:`${Math.round(n.top-t.top)}px`,left:`${Math.round(n.left-t.left+n.width/2)}px`,transform:"translateX(-50%)"};case"right":return{top:`${Math.round(n.top-t.top+n.height/2)}px`,left:`${Math.round(n.left-t.left+n.width)}px`,transform:"translateX(-100%) translateY(-50%)"};case"left":return{top:`${Math.round(n.top-t.top+n.height/2)}px`,left:`${Math.round(n.left-t.left)}px`,transform:"translateY(-50%)"};case"bottom":default:return{top:`${Math.round(n.top-t.top+n.height)}px`,left:`${Math.round(n.left-t.left+n.width/2)}px`,transform:"translateX(-50%) translateY(-100%)"}}switch(e){case"bottom-start":return{top:`${Math.round(n.top-t.top+n.height+r)}px`,left:`${Math.round(n.left-t.left+o)}px`,transform:""};case"bottom-end":return{top:`${Math.round(n.top-t.top+n.height+r)}px`,left:`${Math.round(n.left-t.left+n.width+o)}px`,transform:"translateX(-100%)"};case"top-start":return{top:`${Math.round(n.top-t.top+r)}px`,left:`${Math.round(n.left-t.left+o)}px`,transform:"translateY(-100%)"};case"top-end":return{top:`${Math.round(n.top-t.top+r)}px`,left:`${Math.round(n.left-t.left+n.width+o)}px`,transform:"translateX(-100%) translateY(-100%)"};case"right-start":return{top:`${Math.round(n.top-t.top+r)}px`,left:`${Math.round(n.left-t.left+n.width+o)}px`,transform:""};case"right-end":return{top:`${Math.round(n.top-t.top+n.height+r)}px`,left:`${Math.round(n.left-t.left+n.width+o)}px`,transform:"translateY(-100%)"};case"left-start":return{top:`${Math.round(n.top-t.top+r)}px`,left:`${Math.round(n.left-t.left+o)}px`,transform:"translateX(-100%)"};case"left-end":return{top:`${Math.round(n.top-t.top+n.height+r)}px`,left:`${Math.round(n.left-t.left+o)}px`,transform:"translateX(-100%) translateY(-100%)"};case"top":return{top:`${Math.round(n.top-t.top+r)}px`,left:`${Math.round(n.left-t.left+n.width/2+o)}px`,transform:"translateY(-100%) translateX(-50%)"};case"right":return{top:`${Math.round(n.top-t.top+n.height/2+r)}px`,left:`${Math.round(n.left-t.left+n.width+o)}px`,transform:"translateY(-50%)"};case"left":return{top:`${Math.round(n.top-t.top+n.height/2+r)}px`,left:`${Math.round(n.left-t.left+o)}px`,transform:"translateY(-50%) translateX(-100%)"};case"bottom":default:return{top:`${Math.round(n.top-t.top+n.height+r)}px`,left:`${Math.round(n.left-t.left+n.width/2+o)}px`,transform:"translateX(-50%)"}}}const lx=Ri([Ri(".v-binder-follower-container",{position:"absolute",left:"0",right:"0",top:"0",height:"0",pointerEvents:"none",zIndex:"auto"}),Ri(".v-binder-follower-content",{position:"absolute",zIndex:"auto"},[Ri("> *",{pointerEvents:"all"})])]),Dp=ge({name:"Follower",inheritAttrs:!1,props:{show:Boolean,enabled:{type:Boolean,default:void 0},placement:{type:String,default:"bottom"},syncTrigger:{type:Array,default:["resize","scroll"]},to:[String,Object],flip:{type:Boolean,default:!0},internalShift:Boolean,x:Number,y:Number,width:String,minWidth:String,containerClass:String,teleportDisabled:Boolean,zindexable:{type:Boolean,default:!0},zIndex:Number,overlap:Boolean},setup(e){const t=Se("VBinder"),n=an(()=>e.enabled!==void 0?e.enabled:e.show),r=D(null),o=D(null),i=()=>{const{syncTrigger:f}=e;f.includes("scroll")&&t.addScrollListener(a),f.includes("resize")&&t.addResizeListener(a)},s=()=>{t.removeScrollListener(a),t.removeResizeListener(a)};mt(()=>{n.value&&(a(),i())});const l=gi();lx.mount({id:"vueuc/binder",head:!0,anchorMetaName:ex,ssr:l}),pt(()=>{s()}),Ry(()=>{n.value&&a()});const a=()=>{if(!n.value)return;const f=r.value;if(f===null)return;const m=t.targetRef,{x:p,y:g,overlap:x}=e,h=p!==void 0&&g!==void 0?Vy(p,g):pl(m);f.style.setProperty("--v-target-width",`${Math.round(h.width)}px`),f.style.setProperty("--v-target-height",`${Math.round(h.height)}px`);const{width:y,minWidth:P,placement:b,internalShift:C,flip:S}=e;f.setAttribute("v-placement",b),x?f.setAttribute("v-overlap",""):f.removeAttribute("v-overlap");const{style:v}=f;y==="target"?v.width=`${h.width}px`:y!==void 0?v.width=y:v.width="",P==="target"?v.minWidth=`${h.width}px`:P!==void 0?v.minWidth=P:v.minWidth="";const E=pl(f),A=pl(o.value),{left:F,top:B,placement:R}=ox(b,h,E,C,S,x),j=ix(R,x),{left:Q,top:H,transform:te}=sx(R,A,h,B,F,x);f.setAttribute("v-placement",R),f.style.setProperty("--v-offset-left",`${Math.round(F)}px`),f.style.setProperty("--v-offset-top",`${Math.round(B)}px`),f.style.transform=`translateX(${Q}) translateY(${H}) ${te}`,f.style.setProperty("--v-transform-origin",j),f.style.transformOrigin=j};Ye(n,f=>{f?(i(),c()):s()});const c=()=>{qt().then(a).catch(f=>console.error(f))};["placement","x","y","internalShift","flip","width","overlap","minWidth"].forEach(f=>{Ye(Qe(e,f),a)}),["teleportDisabled"].forEach(f=>{Ye(Qe(e,f),c)}),Ye(Qe(e,"syncTrigger"),f=>{f.includes("resize")?t.addResizeListener(a):t.removeResizeListener(a),f.includes("scroll")?t.addScrollListener(a):t.removeScrollListener(a)});const u=hi(),d=an(()=>{const{to:f}=e;if(f!==void 0)return f;u.value});return{VBinder:t,mergedEnabled:n,offsetContainerRef:o,followerRef:r,mergedTo:d,syncPosition:a}},render(){return T(Np,{show:this.show,to:this.mergedTo,disabled:this.teleportDisabled},{default:()=>{var e,t;const n=T("div",{class:["v-binder-follower-container",this.containerClass],ref:"offsetContainerRef"},[T("div",{class:"v-binder-follower-content",ref:"followerRef"},(t=(e=this.$slots).default)===null||t===void 0?void 0:t.call(e))]);return this.zindexable?Un(n,[[Qa,{enabled:this.mergedEnabled,zIndex:this.zIndex}]]):n}})}});var Mr=[],ax=function(){return Mr.some(function(e){return e.activeTargets.length>0})},cx=function(){return Mr.some(function(e){return e.skippedTargets.length>0})},ud="ResizeObserver loop completed with undelivered notifications.",ux=function(){var e;typeof ErrorEvent=="function"?e=new ErrorEvent("error",{message:ud}):(e=document.createEvent("Event"),e.initEvent("error",!1,!1),e.message=ud),window.dispatchEvent(e)},ii;(function(e){e.BORDER_BOX="border-box",e.CONTENT_BOX="content-box",e.DEVICE_PIXEL_CONTENT_BOX="device-pixel-content-box"})(ii||(ii={}));var Fr=function(e){return Object.freeze(e)},dx=function(){function e(t,n){this.inlineSize=t,this.blockSize=n,Fr(this)}return e}(),zp=function(){function e(t,n,r,o){return this.x=t,this.y=n,this.width=r,this.height=o,this.top=this.y,this.left=this.x,this.bottom=this.top+this.height,this.right=this.left+this.width,Fr(this)}return e.prototype.toJSON=function(){var t=this,n=t.x,r=t.y,o=t.top,i=t.right,s=t.bottom,l=t.left,a=t.width,c=t.height;return{x:n,y:r,top:o,right:i,bottom:s,left:l,width:a,height:c}},e.fromRect=function(t){return new e(t.x,t.y,t.width,t.height)},e}(),ec=function(e){return e instanceof SVGElement&&"getBBox"in e},Bp=function(e){if(ec(e)){var t=e.getBBox(),n=t.width,r=t.height;return!n&&!r}var o=e,i=o.offsetWidth,s=o.offsetHeight;return!(i||s||e.getClientRects().length)},dd=function(e){var t;if(e instanceof Element)return!0;var n=(t=e==null?void 0:e.ownerDocument)===null||t===void 0?void 0:t.defaultView;return!!(n&&e instanceof n.Element)},fx=function(e){switch(e.tagName){case"INPUT":if(e.type!=="image")break;case"VIDEO":case"AUDIO":case"EMBED":case"OBJECT":case"CANVAS":case"IFRAME":case"IMG":return!0}return!1},Uo=typeof window<"u"?window:{},Mi=new WeakMap,fd=/auto|scroll/,hx=/^tb|vertical/,px=/msie|trident/i.test(Uo.navigator&&Uo.navigator.userAgent),Cn=function(e){return parseFloat(e||"0")},po=function(e,t,n){return e===void 0&&(e=0),t===void 0&&(t=0),n===void 0&&(n=!1),new dx((n?t:e)||0,(n?e:t)||0)},hd=Fr({devicePixelContentBoxSize:po(),borderBoxSize:po(),contentBoxSize:po(),contentRect:new zp(0,0,0,0)}),Hp=function(e,t){if(t===void 0&&(t=!1),Mi.has(e)&&!t)return Mi.get(e);if(Bp(e))return Mi.set(e,hd),hd;var n=getComputedStyle(e),r=ec(e)&&e.ownerSVGElement&&e.getBBox(),o=!px&&n.boxSizing==="border-box",i=hx.test(n.writingMode||""),s=!r&&fd.test(n.overflowY||""),l=!r&&fd.test(n.overflowX||""),a=r?0:Cn(n.paddingTop),c=r?0:Cn(n.paddingRight),u=r?0:Cn(n.paddingBottom),d=r?0:Cn(n.paddingLeft),f=r?0:Cn(n.borderTopWidth),m=r?0:Cn(n.borderRightWidth),p=r?0:Cn(n.borderBottomWidth),g=r?0:Cn(n.borderLeftWidth),x=d+c,h=a+u,y=g+m,P=f+p,b=l?e.offsetHeight-P-e.clientHeight:0,C=s?e.offsetWidth-y-e.clientWidth:0,S=o?x+y:0,v=o?h+P:0,E=r?r.width:Cn(n.width)-S-C,A=r?r.height:Cn(n.height)-v-b,F=E+x+C+y,B=A+h+b+P,R=Fr({devicePixelContentBoxSize:po(Math.round(E*devicePixelRatio),Math.round(A*devicePixelRatio),i),borderBoxSize:po(F,B,i),contentBoxSize:po(E,A,i),contentRect:new zp(d,a,E,A)});return Mi.set(e,R),R},jp=function(e,t,n){var r=Hp(e,n),o=r.borderBoxSize,i=r.contentBoxSize,s=r.devicePixelContentBoxSize;switch(t){case ii.DEVICE_PIXEL_CONTENT_BOX:return s;case ii.BORDER_BOX:return o;default:return i}},gx=function(){function e(t){var n=Hp(t);this.target=t,this.contentRect=n.contentRect,this.borderBoxSize=Fr([n.borderBoxSize]),this.contentBoxSize=Fr([n.contentBoxSize]),this.devicePixelContentBoxSize=Fr([n.devicePixelContentBoxSize])}return e}(),Wp=function(e){if(Bp(e))return 1/0;for(var t=0,n=e.parentNode;n;)t+=1,n=n.parentNode;return t},vx=function(){var e=1/0,t=[];Mr.forEach(function(s){if(s.activeTargets.length!==0){var l=[];s.activeTargets.forEach(function(c){var u=new gx(c.target),d=Wp(c.target);l.push(u),c.lastReportedSize=jp(c.target,c.observedBox),d<e&&(e=d)}),t.push(function(){s.callback.call(s.observer,l,s.observer)}),s.activeTargets.splice(0,s.activeTargets.length)}});for(var n=0,r=t;n<r.length;n++){var o=r[n];o()}return e},pd=function(e){Mr.forEach(function(n){n.activeTargets.splice(0,n.activeTargets.length),n.skippedTargets.splice(0,n.skippedTargets.length),n.observationTargets.forEach(function(o){o.isActive()&&(Wp(o.target)>e?n.activeTargets.push(o):n.skippedTargets.push(o))})})},mx=function(){var e=0;for(pd(e);ax();)e=vx(),pd(e);return cx()&&ux(),e>0},ml,Up=[],bx=function(){return Up.splice(0).forEach(function(e){return e()})},yx=function(e){if(!ml){var t=0,n=document.createTextNode(""),r={characterData:!0};new MutationObserver(function(){return bx()}).observe(n,r),ml=function(){n.textContent="".concat(t?t--:t++)}}Up.push(e),ml()},xx=function(e){yx(function(){requestAnimationFrame(e)})},qi=0,wx=function(){return!!qi},_x=250,Cx={attributes:!0,characterData:!0,childList:!0,subtree:!0},gd=["resize","load","transitionend","animationend","animationstart","animationiteration","keyup","keydown","mouseup","mousedown","mouseover","mouseout","blur","focus"],vd=function(e){return e===void 0&&(e=0),Date.now()+e},bl=!1,Sx=function(){function e(){var t=this;this.stopped=!0,this.listener=function(){return t.schedule()}}return e.prototype.run=function(t){var n=this;if(t===void 0&&(t=_x),!bl){bl=!0;var r=vd(t);xx(function(){var o=!1;try{o=mx()}finally{if(bl=!1,t=r-vd(),!wx())return;o?n.run(1e3):t>0?n.run(t):n.start()}})}},e.prototype.schedule=function(){this.stop(),this.run()},e.prototype.observe=function(){var t=this,n=function(){return t.observer&&t.observer.observe(document.body,Cx)};document.body?n():Uo.addEventListener("DOMContentLoaded",n)},e.prototype.start=function(){var t=this;this.stopped&&(this.stopped=!1,this.observer=new MutationObserver(this.listener),this.observe(),gd.forEach(function(n){return Uo.addEventListener(n,t.listener,!0)}))},e.prototype.stop=function(){var t=this;this.stopped||(this.observer&&this.observer.disconnect(),gd.forEach(function(n){return Uo.removeEventListener(n,t.listener,!0)}),this.stopped=!0)},e}(),ia=new Sx,md=function(e){!qi&&e>0&&ia.start(),qi+=e,!qi&&ia.stop()},Ex=function(e){return!ec(e)&&!fx(e)&&getComputedStyle(e).display==="inline"},Tx=function(){function e(t,n){this.target=t,this.observedBox=n||ii.CONTENT_BOX,this.lastReportedSize={inlineSize:0,blockSize:0}}return e.prototype.isActive=function(){var t=jp(this.target,this.observedBox,!0);return Ex(this.target)&&(this.lastReportedSize=t),this.lastReportedSize.inlineSize!==t.inlineSize||this.lastReportedSize.blockSize!==t.blockSize},e}(),$x=function(){function e(t,n){this.activeTargets=[],this.skippedTargets=[],this.observationTargets=[],this.observer=t,this.callback=n}return e}(),Fi=new WeakMap,bd=function(e,t){for(var n=0;n<e.length;n+=1)if(e[n].target===t)return n;return-1},Ni=function(){function e(){}return e.connect=function(t,n){var r=new $x(t,n);Fi.set(t,r)},e.observe=function(t,n,r){var o=Fi.get(t),i=o.observationTargets.length===0;bd(o.observationTargets,n)<0&&(i&&Mr.push(o),o.observationTargets.push(new Tx(n,r&&r.box)),md(1),ia.schedule())},e.unobserve=function(t,n){var r=Fi.get(t),o=bd(r.observationTargets,n),i=r.observationTargets.length===1;o>=0&&(i&&Mr.splice(Mr.indexOf(r),1),r.observationTargets.splice(o,1),md(-1))},e.disconnect=function(t){var n=this,r=Fi.get(t);r.observationTargets.slice().forEach(function(o){return n.unobserve(t,o.target)}),r.activeTargets.splice(0,r.activeTargets.length)},e}(),Px=function(){function e(t){if(arguments.length===0)throw new TypeError("Failed to construct 'ResizeObserver': 1 argument required, but only 0 present.");if(typeof t!="function")throw new TypeError("Failed to construct 'ResizeObserver': The callback provided as parameter 1 is not a function.");Ni.connect(this,t)}return e.prototype.observe=function(t,n){if(arguments.length===0)throw new TypeError("Failed to execute 'observe' on 'ResizeObserver': 1 argument required, but only 0 present.");if(!dd(t))throw new TypeError("Failed to execute 'observe' on 'ResizeObserver': parameter 1 is not of type 'Element");Ni.observe(this,t,n)},e.prototype.unobserve=function(t){if(arguments.length===0)throw new TypeError("Failed to execute 'unobserve' on 'ResizeObserver': 1 argument required, but only 0 present.");if(!dd(t))throw new TypeError("Failed to execute 'unobserve' on 'ResizeObserver': parameter 1 is not of type 'Element");Ni.unobserve(this,t)},e.prototype.disconnect=function(){Ni.disconnect(this)},e.toString=function(){return"function ResizeObserver () { [polyfill code] }"},e}();class Ox{constructor(){this.handleResize=this.handleResize.bind(this),this.observer=new(typeof window<"u"&&window.ResizeObserver||Px)(this.handleResize),this.elHandlersMap=new Map}handleResize(t){for(const n of t){const r=this.elHandlersMap.get(n.target);r!==void 0&&r(n)}}registerHandler(t,n){this.elHandlersMap.set(t,n),this.observer.observe(t)}unregisterHandler(t){this.elHandlersMap.has(t)&&(this.elHandlersMap.delete(t),this.observer.unobserve(t))}}const yd=new Ox,ds=ge({name:"ResizeObserver",props:{onResize:Function},setup(e){let t=!1;const n=In().proxy;function r(o){const{onResize:i}=e;i!==void 0&&i(o)}mt(()=>{const o=n.$el;if(o===void 0){id("resize-observer","$el does not exist.");return}if(o.nextElementSibling!==o.nextSibling&&o.nodeType===3&&o.nodeValue!==""){id("resize-observer","$el can not be observed (it may be a text node).");return}o.nextElementSibling!==null&&(yd.registerHandler(o.nextElementSibling,r),t=!0)}),pt(()=>{t&&yd.unregisterHandler(n.$el.nextElementSibling)})},render(){return Sm(this.$slots,"default")}});function Vp(e){return e instanceof HTMLElement}function Kp(e){for(let t=0;t<e.childNodes.length;t++){const n=e.childNodes[t];if(Vp(n)&&(Yp(n)||Kp(n)))return!0}return!1}function Gp(e){for(let t=e.childNodes.length-1;t>=0;t--){const n=e.childNodes[t];if(Vp(n)&&(Yp(n)||Gp(n)))return!0}return!1}function Yp(e){if(!kx(e))return!1;try{e.focus({preventScroll:!0})}catch{}return document.activeElement===e}function kx(e){if(e.tabIndex>0||e.tabIndex===0&&e.getAttribute("tabIndex")!==null)return!0;if(e.getAttribute("disabled"))return!1;switch(e.nodeName){case"A":return!!e.href&&e.rel!=="ignore";case"INPUT":return e.type!=="hidden"&&e.type!=="file";case"BUTTON":case"SELECT":case"TEXTAREA":return!0;default:return!1}}let Ao=[];const Xp=ge({name:"FocusTrap",props:{disabled:Boolean,active:Boolean,autoFocus:{type:Boolean,default:!0},onEsc:Function,initialFocusTo:String,finalFocusTo:String,returnFocusOnDeactivated:{type:Boolean,default:!0}},setup(e){const t=fi(),n=D(null),r=D(null);let o=!1,i=!1;const s=typeof document>"u"?null:document.activeElement;function l(){return Ao[Ao.length-1]===t}function a(x){var h;x.code==="Escape"&&l()&&((h=e.onEsc)===null||h===void 0||h.call(e,x))}mt(()=>{Ye(()=>e.active,x=>{x?(d(),De("keydown",document,a)):(Ne("keydown",document,a),o&&f())},{immediate:!0})}),pt(()=>{Ne("keydown",document,a),o&&f()});function c(x){if(!i&&l()){const h=u();if(h===null||h.contains(xo(x)))return;m("first")}}function u(){const x=n.value;if(x===null)return null;let h=x;for(;h=h.nextSibling,!(h===null||h instanceof Element&&h.tagName==="DIV"););return h}function d(){var x;if(!e.disabled){if(Ao.push(t),e.autoFocus){const{initialFocusTo:h}=e;h===void 0?m("first"):(x=sd(h))===null||x===void 0||x.focus({preventScroll:!0})}o=!0,document.addEventListener("focus",c,!0)}}function f(){var x;if(e.disabled||(document.removeEventListener("focus",c,!0),Ao=Ao.filter(y=>y!==t),l()))return;const{finalFocusTo:h}=e;h!==void 0?(x=sd(h))===null||x===void 0||x.focus({preventScroll:!0}):e.returnFocusOnDeactivated&&s instanceof HTMLElement&&(i=!0,s.focus({preventScroll:!0}),i=!1)}function m(x){if(l()&&e.active){const h=n.value,y=r.value;if(h!==null&&y!==null){const P=u();if(P==null||P===y){i=!0,h.focus({preventScroll:!0}),i=!1;return}i=!0;const b=x==="first"?Kp(P):Gp(P);i=!1,b||(i=!0,h.focus({preventScroll:!0}),i=!1)}}}function p(x){if(i)return;const h=u();h!==null&&(x.relatedTarget!==null&&h.contains(x.relatedTarget)?m("last"):m("first"))}function g(x){i||(x.relatedTarget!==null&&x.relatedTarget===n.value?m("last"):m("first"))}return{focusableStartRef:n,focusableEndRef:r,focusableStyle:"position: absolute; height: 0; width: 0;",handleStartFocus:p,handleEndFocus:g}},render(){const{default:e}=this.$slots;if(e===void 0)return null;if(this.disabled)return e();const{active:t,focusableStyle:n}=this;return T(We,null,[T("div",{"aria-hidden":"true",tabindex:t?"0":"-1",ref:"focusableStartRef",style:n,onFocus:this.handleStartFocus}),e(),T("div",{"aria-hidden":"true",style:n,ref:"focusableEndRef",tabindex:t?"0":"-1",onFocus:this.handleEndFocus})])}});function xd(e){return e.replace(/#|\(|\)|,|\s|\./g,"_")}const Ax=/^(\d|\.)+$/,wd=/(\d|\.)+/;function Zi(e,{c:t=1,offset:n=0,attachPx:r=!0}={}){if(typeof e=="number"){const o=(e+n)*t;return o===0?"0":`${o}px`}else if(typeof e=="string")if(Ax.test(e)){const o=(Number(e)+n)*t;return r?o===0?"0":`${o}px`:`${o}`}else{const o=wd.exec(e);return o?e.replace(wd,String((Number(o[0])+n)*t)):e}return e}function _d(e){const{left:t,right:n,top:r,bottom:o}=lr(e);return`${r} ${t} ${o} ${n}`}let yl;function Ix(){return yl===void 0&&(yl=navigator.userAgent.includes("Node.js")||navigator.userAgent.includes("jsdom")),yl}const Rx=new WeakSet;function Lx(e){return!Rx.has(e)}function hr(e,t){console.error(`[naive/${e}]: ${t}`)}function Vr(e,t){throw new Error(`[naive/${e}]: ${t}`)}function Xt(e,...t){if(Array.isArray(e))e.forEach(n=>Xt(n,...t));else return e(...t)}function Mx(e){return t=>{t?e.value=t.$el:e.value=null}}function si(e,t=!0,n=[]){return e.forEach(r=>{if(r!==null){if(typeof r!="object"){(typeof r=="string"||typeof r=="number")&&n.push(vo(String(r)));return}if(Array.isArray(r)){si(r,t,n);return}if(r.type===We){if(r.children===null)return;Array.isArray(r.children)&&si(r.children,t,n)}else{if(r.type===$t&&t)return;n.push(r)}}}),n}function Fx(e,t="default",n=void 0){const r=e[t];if(!r)return hr("getFirstSlotVNode",`slot[${t}] is empty`),null;const o=si(r(n));return o.length===1?o[0]:(hr("getFirstSlotVNode",`slot[${t}] should have exactly one child`),null)}function Nx(e,t,n){if(!t)return null;const r=si(t(n));return r.length===1?r[0]:(hr("getFirstSlotVNode",`slot[${e}] should have exactly one child`),null)}function Vn(e,t=[],n){const r={};return t.forEach(o=>{r[o]=e[o]}),Object.assign(r,n)}function Ds(e){return Object.keys(e)}function zs(e,t=[],n){const r={};return Object.getOwnPropertyNames(e).forEach(i=>{t.includes(i)||(r[i]=e[i])}),Object.assign(r,n)}function kt(e,...t){return typeof e=="function"?e(...t):typeof e=="string"?vo(e):typeof e=="number"?vo(String(e)):null}function hn(e){return e.some(t=>go(t)?!(t.type===$t||t.type===We&&!hn(t.children)):!0)?e:null}function Cd(e,t){return e&&hn(e())||t()}function Sd(e,t,n){return e&&hn(e(t))||n(t)}function Yt(e,t){const n=e&&hn(e());return t(n||null)}function sa(e){return!(e&&hn(e()))}const Ed=ge({render(){var e,t;return(t=(e=this.$slots).default)===null||t===void 0?void 0:t.call(e)}}),pr="n-config-provider",la="n";function Pt(e={},t={defaultBordered:!0}){const n=Se(pr,null);return{inlineThemeDisabled:n==null?void 0:n.inlineThemeDisabled,mergedRtlRef:n==null?void 0:n.mergedRtlRef,mergedComponentPropsRef:n==null?void 0:n.mergedComponentPropsRef,mergedBreakpointsRef:n==null?void 0:n.mergedBreakpointsRef,mergedBorderedRef:V(()=>{var r,o;const{bordered:i}=e;return i!==void 0?i:(o=(r=n==null?void 0:n.mergedBorderedRef.value)!==null&&r!==void 0?r:t.defaultBordered)!==null&&o!==void 0?o:!0}),mergedClsPrefixRef:n?n.mergedClsPrefixRef:Kf(la),namespaceRef:V(()=>n==null?void 0:n.mergedNamespaceRef.value)}}function dn(e,t,n,r){n||Vr("useThemeClass","cssVarsRef is not passed");const o=Se(pr,null),i=o==null?void 0:o.mergedThemeHashRef,s=o==null?void 0:o.styleMountTarget,l=D(""),a=gi();let c;const u=`__${e}`,d=()=>{let f=u;const m=t?t.value:void 0,p=i==null?void 0:i.value;p&&(f+=`-${p}`),m&&(f+=`-${m}`);const{themeOverrides:g,builtinThemeOverrides:x}=r;g&&(f+=`-${ri(JSON.stringify(g))}`),x&&(f+=`-${ri(JSON.stringify(x))}`),l.value=f,c=()=>{const h=n.value;let y="";for(const P in h)y+=`${P}: ${h[P]};`;U(`.${f}`,y).mount({id:f,ssr:a,parent:s}),c=void 0}};return Wr(()=>{d()}),{themeClass:l,onRender:()=>{c==null||c()}}}const Td="n-form-item";function Dx(e,{defaultSize:t="medium",mergedSize:n,mergedDisabled:r}={}){const o=Se(Td,null);ze(Td,null);const i=V(n?()=>n(o):()=>{const{size:a}=e;if(a)return a;if(o){const{mergedSize:c}=o;if(c.value!==void 0)return c.value}return t}),s=V(r?()=>r(o):()=>{const{disabled:a}=e;return a!==void 0?a:o?o.disabled.value:!1}),l=V(()=>{const{status:a}=e;return a||(o==null?void 0:o.mergedValidationStatus.value)});return pt(()=>{o&&o.restoreValidation()}),{mergedSizeRef:i,mergedDisabledRef:s,mergedStatusRef:l,nTriggerFormBlur(){o&&o.handleContentBlur()},nTriggerFormChange(){o&&o.handleContentChange()},nTriggerFormFocus(){o&&o.handleContentFocus()},nTriggerFormInput(){o&&o.handleContentInput()}}}var qp=typeof global=="object"&&global&&global.Object===Object&&global,zx=typeof self=="object"&&self&&self.Object===Object&&self,yn=qp||zx||Function("return this")(),gr=yn.Symbol,Zp=Object.prototype,Bx=Zp.hasOwnProperty,Hx=Zp.toString,Io=gr?gr.toStringTag:void 0;function jx(e){var t=Bx.call(e,Io),n=e[Io];try{e[Io]=void 0;var r=!0}catch{}var o=Hx.call(e);return r&&(t?e[Io]=n:delete e[Io]),o}var Wx=Object.prototype,Ux=Wx.toString;function Vx(e){return Ux.call(e)}var Kx="[object Null]",Gx="[object Undefined]",$d=gr?gr.toStringTag:void 0;function Kr(e){return e==null?e===void 0?Gx:Kx:$d&&$d in Object(e)?jx(e):Vx(e)}function vr(e){return e!=null&&typeof e=="object"}var Yx="[object Symbol]";function Bs(e){return typeof e=="symbol"||vr(e)&&Kr(e)==Yx}function Jp(e,t){for(var n=-1,r=e==null?0:e.length,o=Array(r);++n<r;)o[n]=t(e[n],n,e);return o}var un=Array.isArray,Pd=gr?gr.prototype:void 0,Od=Pd?Pd.toString:void 0;function Qp(e){if(typeof e=="string")return e;if(un(e))return Jp(e,Qp)+"";if(Bs(e))return Od?Od.call(e):"";var t=e+"";return t=="0"&&1/e==-1/0?"-0":t}var Xx=/\s/;function qx(e){for(var t=e.length;t--&&Xx.test(e.charAt(t)););return t}var Zx=/^\s+/;function Jx(e){return e&&e.slice(0,qx(e)+1).replace(Zx,"")}function bn(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}var kd=NaN,Qx=/^[-+]0x[0-9a-f]+$/i,ew=/^0b[01]+$/i,tw=/^0o[0-7]+$/i,nw=parseInt;function Ad(e){if(typeof e=="number")return e;if(Bs(e))return kd;if(bn(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=bn(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=Jx(e);var n=ew.test(e);return n||tw.test(e)?nw(e.slice(2),n?2:8):Qx.test(e)?kd:+e}function tc(e){return e}var rw="[object AsyncFunction]",ow="[object Function]",iw="[object GeneratorFunction]",sw="[object Proxy]";function nc(e){if(!bn(e))return!1;var t=Kr(e);return t==ow||t==iw||t==rw||t==sw}var xl=yn["__core-js_shared__"],Id=function(){var e=/[^.]+$/.exec(xl&&xl.keys&&xl.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();function lw(e){return!!Id&&Id in e}var aw=Function.prototype,cw=aw.toString;function Gr(e){if(e!=null){try{return cw.call(e)}catch{}try{return e+""}catch{}}return""}var uw=/[\\^$.*+?()[\]{}|]/g,dw=/^\[object .+?Constructor\]$/,fw=Function.prototype,hw=Object.prototype,pw=fw.toString,gw=hw.hasOwnProperty,vw=RegExp("^"+pw.call(gw).replace(uw,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function mw(e){if(!bn(e)||lw(e))return!1;var t=nc(e)?vw:dw;return t.test(Gr(e))}function bw(e,t){return e==null?void 0:e[t]}function Yr(e,t){var n=bw(e,t);return mw(n)?n:void 0}var aa=Yr(yn,"WeakMap"),Rd=Object.create,yw=function(){function e(){}return function(t){if(!bn(t))return{};if(Rd)return Rd(t);e.prototype=t;var n=new e;return e.prototype=void 0,n}}();function xw(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}function ww(e,t){var n=-1,r=e.length;for(t||(t=Array(r));++n<r;)t[n]=e[n];return t}var _w=800,Cw=16,Sw=Date.now;function Ew(e){var t=0,n=0;return function(){var r=Sw(),o=Cw-(r-n);if(n=r,o>0){if(++t>=_w)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}function Tw(e){return function(){return e}}var fs=function(){try{var e=Yr(Object,"defineProperty");return e({},"",{}),e}catch{}}(),$w=fs?function(e,t){return fs(e,"toString",{configurable:!0,enumerable:!1,value:Tw(t),writable:!0})}:tc,Pw=Ew($w),Ow=9007199254740991,kw=/^(?:0|[1-9]\d*)$/;function rc(e,t){var n=typeof e;return t=t??Ow,!!t&&(n=="number"||n!="symbol"&&kw.test(e))&&e>-1&&e%1==0&&e<t}function oc(e,t,n){t=="__proto__"&&fs?fs(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}function vi(e,t){return e===t||e!==e&&t!==t}var Aw=Object.prototype,Iw=Aw.hasOwnProperty;function Rw(e,t,n){var r=e[t];(!(Iw.call(e,t)&&vi(r,n))||n===void 0&&!(t in e))&&oc(e,t,n)}function Lw(e,t,n,r){var o=!n;n||(n={});for(var i=-1,s=t.length;++i<s;){var l=t[i],a=void 0;a===void 0&&(a=e[l]),o?oc(n,l,a):Rw(n,l,a)}return n}var Ld=Math.max;function Mw(e,t,n){return t=Ld(t===void 0?e.length-1:t,0),function(){for(var r=arguments,o=-1,i=Ld(r.length-t,0),s=Array(i);++o<i;)s[o]=r[t+o];o=-1;for(var l=Array(t+1);++o<t;)l[o]=r[o];return l[t]=n(s),xw(e,this,l)}}function Fw(e,t){return Pw(Mw(e,t,tc),e+"")}var Nw=9007199254740991;function ic(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=Nw}function So(e){return e!=null&&ic(e.length)&&!nc(e)}function Dw(e,t,n){if(!bn(n))return!1;var r=typeof t;return(r=="number"?So(n)&&rc(t,n.length):r=="string"&&t in n)?vi(n[t],e):!1}function zw(e){return Fw(function(t,n){var r=-1,o=n.length,i=o>1?n[o-1]:void 0,s=o>2?n[2]:void 0;for(i=e.length>3&&typeof i=="function"?(o--,i):void 0,s&&Dw(n[0],n[1],s)&&(i=o<3?void 0:i,o=1),t=Object(t);++r<o;){var l=n[r];l&&e(t,l,r,i)}return t})}var Bw=Object.prototype;function sc(e){var t=e&&e.constructor,n=typeof t=="function"&&t.prototype||Bw;return e===n}function Hw(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}var jw="[object Arguments]";function Md(e){return vr(e)&&Kr(e)==jw}var eg=Object.prototype,Ww=eg.hasOwnProperty,Uw=eg.propertyIsEnumerable,hs=Md(function(){return arguments}())?Md:function(e){return vr(e)&&Ww.call(e,"callee")&&!Uw.call(e,"callee")};function Vw(){return!1}var tg=typeof tn=="object"&&tn&&!tn.nodeType&&tn,Fd=tg&&typeof nn=="object"&&nn&&!nn.nodeType&&nn,Kw=Fd&&Fd.exports===tg,Nd=Kw?yn.Buffer:void 0,Gw=Nd?Nd.isBuffer:void 0,ps=Gw||Vw,Yw="[object Arguments]",Xw="[object Array]",qw="[object Boolean]",Zw="[object Date]",Jw="[object Error]",Qw="[object Function]",e_="[object Map]",t_="[object Number]",n_="[object Object]",r_="[object RegExp]",o_="[object Set]",i_="[object String]",s_="[object WeakMap]",l_="[object ArrayBuffer]",a_="[object DataView]",c_="[object Float32Array]",u_="[object Float64Array]",d_="[object Int8Array]",f_="[object Int16Array]",h_="[object Int32Array]",p_="[object Uint8Array]",g_="[object Uint8ClampedArray]",v_="[object Uint16Array]",m_="[object Uint32Array]",at={};at[c_]=at[u_]=at[d_]=at[f_]=at[h_]=at[p_]=at[g_]=at[v_]=at[m_]=!0;at[Yw]=at[Xw]=at[l_]=at[qw]=at[a_]=at[Zw]=at[Jw]=at[Qw]=at[e_]=at[t_]=at[n_]=at[r_]=at[o_]=at[i_]=at[s_]=!1;function b_(e){return vr(e)&&ic(e.length)&&!!at[Kr(e)]}function y_(e){return function(t){return e(t)}}var ng=typeof tn=="object"&&tn&&!tn.nodeType&&tn,Vo=ng&&typeof nn=="object"&&nn&&!nn.nodeType&&nn,x_=Vo&&Vo.exports===ng,wl=x_&&qp.process,Dd=function(){try{var e=Vo&&Vo.require&&Vo.require("util").types;return e||wl&&wl.binding&&wl.binding("util")}catch{}}(),zd=Dd&&Dd.isTypedArray,lc=zd?y_(zd):b_,w_=Object.prototype,__=w_.hasOwnProperty;function rg(e,t){var n=un(e),r=!n&&hs(e),o=!n&&!r&&ps(e),i=!n&&!r&&!o&&lc(e),s=n||r||o||i,l=s?Hw(e.length,String):[],a=l.length;for(var c in e)(t||__.call(e,c))&&!(s&&(c=="length"||o&&(c=="offset"||c=="parent")||i&&(c=="buffer"||c=="byteLength"||c=="byteOffset")||rc(c,a)))&&l.push(c);return l}function og(e,t){return function(n){return e(t(n))}}var C_=og(Object.keys,Object),S_=Object.prototype,E_=S_.hasOwnProperty;function T_(e){if(!sc(e))return C_(e);var t=[];for(var n in Object(e))E_.call(e,n)&&n!="constructor"&&t.push(n);return t}function ac(e){return So(e)?rg(e):T_(e)}function $_(e){var t=[];if(e!=null)for(var n in Object(e))t.push(n);return t}var P_=Object.prototype,O_=P_.hasOwnProperty;function k_(e){if(!bn(e))return $_(e);var t=sc(e),n=[];for(var r in e)r=="constructor"&&(t||!O_.call(e,r))||n.push(r);return n}function ig(e){return So(e)?rg(e,!0):k_(e)}var A_=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,I_=/^\w*$/;function cc(e,t){if(un(e))return!1;var n=typeof e;return n=="number"||n=="symbol"||n=="boolean"||e==null||Bs(e)?!0:I_.test(e)||!A_.test(e)||t!=null&&e in Object(t)}var li=Yr(Object,"create");function R_(){this.__data__=li?li(null):{},this.size=0}function L_(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}var M_="__lodash_hash_undefined__",F_=Object.prototype,N_=F_.hasOwnProperty;function D_(e){var t=this.__data__;if(li){var n=t[e];return n===M_?void 0:n}return N_.call(t,e)?t[e]:void 0}var z_=Object.prototype,B_=z_.hasOwnProperty;function H_(e){var t=this.__data__;return li?t[e]!==void 0:B_.call(t,e)}var j_="__lodash_hash_undefined__";function W_(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=li&&t===void 0?j_:t,this}function Hr(e){var t=-1,n=e==null?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}Hr.prototype.clear=R_;Hr.prototype.delete=L_;Hr.prototype.get=D_;Hr.prototype.has=H_;Hr.prototype.set=W_;function U_(){this.__data__=[],this.size=0}function Hs(e,t){for(var n=e.length;n--;)if(vi(e[n][0],t))return n;return-1}var V_=Array.prototype,K_=V_.splice;function G_(e){var t=this.__data__,n=Hs(t,e);if(n<0)return!1;var r=t.length-1;return n==r?t.pop():K_.call(t,n,1),--this.size,!0}function Y_(e){var t=this.__data__,n=Hs(t,e);return n<0?void 0:t[n][1]}function X_(e){return Hs(this.__data__,e)>-1}function q_(e,t){var n=this.__data__,r=Hs(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this}function Yn(e){var t=-1,n=e==null?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}Yn.prototype.clear=U_;Yn.prototype.delete=G_;Yn.prototype.get=Y_;Yn.prototype.has=X_;Yn.prototype.set=q_;var ai=Yr(yn,"Map");function Z_(){this.size=0,this.__data__={hash:new Hr,map:new(ai||Yn),string:new Hr}}function J_(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}function js(e,t){var n=e.__data__;return J_(t)?n[typeof t=="string"?"string":"hash"]:n.map}function Q_(e){var t=js(this,e).delete(e);return this.size-=t?1:0,t}function eC(e){return js(this,e).get(e)}function tC(e){return js(this,e).has(e)}function nC(e,t){var n=js(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this}function Xn(e){var t=-1,n=e==null?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}Xn.prototype.clear=Z_;Xn.prototype.delete=Q_;Xn.prototype.get=eC;Xn.prototype.has=tC;Xn.prototype.set=nC;var rC="Expected a function";function uc(e,t){if(typeof e!="function"||t!=null&&typeof t!="function")throw new TypeError(rC);var n=function(){var r=arguments,o=t?t.apply(this,r):r[0],i=n.cache;if(i.has(o))return i.get(o);var s=e.apply(this,r);return n.cache=i.set(o,s)||i,s};return n.cache=new(uc.Cache||Xn),n}uc.Cache=Xn;var oC=500;function iC(e){var t=uc(e,function(r){return n.size===oC&&n.clear(),r}),n=t.cache;return t}var sC=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,lC=/\\(\\)?/g,aC=iC(function(e){var t=[];return e.charCodeAt(0)===46&&t.push(""),e.replace(sC,function(n,r,o,i){t.push(o?i.replace(lC,"$1"):r||n)}),t});function mi(e){return e==null?"":Qp(e)}function sg(e,t){return un(e)?e:cc(e,t)?[e]:aC(mi(e))}function Ws(e){if(typeof e=="string"||Bs(e))return e;var t=e+"";return t=="0"&&1/e==-1/0?"-0":t}function lg(e,t){t=sg(t,e);for(var n=0,r=t.length;e!=null&&n<r;)e=e[Ws(t[n++])];return n&&n==r?e:void 0}function cC(e,t,n){var r=e==null?void 0:lg(e,t);return r===void 0?n:r}function uC(e,t){for(var n=-1,r=t.length,o=e.length;++n<r;)e[o+n]=t[n];return e}var ag=og(Object.getPrototypeOf,Object),dC="[object Object]",fC=Function.prototype,hC=Object.prototype,cg=fC.toString,pC=hC.hasOwnProperty,gC=cg.call(Object);function vC(e){if(!vr(e)||Kr(e)!=dC)return!1;var t=ag(e);if(t===null)return!0;var n=pC.call(t,"constructor")&&t.constructor;return typeof n=="function"&&n instanceof n&&cg.call(n)==gC}function mC(e,t,n){var r=-1,o=e.length;t<0&&(t=-t>o?0:o+t),n=n>o?o:n,n<0&&(n+=o),o=t>n?0:n-t>>>0,t>>>=0;for(var i=Array(o);++r<o;)i[r]=e[r+t];return i}function bC(e,t,n){var r=e.length;return n=n===void 0?r:n,mC(e,t,n)}var yC="\\ud800-\\udfff",xC="\\u0300-\\u036f",wC="\\ufe20-\\ufe2f",_C="\\u20d0-\\u20ff",CC=xC+wC+_C,SC="\\ufe0e\\ufe0f",EC="\\u200d",TC=RegExp("["+EC+yC+CC+SC+"]");function ug(e){return TC.test(e)}function $C(e){return e.split("")}var dg="\\ud800-\\udfff",PC="\\u0300-\\u036f",OC="\\ufe20-\\ufe2f",kC="\\u20d0-\\u20ff",AC=PC+OC+kC,IC="\\ufe0e\\ufe0f",RC="["+dg+"]",ca="["+AC+"]",ua="\\ud83c[\\udffb-\\udfff]",LC="(?:"+ca+"|"+ua+")",fg="[^"+dg+"]",hg="(?:\\ud83c[\\udde6-\\uddff]){2}",pg="[\\ud800-\\udbff][\\udc00-\\udfff]",MC="\\u200d",gg=LC+"?",vg="["+IC+"]?",FC="(?:"+MC+"(?:"+[fg,hg,pg].join("|")+")"+vg+gg+")*",NC=vg+gg+FC,DC="(?:"+[fg+ca+"?",ca,hg,pg,RC].join("|")+")",zC=RegExp(ua+"(?="+ua+")|"+DC+NC,"g");function BC(e){return e.match(zC)||[]}function HC(e){return ug(e)?BC(e):$C(e)}function jC(e){return function(t){t=mi(t);var n=ug(t)?HC(t):void 0,r=n?n[0]:t.charAt(0),o=n?bC(n,1).join(""):t.slice(1);return r[e]()+o}}var mg=jC("toUpperCase");function WC(e){return mg(mi(e).toLowerCase())}function UC(e,t,n,r){for(var o=-1,i=e==null?0:e.length;++o<i;)n=t(n,e[o],o,e);return n}function VC(e){return function(t){return e==null?void 0:e[t]}}var KC={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"},GC=VC(KC),YC=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,XC="\\u0300-\\u036f",qC="\\ufe20-\\ufe2f",ZC="\\u20d0-\\u20ff",JC=XC+qC+ZC,QC="["+JC+"]",eS=RegExp(QC,"g");function tS(e){return e=mi(e),e&&e.replace(YC,GC).replace(eS,"")}var nS=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g;function rS(e){return e.match(nS)||[]}var oS=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/;function iS(e){return oS.test(e)}var bg="\\ud800-\\udfff",sS="\\u0300-\\u036f",lS="\\ufe20-\\ufe2f",aS="\\u20d0-\\u20ff",cS=sS+lS+aS,yg="\\u2700-\\u27bf",xg="a-z\\xdf-\\xf6\\xf8-\\xff",uS="\\xac\\xb1\\xd7\\xf7",dS="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",fS="\\u2000-\\u206f",hS=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",wg="A-Z\\xc0-\\xd6\\xd8-\\xde",pS="\\ufe0e\\ufe0f",_g=uS+dS+fS+hS,Cg="['’]",Bd="["+_g+"]",gS="["+cS+"]",Sg="\\d+",vS="["+yg+"]",Eg="["+xg+"]",Tg="[^"+bg+_g+Sg+yg+xg+wg+"]",mS="\\ud83c[\\udffb-\\udfff]",bS="(?:"+gS+"|"+mS+")",yS="[^"+bg+"]",$g="(?:\\ud83c[\\udde6-\\uddff]){2}",Pg="[\\ud800-\\udbff][\\udc00-\\udfff]",io="["+wg+"]",xS="\\u200d",Hd="(?:"+Eg+"|"+Tg+")",wS="(?:"+io+"|"+Tg+")",jd="(?:"+Cg+"(?:d|ll|m|re|s|t|ve))?",Wd="(?:"+Cg+"(?:D|LL|M|RE|S|T|VE))?",Og=bS+"?",kg="["+pS+"]?",_S="(?:"+xS+"(?:"+[yS,$g,Pg].join("|")+")"+kg+Og+")*",CS="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",SS="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",ES=kg+Og+_S,TS="(?:"+[vS,$g,Pg].join("|")+")"+ES,$S=RegExp([io+"?"+Eg+"+"+jd+"(?="+[Bd,io,"$"].join("|")+")",wS+"+"+Wd+"(?="+[Bd,io+Hd,"$"].join("|")+")",io+"?"+Hd+"+"+jd,io+"+"+Wd,SS,CS,Sg,TS].join("|"),"g");function PS(e){return e.match($S)||[]}function OS(e,t,n){return e=mi(e),t=t,t===void 0?iS(e)?PS(e):rS(e):e.match(t)||[]}var kS="['’]",AS=RegExp(kS,"g");function IS(e){return function(t){return UC(OS(tS(t).replace(AS,"")),e,"")}}var Ud=IS(function(e,t,n){return t=t.toLowerCase(),e+(n?WC(t):t)});function RS(){this.__data__=new Yn,this.size=0}function LS(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n}function MS(e){return this.__data__.get(e)}function FS(e){return this.__data__.has(e)}var NS=200;function DS(e,t){var n=this.__data__;if(n instanceof Yn){var r=n.__data__;if(!ai||r.length<NS-1)return r.push([e,t]),this.size=++n.size,this;n=this.__data__=new Xn(r)}return n.set(e,t),this.size=n.size,this}function kn(e){var t=this.__data__=new Yn(e);this.size=t.size}kn.prototype.clear=RS;kn.prototype.delete=LS;kn.prototype.get=MS;kn.prototype.has=FS;kn.prototype.set=DS;var Ag=typeof tn=="object"&&tn&&!tn.nodeType&&tn,Vd=Ag&&typeof nn=="object"&&nn&&!nn.nodeType&&nn,zS=Vd&&Vd.exports===Ag,Kd=zS?yn.Buffer:void 0;Kd&&Kd.allocUnsafe;function BS(e,t){return e.slice()}function HS(e,t){for(var n=-1,r=e==null?0:e.length,o=0,i=[];++n<r;){var s=e[n];t(s,n,e)&&(i[o++]=s)}return i}function jS(){return[]}var WS=Object.prototype,US=WS.propertyIsEnumerable,Gd=Object.getOwnPropertySymbols,VS=Gd?function(e){return e==null?[]:(e=Object(e),HS(Gd(e),function(t){return US.call(e,t)}))}:jS;function KS(e,t,n){var r=t(e);return un(e)?r:uC(r,n(e))}function Yd(e){return KS(e,ac,VS)}var da=Yr(yn,"DataView"),fa=Yr(yn,"Promise"),ha=Yr(yn,"Set"),Xd="[object Map]",GS="[object Object]",qd="[object Promise]",Zd="[object Set]",Jd="[object WeakMap]",Qd="[object DataView]",YS=Gr(da),XS=Gr(ai),qS=Gr(fa),ZS=Gr(ha),JS=Gr(aa),ir=Kr;(da&&ir(new da(new ArrayBuffer(1)))!=Qd||ai&&ir(new ai)!=Xd||fa&&ir(fa.resolve())!=qd||ha&&ir(new ha)!=Zd||aa&&ir(new aa)!=Jd)&&(ir=function(e){var t=Kr(e),n=t==GS?e.constructor:void 0,r=n?Gr(n):"";if(r)switch(r){case YS:return Qd;case XS:return Xd;case qS:return qd;case ZS:return Zd;case JS:return Jd}return t});var gs=yn.Uint8Array;function QS(e){var t=new e.constructor(e.byteLength);return new gs(t).set(new gs(e)),t}function eE(e,t){var n=QS(e.buffer);return new e.constructor(n,e.byteOffset,e.length)}function tE(e){return typeof e.constructor=="function"&&!sc(e)?yw(ag(e)):{}}var nE="__lodash_hash_undefined__";function rE(e){return this.__data__.set(e,nE),this}function oE(e){return this.__data__.has(e)}function vs(e){var t=-1,n=e==null?0:e.length;for(this.__data__=new Xn;++t<n;)this.add(e[t])}vs.prototype.add=vs.prototype.push=rE;vs.prototype.has=oE;function iE(e,t){for(var n=-1,r=e==null?0:e.length;++n<r;)if(t(e[n],n,e))return!0;return!1}function sE(e,t){return e.has(t)}var lE=1,aE=2;function Ig(e,t,n,r,o,i){var s=n&lE,l=e.length,a=t.length;if(l!=a&&!(s&&a>l))return!1;var c=i.get(e),u=i.get(t);if(c&&u)return c==t&&u==e;var d=-1,f=!0,m=n&aE?new vs:void 0;for(i.set(e,t),i.set(t,e);++d<l;){var p=e[d],g=t[d];if(r)var x=s?r(g,p,d,t,e,i):r(p,g,d,e,t,i);if(x!==void 0){if(x)continue;f=!1;break}if(m){if(!iE(t,function(h,y){if(!sE(m,y)&&(p===h||o(p,h,n,r,i)))return m.push(y)})){f=!1;break}}else if(!(p===g||o(p,g,n,r,i))){f=!1;break}}return i.delete(e),i.delete(t),f}function cE(e){var t=-1,n=Array(e.size);return e.forEach(function(r,o){n[++t]=[o,r]}),n}function uE(e){var t=-1,n=Array(e.size);return e.forEach(function(r){n[++t]=r}),n}var dE=1,fE=2,hE="[object Boolean]",pE="[object Date]",gE="[object Error]",vE="[object Map]",mE="[object Number]",bE="[object RegExp]",yE="[object Set]",xE="[object String]",wE="[object Symbol]",_E="[object ArrayBuffer]",CE="[object DataView]",ef=gr?gr.prototype:void 0,_l=ef?ef.valueOf:void 0;function SE(e,t,n,r,o,i,s){switch(n){case CE:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case _E:return!(e.byteLength!=t.byteLength||!i(new gs(e),new gs(t)));case hE:case pE:case mE:return vi(+e,+t);case gE:return e.name==t.name&&e.message==t.message;case bE:case xE:return e==t+"";case vE:var l=cE;case yE:var a=r&dE;if(l||(l=uE),e.size!=t.size&&!a)return!1;var c=s.get(e);if(c)return c==t;r|=fE,s.set(e,t);var u=Ig(l(e),l(t),r,o,i,s);return s.delete(e),u;case wE:if(_l)return _l.call(e)==_l.call(t)}return!1}var EE=1,TE=Object.prototype,$E=TE.hasOwnProperty;function PE(e,t,n,r,o,i){var s=n&EE,l=Yd(e),a=l.length,c=Yd(t),u=c.length;if(a!=u&&!s)return!1;for(var d=a;d--;){var f=l[d];if(!(s?f in t:$E.call(t,f)))return!1}var m=i.get(e),p=i.get(t);if(m&&p)return m==t&&p==e;var g=!0;i.set(e,t),i.set(t,e);for(var x=s;++d<a;){f=l[d];var h=e[f],y=t[f];if(r)var P=s?r(y,h,f,t,e,i):r(h,y,f,e,t,i);if(!(P===void 0?h===y||o(h,y,n,r,i):P)){g=!1;break}x||(x=f=="constructor")}if(g&&!x){var b=e.constructor,C=t.constructor;b!=C&&"constructor"in e&&"constructor"in t&&!(typeof b=="function"&&b instanceof b&&typeof C=="function"&&C instanceof C)&&(g=!1)}return i.delete(e),i.delete(t),g}var OE=1,tf="[object Arguments]",nf="[object Array]",Di="[object Object]",kE=Object.prototype,rf=kE.hasOwnProperty;function AE(e,t,n,r,o,i){var s=un(e),l=un(t),a=s?nf:ir(e),c=l?nf:ir(t);a=a==tf?Di:a,c=c==tf?Di:c;var u=a==Di,d=c==Di,f=a==c;if(f&&ps(e)){if(!ps(t))return!1;s=!0,u=!1}if(f&&!u)return i||(i=new kn),s||lc(e)?Ig(e,t,n,r,o,i):SE(e,t,a,n,r,o,i);if(!(n&OE)){var m=u&&rf.call(e,"__wrapped__"),p=d&&rf.call(t,"__wrapped__");if(m||p){var g=m?e.value():e,x=p?t.value():t;return i||(i=new kn),o(g,x,n,r,i)}}return f?(i||(i=new kn),PE(e,t,n,r,o,i)):!1}function dc(e,t,n,r,o){return e===t?!0:e==null||t==null||!vr(e)&&!vr(t)?e!==e&&t!==t:AE(e,t,n,r,dc,o)}var IE=1,RE=2;function LE(e,t,n,r){var o=n.length,i=o;if(e==null)return!i;for(e=Object(e);o--;){var s=n[o];if(s[2]?s[1]!==e[s[0]]:!(s[0]in e))return!1}for(;++o<i;){s=n[o];var l=s[0],a=e[l],c=s[1];if(s[2]){if(a===void 0&&!(l in e))return!1}else{var u=new kn,d;if(!(d===void 0?dc(c,a,IE|RE,r,u):d))return!1}}return!0}function Rg(e){return e===e&&!bn(e)}function ME(e){for(var t=ac(e),n=t.length;n--;){var r=t[n],o=e[r];t[n]=[r,o,Rg(o)]}return t}function Lg(e,t){return function(n){return n==null?!1:n[e]===t&&(t!==void 0||e in Object(n))}}function FE(e){var t=ME(e);return t.length==1&&t[0][2]?Lg(t[0][0],t[0][1]):function(n){return n===e||LE(n,e,t)}}function NE(e,t){return e!=null&&t in Object(e)}function DE(e,t,n){t=sg(t,e);for(var r=-1,o=t.length,i=!1;++r<o;){var s=Ws(t[r]);if(!(i=e!=null&&n(e,s)))break;e=e[s]}return i||++r!=o?i:(o=e==null?0:e.length,!!o&&ic(o)&&rc(s,o)&&(un(e)||hs(e)))}function zE(e,t){return e!=null&&DE(e,t,NE)}var BE=1,HE=2;function jE(e,t){return cc(e)&&Rg(t)?Lg(Ws(e),t):function(n){var r=cC(n,e);return r===void 0&&r===t?zE(n,e):dc(t,r,BE|HE)}}function WE(e){return function(t){return t==null?void 0:t[e]}}function UE(e){return function(t){return lg(t,e)}}function VE(e){return cc(e)?WE(Ws(e)):UE(e)}function KE(e){return typeof e=="function"?e:e==null?tc:typeof e=="object"?un(e)?jE(e[0],e[1]):FE(e):VE(e)}function GE(e){return function(t,n,r){for(var o=-1,i=Object(t),s=r(t),l=s.length;l--;){var a=s[++o];if(n(i[a],a,i)===!1)break}return t}}var Mg=GE();function YE(e,t){return e&&Mg(e,t,ac)}function XE(e,t){return function(n,r){if(n==null)return n;if(!So(n))return e(n,r);for(var o=n.length,i=-1,s=Object(n);++i<o&&r(s[i],i,s)!==!1;);return n}}var qE=XE(YE),Cl=function(){return yn.Date.now()},ZE="Expected a function",JE=Math.max,QE=Math.min;function e2(e,t,n){var r,o,i,s,l,a,c=0,u=!1,d=!1,f=!0;if(typeof e!="function")throw new TypeError(ZE);t=Ad(t)||0,bn(n)&&(u=!!n.leading,d="maxWait"in n,i=d?JE(Ad(n.maxWait)||0,t):i,f="trailing"in n?!!n.trailing:f);function m(S){var v=r,E=o;return r=o=void 0,c=S,s=e.apply(E,v),s}function p(S){return c=S,l=setTimeout(h,t),u?m(S):s}function g(S){var v=S-a,E=S-c,A=t-v;return d?QE(A,i-E):A}function x(S){var v=S-a,E=S-c;return a===void 0||v>=t||v<0||d&&E>=i}function h(){var S=Cl();if(x(S))return y(S);l=setTimeout(h,g(S))}function y(S){return l=void 0,f&&r?m(S):(r=o=void 0,s)}function P(){l!==void 0&&clearTimeout(l),c=0,r=a=o=l=void 0}function b(){return l===void 0?s:y(Cl())}function C(){var S=Cl(),v=x(S);if(r=arguments,o=this,a=S,v){if(l===void 0)return p(a);if(d)return clearTimeout(l),l=setTimeout(h,t),m(a)}return l===void 0&&(l=setTimeout(h,t)),s}return C.cancel=P,C.flush=b,C}function pa(e,t,n){(n!==void 0&&!vi(e[t],n)||n===void 0&&!(t in e))&&oc(e,t,n)}function t2(e){return vr(e)&&So(e)}function ga(e,t){if(!(t==="constructor"&&typeof e[t]=="function")&&t!="__proto__")return e[t]}function n2(e){return Lw(e,ig(e))}function r2(e,t,n,r,o,i,s){var l=ga(e,n),a=ga(t,n),c=s.get(a);if(c){pa(e,n,c);return}var u=i?i(l,a,n+"",e,t,s):void 0,d=u===void 0;if(d){var f=un(a),m=!f&&ps(a),p=!f&&!m&&lc(a);u=a,f||m||p?un(l)?u=l:t2(l)?u=ww(l):m?(d=!1,u=BS(a)):p?(d=!1,u=eE(a)):u=[]:vC(a)||hs(a)?(u=l,hs(l)?u=n2(l):(!bn(l)||nc(l))&&(u=tE(a))):d=!1}d&&(s.set(a,u),o(u,a,r,i,s),s.delete(a)),pa(e,n,u)}function Fg(e,t,n,r,o){e!==t&&Mg(t,function(i,s){if(o||(o=new kn),bn(i))r2(e,t,s,n,Fg,r,o);else{var l=r?r(ga(e,s),i,s+"",e,t,o):void 0;l===void 0&&(l=i),pa(e,s,l)}},ig)}function o2(e,t){var n=-1,r=So(e)?Array(e.length):[];return qE(e,function(o,i,s){r[++n]=t(o,i,s)}),r}function i2(e,t){var n=un(e)?Jp:o2;return n(e,KE(t))}var Fo=zw(function(e,t,n){Fg(e,t,n)});const ci="naive-ui-style";function Eo(e,t,n){if(!t)return;const r=gi(),o=V(()=>{const{value:l}=t;if(!l)return;const a=l[e];if(a)return a}),i=Se(pr,null),s=()=>{Wr(()=>{const{value:l}=n,a=`${l}${e}Rtl`;if(iy(a,r))return;const{value:c}=o;c&&c.style.mount({id:a,head:!0,anchorMetaName:ci,props:{bPrefix:l?`.${l}-`:void 0},ssr:r,parent:i==null?void 0:i.styleMountTarget})})};return r?s():xr(s),o}const Xr={fontFamily:'v-sans, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"',fontFamilyMono:"v-mono, SFMono-Regular, Menlo, Consolas, Courier, monospace",fontWeight:"400",fontWeightStrong:"500",cubicBezierEaseInOut:"cubic-bezier(.4, 0, .2, 1)",cubicBezierEaseOut:"cubic-bezier(0, 0, .2, 1)",cubicBezierEaseIn:"cubic-bezier(.4, 0, 1, 1)",borderRadius:"3px",borderRadiusSmall:"2px",fontSize:"14px",fontSizeMini:"12px",fontSizeTiny:"12px",fontSizeSmall:"14px",fontSizeMedium:"14px",fontSizeLarge:"15px",fontSizeHuge:"16px",lineHeight:"1.6",heightMini:"16px",heightTiny:"22px",heightSmall:"28px",heightMedium:"34px",heightLarge:"40px",heightHuge:"46px"},{fontSize:s2,fontFamily:l2,lineHeight:a2}=Xr,Ng=U("body",`
 margin: 0;
 font-size: ${s2};
 font-family: ${l2};
 line-height: ${a2};
 -webkit-text-size-adjust: 100%;
 -webkit-tap-highlight-color: transparent;
`,[U("input",`
 font-family: inherit;
 font-size: inherit;
 `)]);function Us(e,t,n){if(!t)return;const r=gi(),o=Se(pr,null),i=()=>{const s=n.value;t.mount({id:s===void 0?e:s+e,head:!0,anchorMetaName:ci,props:{bPrefix:s?`.${s}-`:void 0},ssr:r,parent:o==null?void 0:o.styleMountTarget}),o!=null&&o.preflightStyleDisabled||Ng.mount({id:"n-global",head:!0,anchorMetaName:ci,ssr:r,parent:o==null?void 0:o.styleMountTarget})};r?i():xr(i)}function it(e,t,n,r,o,i){const s=gi(),l=Se(pr,null);if(n){const c=()=>{const u=i==null?void 0:i.value;n.mount({id:u===void 0?t:u+t,head:!0,props:{bPrefix:u?`.${u}-`:void 0},anchorMetaName:ci,ssr:s,parent:l==null?void 0:l.styleMountTarget}),l!=null&&l.preflightStyleDisabled||Ng.mount({id:"n-global",head:!0,anchorMetaName:ci,ssr:s,parent:l==null?void 0:l.styleMountTarget})};s?c():xr(c)}return V(()=>{var c;const{theme:{common:u,self:d,peers:f={}}={},themeOverrides:m={},builtinThemeOverrides:p={}}=o,{common:g,peers:x}=m,{common:h=void 0,[e]:{common:y=void 0,self:P=void 0,peers:b={}}={}}=(l==null?void 0:l.mergedThemeRef.value)||{},{common:C=void 0,[e]:S={}}=(l==null?void 0:l.mergedThemeOverridesRef.value)||{},{common:v,peers:E={}}=S,A=Fo({},u||y||h||r.common,C,v,g),F=Fo((c=d||P||r.self)===null||c===void 0?void 0:c(A),p,S,m);return{common:A,self:F,peers:Fo({},r.peers,b,f),peerOverrides:Fo({},p.peers,E,x)}})}it.props={theme:Object,themeOverrides:Object,builtinThemeOverrides:Object};const c2=oe("base-icon",`
 height: 1em;
 width: 1em;
 line-height: 1em;
 text-align: center;
 display: inline-block;
 position: relative;
 fill: currentColor;
 transform: translateZ(0);
`,[U("svg",`
 height: 1em;
 width: 1em;
 `)]),Vs=ge({name:"BaseIcon",props:{role:String,ariaLabel:String,ariaDisabled:{type:Boolean,default:void 0},ariaHidden:{type:Boolean,default:void 0},clsPrefix:{type:String,required:!0},onClick:Function,onMousedown:Function,onMouseup:Function},setup(e){Us("-base-icon",c2,Qe(e,"clsPrefix"))},render(){return T("i",{class:`${this.clsPrefix}-base-icon`,onClick:this.onClick,onMousedown:this.onMousedown,onMouseup:this.onMouseup,role:this.role,"aria-label":this.ariaLabel,"aria-hidden":this.ariaHidden,"aria-disabled":this.ariaDisabled},this.$slots)}}),fc=ge({name:"BaseIconSwitchTransition",setup(e,{slots:t}){const n=hi();return()=>T(mn,{name:"icon-switch-transition",appear:n.value},t)}});function bi(e,t){const n=ge({render(){return t()}});return ge({name:mg(e),setup(){var r;const o=(r=Se(pr,null))===null||r===void 0?void 0:r.mergedIconsRef;return()=>{var i;const s=(i=o==null?void 0:o.value)===null||i===void 0?void 0:i[e];return s?s():T(n,null)}}})}const u2=ge({name:"ChevronRight",render(){return T("svg",{viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},T("path",{d:"M5.64645 3.14645C5.45118 3.34171 5.45118 3.65829 5.64645 3.85355L9.79289 8L5.64645 12.1464C5.45118 12.3417 5.45118 12.6583 5.64645 12.8536C5.84171 13.0488 6.15829 13.0488 6.35355 12.8536L10.8536 8.35355C11.0488 8.15829 11.0488 7.84171 10.8536 7.64645L6.35355 3.14645C6.15829 2.95118 5.84171 2.95118 5.64645 3.14645Z",fill:"currentColor"}))}}),d2=bi("close",()=>T("svg",{viewBox:"0 0 12 12",version:"1.1",xmlns:"http://www.w3.org/2000/svg","aria-hidden":!0},T("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},T("g",{fill:"currentColor","fill-rule":"nonzero"},T("path",{d:"M2.08859116,2.2156945 L2.14644661,2.14644661 C2.32001296,1.97288026 2.58943736,1.95359511 2.7843055,2.08859116 L2.85355339,2.14644661 L6,5.293 L9.14644661,2.14644661 C9.34170876,1.95118446 9.65829124,1.95118446 9.85355339,2.14644661 C10.0488155,2.34170876 10.0488155,2.65829124 9.85355339,2.85355339 L6.707,6 L9.85355339,9.14644661 C10.0271197,9.32001296 10.0464049,9.58943736 9.91140884,9.7843055 L9.85355339,9.85355339 C9.67998704,10.0271197 9.41056264,10.0464049 9.2156945,9.91140884 L9.14644661,9.85355339 L6,6.707 L2.85355339,9.85355339 C2.65829124,10.0488155 2.34170876,10.0488155 2.14644661,9.85355339 C1.95118446,9.65829124 1.95118446,9.34170876 2.14644661,9.14644661 L5.293,6 L2.14644661,2.85355339 C1.97288026,2.67998704 1.95359511,2.41056264 2.08859116,2.2156945 L2.14644661,2.14644661 L2.08859116,2.2156945 Z"}))))),hc=bi("error",()=>T("svg",{viewBox:"0 0 48 48",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},T("g",{stroke:"none","stroke-width":"1","fill-rule":"evenodd"},T("g",{"fill-rule":"nonzero"},T("path",{d:"M24,4 C35.045695,4 44,12.954305 44,24 C44,35.045695 35.045695,44 24,44 C12.954305,44 4,35.045695 4,24 C4,12.954305 12.954305,4 24,4 Z M17.8838835,16.1161165 L17.7823881,16.0249942 C17.3266086,15.6583353 16.6733914,15.6583353 16.2176119,16.0249942 L16.1161165,16.1161165 L16.0249942,16.2176119 C15.6583353,16.6733914 15.6583353,17.3266086 16.0249942,17.7823881 L16.1161165,17.8838835 L22.233,24 L16.1161165,30.1161165 L16.0249942,30.2176119 C15.6583353,30.6733914 15.6583353,31.3266086 16.0249942,31.7823881 L16.1161165,31.8838835 L16.2176119,31.9750058 C16.6733914,32.3416647 17.3266086,32.3416647 17.7823881,31.9750058 L17.8838835,31.8838835 L24,25.767 L30.1161165,31.8838835 L30.2176119,31.9750058 C30.6733914,32.3416647 31.3266086,32.3416647 31.7823881,31.9750058 L31.8838835,31.8838835 L31.9750058,31.7823881 C32.3416647,31.3266086 32.3416647,30.6733914 31.9750058,30.2176119 L31.8838835,30.1161165 L25.767,24 L31.8838835,17.8838835 L31.9750058,17.7823881 C32.3416647,17.3266086 32.3416647,16.6733914 31.9750058,16.2176119 L31.8838835,16.1161165 L31.7823881,16.0249942 C31.3266086,15.6583353 30.6733914,15.6583353 30.2176119,16.0249942 L30.1161165,16.1161165 L24,22.233 L17.8838835,16.1161165 L17.7823881,16.0249942 L17.8838835,16.1161165 Z"}))))),ms=bi("info",()=>T("svg",{viewBox:"0 0 28 28",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},T("g",{stroke:"none","stroke-width":"1","fill-rule":"evenodd"},T("g",{"fill-rule":"nonzero"},T("path",{d:"M14,2 C20.6274,2 26,7.37258 26,14 C26,20.6274 20.6274,26 14,26 C7.37258,26 2,20.6274 2,14 C2,7.37258 7.37258,2 14,2 Z M14,11 C13.4477,11 13,11.4477 13,12 L13,12 L13,20 C13,20.5523 13.4477,21 14,21 C14.5523,21 15,20.5523 15,20 L15,20 L15,12 C15,11.4477 14.5523,11 14,11 Z M14,6.75 C13.3096,6.75 12.75,7.30964 12.75,8 C12.75,8.69036 13.3096,9.25 14,9.25 C14.6904,9.25 15.25,8.69036 15.25,8 C15.25,7.30964 14.6904,6.75 14,6.75 Z"}))))),pc=bi("success",()=>T("svg",{viewBox:"0 0 48 48",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},T("g",{stroke:"none","stroke-width":"1","fill-rule":"evenodd"},T("g",{"fill-rule":"nonzero"},T("path",{d:"M24,4 C35.045695,4 44,12.954305 44,24 C44,35.045695 35.045695,44 24,44 C12.954305,44 4,35.045695 4,24 C4,12.954305 12.954305,4 24,4 Z M32.6338835,17.6161165 C32.1782718,17.1605048 31.4584514,17.1301307 30.9676119,17.5249942 L30.8661165,17.6161165 L20.75,27.732233 L17.1338835,24.1161165 C16.6457281,23.6279612 15.8542719,23.6279612 15.3661165,24.1161165 C14.9105048,24.5717282 14.8801307,25.2915486 15.2749942,25.7823881 L15.3661165,25.8838835 L19.8661165,30.3838835 C20.3217282,30.8394952 21.0415486,30.8698693 21.5323881,30.4750058 L21.6338835,30.3838835 L32.6338835,19.3838835 C33.1220388,18.8957281 33.1220388,18.1042719 32.6338835,17.6161165 Z"}))))),gc=bi("warning",()=>T("svg",{viewBox:"0 0 24 24",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},T("g",{stroke:"none","stroke-width":"1","fill-rule":"evenodd"},T("g",{"fill-rule":"nonzero"},T("path",{d:"M12,2 C17.523,2 22,6.478 22,12 C22,17.522 17.523,22 12,22 C6.477,22 2,17.522 2,12 C2,6.478 6.477,2 12,2 Z M12.0018002,15.0037242 C11.450254,15.0037242 11.0031376,15.4508407 11.0031376,16.0023869 C11.0031376,16.553933 11.450254,17.0010495 12.0018002,17.0010495 C12.5533463,17.0010495 13.0004628,16.553933 13.0004628,16.0023869 C13.0004628,15.4508407 12.5533463,15.0037242 12.0018002,15.0037242 Z M11.99964,7 C11.4868042,7.00018474 11.0642719,7.38637706 11.0066858,7.8837365 L11,8.00036004 L11.0018003,13.0012393 L11.00857,13.117858 C11.0665141,13.6151758 11.4893244,14.0010638 12.0021602,14.0008793 C12.514996,14.0006946 12.9375283,13.6145023 12.9951144,13.1171428 L13.0018002,13.0005193 L13,7.99964009 L12.9932303,7.8830214 C12.9352861,7.38570354 12.5124758,6.99981552 11.99964,7 Z"}))))),{cubicBezierEaseInOut:f2}=Xr;function bs({originalTransform:e="",left:t=0,top:n=0,transition:r=`all .3s ${f2} !important`}={}){return[U("&.icon-switch-transition-enter-from, &.icon-switch-transition-leave-to",{transform:`${e} scale(0.75)`,left:t,top:n,opacity:0}),U("&.icon-switch-transition-enter-to, &.icon-switch-transition-leave-from",{transform:`scale(1) ${e}`,left:t,top:n,opacity:1}),U("&.icon-switch-transition-enter-active, &.icon-switch-transition-leave-active",{transformOrigin:"center",position:"absolute",left:t,top:n,transition:r})]}const h2=oe("base-close",`
 display: flex;
 align-items: center;
 justify-content: center;
 cursor: pointer;
 background-color: transparent;
 color: var(--n-close-icon-color);
 border-radius: var(--n-close-border-radius);
 height: var(--n-close-size);
 width: var(--n-close-size);
 font-size: var(--n-close-icon-size);
 outline: none;
 border: none;
 position: relative;
 padding: 0;
`,[X("absolute",`
 height: var(--n-close-icon-size);
 width: var(--n-close-icon-size);
 `),U("&::before",`
 content: "";
 position: absolute;
 width: var(--n-close-size);
 height: var(--n-close-size);
 left: 50%;
 top: 50%;
 transform: translateY(-50%) translateX(-50%);
 transition: inherit;
 border-radius: inherit;
 `),ur("disabled",[U("&:hover",`
 color: var(--n-close-icon-color-hover);
 `),U("&:hover::before",`
 background-color: var(--n-close-color-hover);
 `),U("&:focus::before",`
 background-color: var(--n-close-color-hover);
 `),U("&:active",`
 color: var(--n-close-icon-color-pressed);
 `),U("&:active::before",`
 background-color: var(--n-close-color-pressed);
 `)]),X("disabled",`
 cursor: not-allowed;
 color: var(--n-close-icon-color-disabled);
 background-color: transparent;
 `),X("round",[U("&::before",`
 border-radius: 50%;
 `)])]),Ks=ge({name:"BaseClose",props:{isButtonTag:{type:Boolean,default:!0},clsPrefix:{type:String,required:!0},disabled:{type:Boolean,default:void 0},focusable:{type:Boolean,default:!0},round:Boolean,onClick:Function,absolute:Boolean},setup(e){return Us("-base-close",h2,Qe(e,"clsPrefix")),()=>{const{clsPrefix:t,disabled:n,absolute:r,round:o,isButtonTag:i}=e;return T(i?"button":"div",{type:i?"button":void 0,tabindex:n||!e.focusable?-1:0,"aria-disabled":n,"aria-label":"close",role:i?void 0:"button",disabled:n,class:[`${t}-base-close`,r&&`${t}-base-close--absolute`,n&&`${t}-base-close--disabled`,o&&`${t}-base-close--round`],onMousedown:l=>{e.focusable||l.preventDefault()},onClick:e.onClick},T(Vs,{clsPrefix:t},{default:()=>T(d2,null)}))}}}),Dg=ge({name:"FadeInExpandTransition",props:{appear:Boolean,group:Boolean,mode:String,onLeave:Function,onAfterLeave:Function,onAfterEnter:Function,width:Boolean,reverse:Boolean},setup(e,{slots:t}){function n(l){e.width?l.style.maxWidth=`${l.offsetWidth}px`:l.style.maxHeight=`${l.offsetHeight}px`,l.offsetWidth}function r(l){e.width?l.style.maxWidth="0":l.style.maxHeight="0",l.offsetWidth;const{onLeave:a}=e;a&&a()}function o(l){e.width?l.style.maxWidth="":l.style.maxHeight="";const{onAfterLeave:a}=e;a&&a()}function i(l){if(l.style.transition="none",e.width){const a=l.offsetWidth;l.style.maxWidth="0",l.offsetWidth,l.style.transition="",l.style.maxWidth=`${a}px`}else if(e.reverse)l.style.maxHeight=`${l.offsetHeight}px`,l.offsetHeight,l.style.transition="",l.style.maxHeight="0";else{const a=l.offsetHeight;l.style.maxHeight="0",l.offsetWidth,l.style.transition="",l.style.maxHeight=`${a}px`}l.offsetWidth}function s(l){var a;e.width?l.style.maxWidth="":e.reverse||(l.style.maxHeight=""),(a=e.onAfterEnter)===null||a===void 0||a.call(e)}return()=>{const{group:l,width:a,appear:c,mode:u}=e,d=l?Lb:mn,f={name:a?"fade-in-width-expand-transition":"fade-in-height-expand-transition",appear:c,onEnter:i,onAfterEnter:s,onBeforeLeave:n,onLeave:r,onAfterLeave:o};return l||(f.mode=u),T(d,f,t)}}}),p2=U([U("@keyframes rotator",`
 0% {
 -webkit-transform: rotate(0deg);
 transform: rotate(0deg);
 }
 100% {
 -webkit-transform: rotate(360deg);
 transform: rotate(360deg);
 }`),oe("base-loading",`
 position: relative;
 line-height: 0;
 width: 1em;
 height: 1em;
 `,[Z("transition-wrapper",`
 position: absolute;
 width: 100%;
 height: 100%;
 `,[bs()]),Z("placeholder",`
 position: absolute;
 left: 50%;
 top: 50%;
 transform: translateX(-50%) translateY(-50%);
 `,[bs({left:"50%",top:"50%",originalTransform:"translateX(-50%) translateY(-50%)"})]),Z("container",`
 animation: rotator 3s linear infinite both;
 `,[Z("icon",`
 height: 1em;
 width: 1em;
 `)])])]),Sl="1.6s",g2={strokeWidth:{type:Number,default:28},stroke:{type:String,default:void 0}},zg=ge({name:"BaseLoading",props:Object.assign({clsPrefix:{type:String,required:!0},show:{type:Boolean,default:!0},scale:{type:Number,default:1},radius:{type:Number,default:100}},g2),setup(e){Us("-base-loading",p2,Qe(e,"clsPrefix"))},render(){const{clsPrefix:e,radius:t,strokeWidth:n,stroke:r,scale:o}=this,i=t/o;return T("div",{class:`${e}-base-loading`,role:"img","aria-label":"loading"},T(fc,null,{default:()=>this.show?T("div",{key:"icon",class:`${e}-base-loading__transition-wrapper`},T("div",{class:`${e}-base-loading__container`},T("svg",{class:`${e}-base-loading__icon`,viewBox:`0 0 ${2*i} ${2*i}`,xmlns:"http://www.w3.org/2000/svg",style:{color:r}},T("g",null,T("animateTransform",{attributeName:"transform",type:"rotate",values:`0 ${i} ${i};270 ${i} ${i}`,begin:"0s",dur:Sl,fill:"freeze",repeatCount:"indefinite"}),T("circle",{class:`${e}-base-loading__icon`,fill:"none",stroke:"currentColor","stroke-width":n,"stroke-linecap":"round",cx:i,cy:i,r:t-n/2,"stroke-dasharray":5.67*t,"stroke-dashoffset":18.48*t},T("animateTransform",{attributeName:"transform",type:"rotate",values:`0 ${i} ${i};135 ${i} ${i};450 ${i} ${i}`,begin:"0s",dur:Sl,fill:"freeze",repeatCount:"indefinite"}),T("animate",{attributeName:"stroke-dashoffset",values:`${5.67*t};${1.42*t};${5.67*t}`,begin:"0s",dur:Sl,fill:"freeze",repeatCount:"indefinite"})))))):T("div",{key:"placeholder",class:`${e}-base-loading__placeholder`},this.$slots)}))}}),{cubicBezierEaseInOut:of}=Xr;function vc({name:e="fade-in",enterDuration:t="0.2s",leaveDuration:n="0.2s",enterCubicBezier:r=of,leaveCubicBezier:o=of}={}){return[U(`&.${e}-transition-enter-active`,{transition:`all ${t} ${r}!important`}),U(`&.${e}-transition-leave-active`,{transition:`all ${n} ${o}!important`}),U(`&.${e}-transition-enter-from, &.${e}-transition-leave-to`,{opacity:0}),U(`&.${e}-transition-leave-from, &.${e}-transition-enter-to`,{opacity:1})]}const ve={neutralBase:"#FFF",neutralInvertBase:"#000",neutralTextBase:"#000",neutralPopover:"#fff",neutralCard:"#fff",neutralModal:"#fff",neutralBody:"#fff",alpha1:"0.82",alpha2:"0.72",alpha3:"0.38",alpha4:"0.24",alpha5:"0.18",alphaClose:"0.6",alphaDisabled:"0.5",alphaAvatar:"0.2",alphaProgressRail:".08",alphaInput:"0",alphaScrollbar:"0.25",alphaScrollbarHover:"0.4",primaryHover:"#36ad6a",primaryDefault:"#18a058",primaryActive:"#0c7a43",primarySuppl:"#36ad6a",infoHover:"#4098fc",infoDefault:"#2080f0",infoActive:"#1060c9",infoSuppl:"#4098fc",errorHover:"#de576d",errorDefault:"#d03050",errorActive:"#ab1f3f",errorSuppl:"#de576d",warningHover:"#fcb040",warningDefault:"#f0a020",warningActive:"#c97c10",warningSuppl:"#fcb040",successHover:"#36ad6a",successDefault:"#18a058",successActive:"#0c7a43",successSuppl:"#36ad6a"},v2=Br(ve.neutralBase),Bg=Br(ve.neutralInvertBase),m2=`rgba(${Bg.slice(0,3).join(", ")}, `;function sf(e){return`${m2+String(e)})`}function It(e){const t=Array.from(Bg);return t[3]=Number(e),Ka(v2,t)}const fn=Object.assign(Object.assign({name:"common"},Xr),{baseColor:ve.neutralBase,primaryColor:ve.primaryDefault,primaryColorHover:ve.primaryHover,primaryColorPressed:ve.primaryActive,primaryColorSuppl:ve.primarySuppl,infoColor:ve.infoDefault,infoColorHover:ve.infoHover,infoColorPressed:ve.infoActive,infoColorSuppl:ve.infoSuppl,successColor:ve.successDefault,successColorHover:ve.successHover,successColorPressed:ve.successActive,successColorSuppl:ve.successSuppl,warningColor:ve.warningDefault,warningColorHover:ve.warningHover,warningColorPressed:ve.warningActive,warningColorSuppl:ve.warningSuppl,errorColor:ve.errorDefault,errorColorHover:ve.errorHover,errorColorPressed:ve.errorActive,errorColorSuppl:ve.errorSuppl,textColorBase:ve.neutralTextBase,textColor1:"rgb(31, 34, 37)",textColor2:"rgb(51, 54, 57)",textColor3:"rgb(118, 124, 130)",textColorDisabled:It(ve.alpha4),placeholderColor:It(ve.alpha4),placeholderColorDisabled:It(ve.alpha5),iconColor:It(ve.alpha4),iconColorHover:ki(It(ve.alpha4),{lightness:.75}),iconColorPressed:ki(It(ve.alpha4),{lightness:.9}),iconColorDisabled:It(ve.alpha5),opacity1:ve.alpha1,opacity2:ve.alpha2,opacity3:ve.alpha3,opacity4:ve.alpha4,opacity5:ve.alpha5,dividerColor:"rgb(239, 239, 245)",borderColor:"rgb(224, 224, 230)",closeIconColor:It(Number(ve.alphaClose)),closeIconColorHover:It(Number(ve.alphaClose)),closeIconColorPressed:It(Number(ve.alphaClose)),closeColorHover:"rgba(0, 0, 0, .09)",closeColorPressed:"rgba(0, 0, 0, .13)",clearColor:It(ve.alpha4),clearColorHover:ki(It(ve.alpha4),{lightness:.75}),clearColorPressed:ki(It(ve.alpha4),{lightness:.9}),scrollbarColor:sf(ve.alphaScrollbar),scrollbarColorHover:sf(ve.alphaScrollbarHover),scrollbarWidth:"5px",scrollbarHeight:"5px",scrollbarBorderRadius:"5px",progressRailColor:It(ve.alphaProgressRail),railColor:"rgb(219, 219, 223)",popoverColor:ve.neutralPopover,tableColor:ve.neutralCard,cardColor:ve.neutralCard,modalColor:ve.neutralModal,bodyColor:ve.neutralBody,tagColor:"#eee",avatarColor:It(ve.alphaAvatar),invertedColor:"rgb(0, 20, 40)",inputColor:It(ve.alphaInput),codeColor:"rgb(244, 244, 248)",tabColor:"rgb(247, 247, 250)",actionColor:"rgb(250, 250, 252)",tableHeaderColor:"rgb(250, 250, 252)",hoverColor:"rgb(243, 243, 245)",tableColorHover:"rgba(0, 0, 100, 0.03)",tableColorStriped:"rgba(0, 0, 100, 0.02)",pressedColor:"rgb(237, 237, 239)",opacityDisabled:ve.alphaDisabled,inputColorDisabled:"rgb(250, 250, 252)",buttonColor2:"rgba(46, 51, 56, .05)",buttonColor2Hover:"rgba(46, 51, 56, .09)",buttonColor2Pressed:"rgba(46, 51, 56, .13)",boxShadow1:"0 1px 2px -2px rgba(0, 0, 0, .08), 0 3px 6px 0 rgba(0, 0, 0, .06), 0 5px 12px 4px rgba(0, 0, 0, .04)",boxShadow2:"0 3px 6px -4px rgba(0, 0, 0, .12), 0 6px 16px 0 rgba(0, 0, 0, .08), 0 9px 28px 8px rgba(0, 0, 0, .05)",boxShadow3:"0 6px 16px -9px rgba(0, 0, 0, .08), 0 9px 28px 0 rgba(0, 0, 0, .05), 0 12px 48px 16px rgba(0, 0, 0, .03)"}),b2={railInsetHorizontalBottom:"auto 2px 4px 2px",railInsetHorizontalTop:"4px 2px auto 2px",railInsetVerticalRight:"2px 4px 2px auto",railInsetVerticalLeft:"2px auto 2px 4px",railColor:"transparent"};function y2(e){const{scrollbarColor:t,scrollbarColorHover:n,scrollbarHeight:r,scrollbarWidth:o,scrollbarBorderRadius:i}=e;return Object.assign(Object.assign({},b2),{height:r,width:o,borderRadius:i,color:t,colorHover:n})}const mc={name:"Scrollbar",common:fn,self:y2},x2=oe("scrollbar",`
 overflow: hidden;
 position: relative;
 z-index: auto;
 height: 100%;
 width: 100%;
`,[U(">",[oe("scrollbar-container",`
 width: 100%;
 overflow: scroll;
 height: 100%;
 min-height: inherit;
 max-height: inherit;
 scrollbar-width: none;
 `,[U("&::-webkit-scrollbar, &::-webkit-scrollbar-track-piece, &::-webkit-scrollbar-thumb",`
 width: 0;
 height: 0;
 display: none;
 `),U(">",[oe("scrollbar-content",`
 box-sizing: border-box;
 min-width: 100%;
 `)])])]),U(">, +",[oe("scrollbar-rail",`
 position: absolute;
 pointer-events: none;
 user-select: none;
 background: var(--n-scrollbar-rail-color);
 -webkit-user-select: none;
 `,[X("horizontal",`
 height: var(--n-scrollbar-height);
 `,[U(">",[Z("scrollbar",`
 height: var(--n-scrollbar-height);
 border-radius: var(--n-scrollbar-border-radius);
 right: 0;
 `)])]),X("horizontal--top",`
 top: var(--n-scrollbar-rail-top-horizontal-top); 
 right: var(--n-scrollbar-rail-right-horizontal-top); 
 bottom: var(--n-scrollbar-rail-bottom-horizontal-top); 
 left: var(--n-scrollbar-rail-left-horizontal-top); 
 `),X("horizontal--bottom",`
 top: var(--n-scrollbar-rail-top-horizontal-bottom); 
 right: var(--n-scrollbar-rail-right-horizontal-bottom); 
 bottom: var(--n-scrollbar-rail-bottom-horizontal-bottom); 
 left: var(--n-scrollbar-rail-left-horizontal-bottom); 
 `),X("vertical",`
 width: var(--n-scrollbar-width);
 `,[U(">",[Z("scrollbar",`
 width: var(--n-scrollbar-width);
 border-radius: var(--n-scrollbar-border-radius);
 bottom: 0;
 `)])]),X("vertical--left",`
 top: var(--n-scrollbar-rail-top-vertical-left); 
 right: var(--n-scrollbar-rail-right-vertical-left); 
 bottom: var(--n-scrollbar-rail-bottom-vertical-left); 
 left: var(--n-scrollbar-rail-left-vertical-left); 
 `),X("vertical--right",`
 top: var(--n-scrollbar-rail-top-vertical-right); 
 right: var(--n-scrollbar-rail-right-vertical-right); 
 bottom: var(--n-scrollbar-rail-bottom-vertical-right); 
 left: var(--n-scrollbar-rail-left-vertical-right); 
 `),X("disabled",[U(">",[Z("scrollbar","pointer-events: none;")])]),U(">",[Z("scrollbar",`
 z-index: 1;
 position: absolute;
 cursor: pointer;
 pointer-events: all;
 background-color: var(--n-scrollbar-color);
 transition: background-color .2s var(--n-scrollbar-bezier);
 `,[vc(),U("&:hover","background-color: var(--n-scrollbar-color-hover);")])])])])]),w2=Object.assign(Object.assign({},it.props),{duration:{type:Number,default:0},scrollable:{type:Boolean,default:!0},xScrollable:Boolean,trigger:{type:String,default:"hover"},useUnifiedContainer:Boolean,triggerDisplayManually:Boolean,container:Function,content:Function,containerClass:String,containerStyle:[String,Object],contentClass:[String,Array],contentStyle:[String,Object],horizontalRailStyle:[String,Object],verticalRailStyle:[String,Object],onScroll:Function,onWheel:Function,onResize:Function,internalOnUpdateScrollLeft:Function,internalHoistYRail:Boolean,yPlacement:{type:String,default:"right"},xPlacement:{type:String,default:"bottom"}}),bc=ge({name:"Scrollbar",props:w2,inheritAttrs:!1,setup(e){const{mergedClsPrefixRef:t,inlineThemeDisabled:n,mergedRtlRef:r}=Pt(e),o=Eo("Scrollbar",r,t),i=D(null),s=D(null),l=D(null),a=D(null),c=D(null),u=D(null),d=D(null),f=D(null),m=D(null),p=D(null),g=D(null),x=D(0),h=D(0),y=D(!1),P=D(!1);let b=!1,C=!1,S,v,E=0,A=0,F=0,B=0;const R=Ny(),j=it("Scrollbar","-scrollbar",x2,mc,e,t),Q=V(()=>{const{value:L}=f,{value:ee}=u,{value:ce}=p;return L===null||ee===null||ce===null?0:Math.min(L,ce*L/ee+Vu(j.value.self.width)*1.5)}),H=V(()=>`${Q.value}px`),te=V(()=>{const{value:L}=m,{value:ee}=d,{value:ce}=g;return L===null||ee===null||ce===null?0:ce*L/ee+Vu(j.value.self.height)*1.5}),W=V(()=>`${te.value}px`),re=V(()=>{const{value:L}=f,{value:ee}=x,{value:ce}=u,{value:Re}=p;if(L===null||ce===null||Re===null)return 0;{const Oe=ce-L;return Oe?ee/Oe*(Re-Q.value):0}}),ue=V(()=>`${re.value}px`),pe=V(()=>{const{value:L}=m,{value:ee}=h,{value:ce}=d,{value:Re}=g;if(L===null||ce===null||Re===null)return 0;{const Oe=ce-L;return Oe?ee/Oe*(Re-te.value):0}}),we=V(()=>`${pe.value}px`),Te=V(()=>{const{value:L}=f,{value:ee}=u;return L!==null&&ee!==null&&ee>L}),Ue=V(()=>{const{value:L}=m,{value:ee}=d;return L!==null&&ee!==null&&ee>L}),st=V(()=>{const{trigger:L}=e;return L==="none"||y.value}),gt=V(()=>{const{trigger:L}=e;return L==="none"||P.value}),be=V(()=>{const{container:L}=e;return L?L():s.value}),me=V(()=>{const{content:L}=e;return L?L():l.value}),St=(L,ee)=>{if(!e.scrollable)return;if(typeof L=="number"){k(L,ee??0,0,!1,"auto");return}const{left:ce,top:Re,index:Oe,elSize:Ze,position:yt,behavior:lt,el:Wt,debounce:xn=!0}=L;(ce!==void 0||Re!==void 0)&&k(ce??0,Re??0,0,!1,lt),Wt!==void 0?k(0,Wt.offsetTop,Wt.offsetHeight,xn,lt):Oe!==void 0&&Ze!==void 0?k(0,Oe*Ze,Ze,xn,lt):yt==="bottom"?k(0,Number.MAX_SAFE_INTEGER,0,!1,lt):yt==="top"&&k(0,0,0,!1,lt)},$e=Uy(()=>{e.container||St({top:x.value,left:h.value})}),tt=()=>{$e.isDeactivated||N()},Ot=L=>{if($e.isDeactivated)return;const{onResize:ee}=e;ee&&ee(L),N()},$=(L,ee)=>{if(!e.scrollable)return;const{value:ce}=be;ce&&(typeof L=="object"?ce.scrollBy(L):ce.scrollBy(L,ee||0))};function k(L,ee,ce,Re,Oe){const{value:Ze}=be;if(Ze){if(Re){const{scrollTop:yt,offsetHeight:lt}=Ze;if(ee>yt){ee+ce<=yt+lt||Ze.scrollTo({left:L,top:ee+ce-lt,behavior:Oe});return}}Ze.scrollTo({left:L,top:ee,behavior:Oe})}}function z(){_(),O(),N()}function J(){K()}function K(){q(),w()}function q(){v!==void 0&&window.clearTimeout(v),v=window.setTimeout(()=>{P.value=!1},e.duration)}function w(){S!==void 0&&window.clearTimeout(S),S=window.setTimeout(()=>{y.value=!1},e.duration)}function _(){S!==void 0&&window.clearTimeout(S),y.value=!0}function O(){v!==void 0&&window.clearTimeout(v),P.value=!0}function M(L){const{onScroll:ee}=e;ee&&ee(L),ne()}function ne(){const{value:L}=be;L&&(x.value=L.scrollTop,h.value=L.scrollLeft*(o!=null&&o.value?-1:1))}function Y(){const{value:L}=me;L&&(u.value=L.offsetHeight,d.value=L.offsetWidth);const{value:ee}=be;ee&&(f.value=ee.offsetHeight,m.value=ee.offsetWidth);const{value:ce}=c,{value:Re}=a;ce&&(g.value=ce.offsetWidth),Re&&(p.value=Re.offsetHeight)}function I(){const{value:L}=be;L&&(x.value=L.scrollTop,h.value=L.scrollLeft*(o!=null&&o.value?-1:1),f.value=L.offsetHeight,m.value=L.offsetWidth,u.value=L.scrollHeight,d.value=L.scrollWidth);const{value:ee}=c,{value:ce}=a;ee&&(g.value=ee.offsetWidth),ce&&(p.value=ce.offsetHeight)}function N(){e.scrollable&&(e.useUnifiedContainer?I():(Y(),ne()))}function ie(L){var ee;return!(!((ee=i.value)===null||ee===void 0)&&ee.contains(xo(L)))}function de(L){L.preventDefault(),L.stopPropagation(),C=!0,De("mousemove",window,_e,!0),De("mouseup",window,Xe,!0),A=h.value,F=o!=null&&o.value?window.innerWidth-L.clientX:L.clientX}function _e(L){if(!C)return;S!==void 0&&window.clearTimeout(S),v!==void 0&&window.clearTimeout(v);const{value:ee}=m,{value:ce}=d,{value:Re}=te;if(ee===null||ce===null)return;const Ze=(o!=null&&o.value?window.innerWidth-L.clientX-F:L.clientX-F)*(ce-ee)/(ee-Re),yt=ce-ee;let lt=A+Ze;lt=Math.min(yt,lt),lt=Math.max(lt,0);const{value:Wt}=be;if(Wt){Wt.scrollLeft=lt*(o!=null&&o.value?-1:1);const{internalOnUpdateScrollLeft:xn}=e;xn&&xn(lt)}}function Xe(L){L.preventDefault(),L.stopPropagation(),Ne("mousemove",window,_e,!0),Ne("mouseup",window,Xe,!0),C=!1,N(),ie(L)&&K()}function He(L){L.preventDefault(),L.stopPropagation(),b=!0,De("mousemove",window,nt,!0),De("mouseup",window,vt,!0),E=x.value,B=L.clientY}function nt(L){if(!b)return;S!==void 0&&window.clearTimeout(S),v!==void 0&&window.clearTimeout(v);const{value:ee}=f,{value:ce}=u,{value:Re}=Q;if(ee===null||ce===null)return;const Ze=(L.clientY-B)*(ce-ee)/(ee-Re),yt=ce-ee;let lt=E+Ze;lt=Math.min(yt,lt),lt=Math.max(lt,0);const{value:Wt}=be;Wt&&(Wt.scrollTop=lt)}function vt(L){L.preventDefault(),L.stopPropagation(),Ne("mousemove",window,nt,!0),Ne("mouseup",window,vt,!0),b=!1,N(),ie(L)&&K()}Wr(()=>{const{value:L}=Ue,{value:ee}=Te,{value:ce}=t,{value:Re}=c,{value:Oe}=a;Re&&(L?Re.classList.remove(`${ce}-scrollbar-rail--disabled`):Re.classList.add(`${ce}-scrollbar-rail--disabled`)),Oe&&(ee?Oe.classList.remove(`${ce}-scrollbar-rail--disabled`):Oe.classList.add(`${ce}-scrollbar-rail--disabled`))}),mt(()=>{e.container||N()}),pt(()=>{S!==void 0&&window.clearTimeout(S),v!==void 0&&window.clearTimeout(v),Ne("mousemove",window,nt,!0),Ne("mouseup",window,vt,!0)});const Nt=V(()=>{const{common:{cubicBezierEaseInOut:L},self:{color:ee,colorHover:ce,height:Re,width:Oe,borderRadius:Ze,railInsetHorizontalTop:yt,railInsetHorizontalBottom:lt,railInsetVerticalRight:Wt,railInsetVerticalLeft:xn,railColor:Zs}}=j.value,{top:yi,right:qn,bottom:G,left:se}=lr(yt),{top:fe,right:ke,bottom:Le,left:Pe}=lr(lt),{top:Je,right:ut,bottom:Ut,left:qr}=lr(o!=null&&o.value?_d(Wt):Wt),{top:mv,right:bv,bottom:yv,left:xv}=lr(o!=null&&o.value?_d(xn):xn);return{"--n-scrollbar-bezier":L,"--n-scrollbar-color":ee,"--n-scrollbar-color-hover":ce,"--n-scrollbar-border-radius":Ze,"--n-scrollbar-width":Oe,"--n-scrollbar-height":Re,"--n-scrollbar-rail-top-horizontal-top":yi,"--n-scrollbar-rail-right-horizontal-top":qn,"--n-scrollbar-rail-bottom-horizontal-top":G,"--n-scrollbar-rail-left-horizontal-top":se,"--n-scrollbar-rail-top-horizontal-bottom":fe,"--n-scrollbar-rail-right-horizontal-bottom":ke,"--n-scrollbar-rail-bottom-horizontal-bottom":Le,"--n-scrollbar-rail-left-horizontal-bottom":Pe,"--n-scrollbar-rail-top-vertical-right":Je,"--n-scrollbar-rail-right-vertical-right":ut,"--n-scrollbar-rail-bottom-vertical-right":Ut,"--n-scrollbar-rail-left-vertical-right":qr,"--n-scrollbar-rail-top-vertical-left":mv,"--n-scrollbar-rail-right-vertical-left":bv,"--n-scrollbar-rail-bottom-vertical-left":yv,"--n-scrollbar-rail-left-vertical-left":xv,"--n-scrollbar-rail-color":Zs}}),Et=n?dn("scrollbar",void 0,Nt,e):void 0;return Object.assign(Object.assign({},{scrollTo:St,scrollBy:$,sync:N,syncUnifiedContainer:I,handleMouseEnterWrapper:z,handleMouseLeaveWrapper:J}),{mergedClsPrefix:t,rtlEnabled:o,containerScrollTop:x,wrapperRef:i,containerRef:s,contentRef:l,yRailRef:a,xRailRef:c,needYBar:Te,needXBar:Ue,yBarSizePx:H,xBarSizePx:W,yBarTopPx:ue,xBarLeftPx:we,isShowXBar:st,isShowYBar:gt,isIos:R,handleScroll:M,handleContentResize:tt,handleContainerResize:Ot,handleYScrollMouseDown:He,handleXScrollMouseDown:de,cssVars:n?void 0:Nt,themeClass:Et==null?void 0:Et.themeClass,onRender:Et==null?void 0:Et.onRender})},render(){var e;const{$slots:t,mergedClsPrefix:n,triggerDisplayManually:r,rtlEnabled:o,internalHoistYRail:i,yPlacement:s,xPlacement:l,xScrollable:a}=this;if(!this.scrollable)return(e=t.default)===null||e===void 0?void 0:e.call(t);const c=this.trigger==="none",u=(m,p)=>T("div",{ref:"yRailRef",class:[`${n}-scrollbar-rail`,`${n}-scrollbar-rail--vertical`,`${n}-scrollbar-rail--vertical--${s}`,m],"data-scrollbar-rail":!0,style:[p||"",this.verticalRailStyle],"aria-hidden":!0},T(c?Ed:mn,c?null:{name:"fade-in-transition"},{default:()=>this.needYBar&&this.isShowYBar&&!this.isIos?T("div",{class:`${n}-scrollbar-rail__scrollbar`,style:{height:this.yBarSizePx,top:this.yBarTopPx},onMousedown:this.handleYScrollMouseDown}):null})),d=()=>{var m,p;return(m=this.onRender)===null||m===void 0||m.call(this),T("div",Ur(this.$attrs,{role:"none",ref:"wrapperRef",class:[`${n}-scrollbar`,this.themeClass,o&&`${n}-scrollbar--rtl`],style:this.cssVars,onMouseenter:r?void 0:this.handleMouseEnterWrapper,onMouseleave:r?void 0:this.handleMouseLeaveWrapper}),[this.container?(p=t.default)===null||p===void 0?void 0:p.call(t):T("div",{role:"none",ref:"containerRef",class:[`${n}-scrollbar-container`,this.containerClass],style:this.containerStyle,onScroll:this.handleScroll,onWheel:this.onWheel},T(ds,{onResize:this.handleContentResize},{default:()=>T("div",{ref:"contentRef",role:"none",style:[{width:this.xScrollable?"fit-content":null},this.contentStyle],class:[`${n}-scrollbar-content`,this.contentClass]},t)})),i?null:u(void 0,void 0),a&&T("div",{ref:"xRailRef",class:[`${n}-scrollbar-rail`,`${n}-scrollbar-rail--horizontal`,`${n}-scrollbar-rail--horizontal--${l}`],style:this.horizontalRailStyle,"data-scrollbar-rail":!0,"aria-hidden":!0},T(c?Ed:mn,c?null:{name:"fade-in-transition"},{default:()=>this.needXBar&&this.isShowXBar&&!this.isIos?T("div",{class:`${n}-scrollbar-rail__scrollbar`,style:{width:this.xBarSizePx,right:o?this.xBarLeftPx:void 0,left:o?void 0:this.xBarLeftPx},onMousedown:this.handleXScrollMouseDown}):null}))])},f=this.container?d():T(ds,{onResize:this.handleContainerResize},{default:d});return i?T(We,null,f,u(this.themeClass,this.cssVars)):f}}),Hg=bc;function lf(e){return Array.isArray(e)?e:[e]}const va={STOP:"STOP"};function jg(e,t){const n=t(e);e.children!==void 0&&n!==va.STOP&&e.children.forEach(r=>jg(r,t))}function _2(e,t={}){const{preserveGroup:n=!1}=t,r=[],o=n?s=>{s.isLeaf||(r.push(s.key),i(s.children))}:s=>{s.isLeaf||(s.isGroup||r.push(s.key),i(s.children))};function i(s){s.forEach(o)}return i(e),r}function C2(e,t){const{isLeaf:n}=e;return n!==void 0?n:!t(e)}function S2(e){return e.children}function E2(e){return e.key}function T2(){return!1}function $2(e,t){const{isLeaf:n}=e;return!(n===!1&&!Array.isArray(t(e)))}function P2(e){return e.disabled===!0}function O2(e,t){return e.isLeaf===!1&&!Array.isArray(t(e))}function El(e){var t;return e==null?[]:Array.isArray(e)?e:(t=e.checkedKeys)!==null&&t!==void 0?t:[]}function Tl(e){var t;return e==null||Array.isArray(e)?[]:(t=e.indeterminateKeys)!==null&&t!==void 0?t:[]}function k2(e,t){const n=new Set(e);return t.forEach(r=>{n.has(r)||n.add(r)}),Array.from(n)}function A2(e,t){const n=new Set(e);return t.forEach(r=>{n.has(r)&&n.delete(r)}),Array.from(n)}function I2(e){return(e==null?void 0:e.type)==="group"}class R2 extends Error{constructor(){super(),this.message="SubtreeNotLoadedError: checking a subtree whose required nodes are not fully loaded."}}function L2(e,t,n,r){return ys(t.concat(e),n,r,!1)}function M2(e,t){const n=new Set;return e.forEach(r=>{const o=t.treeNodeMap.get(r);if(o!==void 0){let i=o.parent;for(;i!==null&&!(i.disabled||n.has(i.key));)n.add(i.key),i=i.parent}}),n}function F2(e,t,n,r){const o=ys(t,n,r,!1),i=ys(e,n,r,!0),s=M2(e,n),l=[];return o.forEach(a=>{(i.has(a)||s.has(a))&&l.push(a)}),l.forEach(a=>o.delete(a)),o}function $l(e,t){const{checkedKeys:n,keysToCheck:r,keysToUncheck:o,indeterminateKeys:i,cascade:s,leafOnly:l,checkStrategy:a,allowNotLoaded:c}=e;if(!s)return r!==void 0?{checkedKeys:k2(n,r),indeterminateKeys:Array.from(i)}:o!==void 0?{checkedKeys:A2(n,o),indeterminateKeys:Array.from(i)}:{checkedKeys:Array.from(n),indeterminateKeys:Array.from(i)};const{levelTreeNodeMap:u}=t;let d;o!==void 0?d=F2(o,n,t,c):r!==void 0?d=L2(r,n,t,c):d=ys(n,t,c,!1);const f=a==="parent",m=a==="child"||l,p=d,g=new Set,x=Math.max.apply(null,Array.from(u.keys()));for(let h=x;h>=0;h-=1){const y=h===0,P=u.get(h);for(const b of P){if(b.isLeaf)continue;const{key:C,shallowLoaded:S}=b;if(m&&S&&b.children.forEach(F=>{!F.disabled&&!F.isLeaf&&F.shallowLoaded&&p.has(F.key)&&p.delete(F.key)}),b.disabled||!S)continue;let v=!0,E=!1,A=!0;for(const F of b.children){const B=F.key;if(!F.disabled){if(A&&(A=!1),p.has(B))E=!0;else if(g.has(B)){E=!0,v=!1;break}else if(v=!1,E)break}}v&&!A?(f&&b.children.forEach(F=>{!F.disabled&&p.has(F.key)&&p.delete(F.key)}),p.add(C)):E&&g.add(C),y&&m&&p.has(C)&&p.delete(C)}}return{checkedKeys:Array.from(p),indeterminateKeys:Array.from(g)}}function ys(e,t,n,r){const{treeNodeMap:o,getChildren:i}=t,s=new Set,l=new Set(e);return e.forEach(a=>{const c=o.get(a);c!==void 0&&jg(c,u=>{if(u.disabled)return va.STOP;const{key:d}=u;if(!s.has(d)&&(s.add(d),l.add(d),O2(u.rawNode,i))){if(r)return va.STOP;if(!n)throw new R2}})}),l}function N2(e,{includeGroup:t=!1,includeSelf:n=!0},r){var o;const i=r.treeNodeMap;let s=e==null?null:(o=i.get(e))!==null&&o!==void 0?o:null;const l={keyPath:[],treeNodePath:[],treeNode:s};if(s!=null&&s.ignored)return l.treeNode=null,l;for(;s;)!s.ignored&&(t||!s.isGroup)&&l.treeNodePath.push(s),s=s.parent;return l.treeNodePath.reverse(),n||l.treeNodePath.pop(),l.keyPath=l.treeNodePath.map(a=>a.key),l}function D2(e){if(e.length===0)return null;const t=e[0];return t.isGroup||t.ignored||t.disabled?t.getNext():t}function z2(e,t){const n=e.siblings,r=n.length,{index:o}=e;return t?n[(o+1)%r]:o===n.length-1?null:n[o+1]}function af(e,t,{loop:n=!1,includeDisabled:r=!1}={}){const o=t==="prev"?B2:z2,i={reverse:t==="prev"};let s=!1,l=null;function a(c){if(c!==null){if(c===e){if(!s)s=!0;else if(!e.disabled&&!e.isGroup){l=e;return}}else if((!c.disabled||r)&&!c.ignored&&!c.isGroup){l=c;return}if(c.isGroup){const u=yc(c,i);u!==null?l=u:a(o(c,n))}else{const u=o(c,!1);if(u!==null)a(u);else{const d=H2(c);d!=null&&d.isGroup?a(o(d,n)):n&&a(o(c,!0))}}}}return a(e),l}function B2(e,t){const n=e.siblings,r=n.length,{index:o}=e;return t?n[(o-1+r)%r]:o===0?null:n[o-1]}function H2(e){return e.parent}function yc(e,t={}){const{reverse:n=!1}=t,{children:r}=e;if(r){const{length:o}=r,i=n?o-1:0,s=n?-1:o,l=n?-1:1;for(let a=i;a!==s;a+=l){const c=r[a];if(!c.disabled&&!c.ignored)if(c.isGroup){const u=yc(c,t);if(u!==null)return u}else return c}}return null}const j2={getChild(){return this.ignored?null:yc(this)},getParent(){const{parent:e}=this;return e!=null&&e.isGroup?e.getParent():e},getNext(e={}){return af(this,"next",e)},getPrev(e={}){return af(this,"prev",e)}};function W2(e,t){const n=t?new Set(t):void 0,r=[];function o(i){i.forEach(s=>{r.push(s),!(s.isLeaf||!s.children||s.ignored)&&(s.isGroup||n===void 0||n.has(s.key))&&o(s.children)})}return o(e),r}function U2(e,t){const n=e.key;for(;t;){if(t.key===n)return!0;t=t.parent}return!1}function Wg(e,t,n,r,o,i=null,s=0){const l=[];return e.forEach((a,c)=>{var u;const d=Object.create(r);if(d.rawNode=a,d.siblings=l,d.level=s,d.index=c,d.isFirstChild=c===0,d.isLastChild=c+1===e.length,d.parent=i,!d.ignored){const f=o(a);Array.isArray(f)&&(d.children=Wg(f,t,n,r,o,d,s+1))}l.push(d),t.set(d.key,d),n.has(s)||n.set(s,[]),(u=n.get(s))===null||u===void 0||u.push(d)}),l}function V2(e,t={}){var n;const r=new Map,o=new Map,{getDisabled:i=P2,getIgnored:s=T2,getIsGroup:l=I2,getKey:a=E2}=t,c=(n=t.getChildren)!==null&&n!==void 0?n:S2,u=t.ignoreEmptyChildren?b=>{const C=c(b);return Array.isArray(C)?C.length?C:null:C}:c,d=Object.assign({get key(){return a(this.rawNode)},get disabled(){return i(this.rawNode)},get isGroup(){return l(this.rawNode)},get isLeaf(){return C2(this.rawNode,u)},get shallowLoaded(){return $2(this.rawNode,u)},get ignored(){return s(this.rawNode)},contains(b){return U2(this,b)}},j2),f=Wg(e,r,o,d,u);function m(b){if(b==null)return null;const C=r.get(b);return C&&!C.isGroup&&!C.ignored?C:null}function p(b){if(b==null)return null;const C=r.get(b);return C&&!C.ignored?C:null}function g(b,C){const S=p(b);return S?S.getPrev(C):null}function x(b,C){const S=p(b);return S?S.getNext(C):null}function h(b){const C=p(b);return C?C.getParent():null}function y(b){const C=p(b);return C?C.getChild():null}const P={treeNodes:f,treeNodeMap:r,levelTreeNodeMap:o,maxLevel:Math.max(...o.keys()),getChildren:u,getFlattenedNodes(b){return W2(f,b)},getNode:m,getPrev:g,getNext:x,getParent:h,getChild:y,getFirstAvailableNode(){return D2(f)},getPath(b,C={}){return N2(b,C,P)},getCheckedKeys(b,C={}){const{cascade:S=!0,leafOnly:v=!1,checkStrategy:E="all",allowNotLoaded:A=!1}=C;return $l({checkedKeys:El(b),indeterminateKeys:Tl(b),cascade:S,leafOnly:v,checkStrategy:E,allowNotLoaded:A},P)},check(b,C,S={}){const{cascade:v=!0,leafOnly:E=!1,checkStrategy:A="all",allowNotLoaded:F=!1}=S;return $l({checkedKeys:El(C),indeterminateKeys:Tl(C),keysToCheck:b==null?[]:lf(b),cascade:v,leafOnly:E,checkStrategy:A,allowNotLoaded:F},P)},uncheck(b,C,S={}){const{cascade:v=!0,leafOnly:E=!1,checkStrategy:A="all",allowNotLoaded:F=!1}=S;return $l({checkedKeys:El(C),indeterminateKeys:Tl(C),keysToUncheck:b==null?[]:lf(b),cascade:v,leafOnly:E,checkStrategy:A,allowNotLoaded:F},P)},getNonLeafKeys(b={}){return _2(f,b)}};return P}const{cubicBezierEaseIn:cf,cubicBezierEaseOut:uf}=Xr;function Ug({transformOrigin:e="inherit",duration:t=".2s",enterScale:n=".9",originalTransform:r="",originalTransition:o=""}={}){return[U("&.fade-in-scale-up-transition-leave-active",{transformOrigin:e,transition:`opacity ${t} ${cf}, transform ${t} ${cf} ${o&&`,${o}`}`}),U("&.fade-in-scale-up-transition-enter-active",{transformOrigin:e,transition:`opacity ${t} ${uf}, transform ${t} ${uf} ${o&&`,${o}`}`}),U("&.fade-in-scale-up-transition-enter-from, &.fade-in-scale-up-transition-leave-to",{opacity:0,transform:`${r} scale(${n})`}),U("&.fade-in-scale-up-transition-leave-from, &.fade-in-scale-up-transition-enter-to",{opacity:1,transform:`${r} scale(1)`})]}const K2={space:"6px",spaceArrow:"10px",arrowOffset:"10px",arrowOffsetVertical:"10px",arrowHeight:"6px",padding:"8px 14px"};function G2(e){const{boxShadow2:t,popoverColor:n,textColor2:r,borderRadius:o,fontSize:i,dividerColor:s}=e;return Object.assign(Object.assign({},K2),{fontSize:i,borderRadius:o,color:n,dividerColor:s,textColor:r,boxShadow:t})}const Vg={name:"Popover",common:fn,self:G2},Pl={top:"bottom",bottom:"top",left:"right",right:"left"},xt="var(--n-arrow-height) * 1.414",Y2=U([oe("popover",`
 transition:
 box-shadow .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier);
 position: relative;
 font-size: var(--n-font-size);
 color: var(--n-text-color);
 box-shadow: var(--n-box-shadow);
 word-break: break-word;
 `,[U(">",[oe("scrollbar",`
 height: inherit;
 max-height: inherit;
 `)]),ur("raw",`
 background-color: var(--n-color);
 border-radius: var(--n-border-radius);
 `,[ur("scrollable",[ur("show-header-or-footer","padding: var(--n-padding);")])]),Z("header",`
 padding: var(--n-padding);
 border-bottom: 1px solid var(--n-divider-color);
 transition: border-color .3s var(--n-bezier);
 `),Z("footer",`
 padding: var(--n-padding);
 border-top: 1px solid var(--n-divider-color);
 transition: border-color .3s var(--n-bezier);
 `),X("scrollable, show-header-or-footer",[Z("content",`
 padding: var(--n-padding);
 `)])]),oe("popover-shared",`
 transform-origin: inherit;
 `,[oe("popover-arrow-wrapper",`
 position: absolute;
 overflow: hidden;
 pointer-events: none;
 `,[oe("popover-arrow",`
 transition: background-color .3s var(--n-bezier);
 position: absolute;
 display: block;
 width: calc(${xt});
 height: calc(${xt});
 box-shadow: 0 0 8px 0 rgba(0, 0, 0, .12);
 transform: rotate(45deg);
 background-color: var(--n-color);
 pointer-events: all;
 `)]),U("&.popover-transition-enter-from, &.popover-transition-leave-to",`
 opacity: 0;
 transform: scale(.85);
 `),U("&.popover-transition-enter-to, &.popover-transition-leave-from",`
 transform: scale(1);
 opacity: 1;
 `),U("&.popover-transition-enter-active",`
 transition:
 box-shadow .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier),
 opacity .15s var(--n-bezier-ease-out),
 transform .15s var(--n-bezier-ease-out);
 `),U("&.popover-transition-leave-active",`
 transition:
 box-shadow .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier),
 opacity .15s var(--n-bezier-ease-in),
 transform .15s var(--n-bezier-ease-in);
 `)]),on("top-start",`
 top: calc(${xt} / -2);
 left: calc(${Nn("top-start")} - var(--v-offset-left));
 `),on("top",`
 top: calc(${xt} / -2);
 transform: translateX(calc(${xt} / -2)) rotate(45deg);
 left: 50%;
 `),on("top-end",`
 top: calc(${xt} / -2);
 right: calc(${Nn("top-end")} + var(--v-offset-left));
 `),on("bottom-start",`
 bottom: calc(${xt} / -2);
 left: calc(${Nn("bottom-start")} - var(--v-offset-left));
 `),on("bottom",`
 bottom: calc(${xt} / -2);
 transform: translateX(calc(${xt} / -2)) rotate(45deg);
 left: 50%;
 `),on("bottom-end",`
 bottom: calc(${xt} / -2);
 right: calc(${Nn("bottom-end")} + var(--v-offset-left));
 `),on("left-start",`
 left: calc(${xt} / -2);
 top: calc(${Nn("left-start")} - var(--v-offset-top));
 `),on("left",`
 left: calc(${xt} / -2);
 transform: translateY(calc(${xt} / -2)) rotate(45deg);
 top: 50%;
 `),on("left-end",`
 left: calc(${xt} / -2);
 bottom: calc(${Nn("left-end")} + var(--v-offset-top));
 `),on("right-start",`
 right: calc(${xt} / -2);
 top: calc(${Nn("right-start")} - var(--v-offset-top));
 `),on("right",`
 right: calc(${xt} / -2);
 transform: translateY(calc(${xt} / -2)) rotate(45deg);
 top: 50%;
 `),on("right-end",`
 right: calc(${xt} / -2);
 bottom: calc(${Nn("right-end")} + var(--v-offset-top));
 `),...i2({top:["right-start","left-start"],right:["top-end","bottom-end"],bottom:["right-end","left-end"],left:["top-start","bottom-start"]},(e,t)=>{const n=["right","left"].includes(t),r=n?"width":"height";return e.map(o=>{const i=o.split("-")[1]==="end",l=`calc((${`var(--v-target-${r}, 0px)`} - ${xt}) / 2)`,a=Nn(o);return U(`[v-placement="${o}"] >`,[oe("popover-shared",[X("center-arrow",[oe("popover-arrow",`${t}: calc(max(${l}, ${a}) ${i?"+":"-"} var(--v-offset-${n?"left":"top"}));`)])])])})})]);function Nn(e){return["top","bottom"].includes(e.split("-")[0])?"var(--n-arrow-offset)":"var(--n-arrow-offset-vertical)"}function on(e,t){const n=e.split("-")[0],r=["top","bottom"].includes(n)?"height: var(--n-space-arrow);":"width: var(--n-space-arrow);";return U(`[v-placement="${e}"] >`,[oe("popover-shared",`
 margin-${Pl[n]}: var(--n-space);
 `,[X("show-arrow",`
 margin-${Pl[n]}: var(--n-space-arrow);
 `),X("overlap",`
 margin: 0;
 `),uy("popover-arrow-wrapper",`
 right: 0;
 left: 0;
 top: 0;
 bottom: 0;
 ${n}: 100%;
 ${Pl[n]}: auto;
 ${r}
 `,[oe("popover-arrow",t)])])])}const Kg=Object.assign(Object.assign({},it.props),{to:wo.propTo,show:Boolean,trigger:String,showArrow:Boolean,delay:Number,duration:Number,raw:Boolean,arrowPointToCenter:Boolean,arrowClass:String,arrowStyle:[String,Object],arrowWrapperClass:String,arrowWrapperStyle:[String,Object],displayDirective:String,x:Number,y:Number,flip:Boolean,overlap:Boolean,placement:String,width:[Number,String],keepAliveOnHover:Boolean,scrollable:Boolean,contentClass:String,contentStyle:[Object,String],headerClass:String,headerStyle:[Object,String],footerClass:String,footerStyle:[Object,String],internalDeactivateImmediately:Boolean,animated:Boolean,onClickoutside:Function,internalTrapFocus:Boolean,internalOnAfterLeave:Function,minWidth:Number,maxWidth:Number});function Gg({arrowClass:e,arrowStyle:t,arrowWrapperClass:n,arrowWrapperStyle:r,clsPrefix:o}){return T("div",{key:"__popover-arrow__",style:r,class:[`${o}-popover-arrow-wrapper`,n]},T("div",{class:[`${o}-popover-arrow`,e],style:t}))}const X2=ge({name:"PopoverBody",inheritAttrs:!1,props:Kg,setup(e,{slots:t,attrs:n}){const{namespaceRef:r,mergedClsPrefixRef:o,inlineThemeDisabled:i}=Pt(e),s=it("Popover","-popover",Y2,Vg,e,o),l=D(null),a=Se("NPopover"),c=D(null),u=D(e.show),d=D(!1);Wr(()=>{const{show:v}=e;v&&!Ix()&&!e.internalDeactivateImmediately&&(d.value=!0)});const f=V(()=>{const{trigger:v,onClickoutside:E}=e,A=[],{positionManuallyRef:{value:F}}=a;return F||(v==="click"&&!E&&A.push([oa,b,void 0,{capture:!0}]),v==="hover"&&A.push([Gy,P])),E&&A.push([oa,b,void 0,{capture:!0}]),(e.displayDirective==="show"||e.animated&&d.value)&&A.push([Jo,e.show]),A}),m=V(()=>{const{common:{cubicBezierEaseInOut:v,cubicBezierEaseIn:E,cubicBezierEaseOut:A},self:{space:F,spaceArrow:B,padding:R,fontSize:j,textColor:Q,dividerColor:H,color:te,boxShadow:W,borderRadius:re,arrowHeight:ue,arrowOffset:pe,arrowOffsetVertical:we}}=s.value;return{"--n-box-shadow":W,"--n-bezier":v,"--n-bezier-ease-in":E,"--n-bezier-ease-out":A,"--n-font-size":j,"--n-text-color":Q,"--n-color":te,"--n-divider-color":H,"--n-border-radius":re,"--n-arrow-height":ue,"--n-arrow-offset":pe,"--n-arrow-offset-vertical":we,"--n-padding":R,"--n-space":F,"--n-space-arrow":B}}),p=V(()=>{const v=e.width==="trigger"?void 0:Zi(e.width),E=[];v&&E.push({width:v});const{maxWidth:A,minWidth:F}=e;return A&&E.push({maxWidth:Zi(A)}),F&&E.push({maxWidth:Zi(F)}),i||E.push(m.value),E}),g=i?dn("popover",void 0,m,e):void 0;a.setBodyInstance({syncPosition:x}),pt(()=>{a.setBodyInstance(null)}),Ye(Qe(e,"show"),v=>{e.animated||(v?u.value=!0:u.value=!1)});function x(){var v;(v=l.value)===null||v===void 0||v.syncPosition()}function h(v){e.trigger==="hover"&&e.keepAliveOnHover&&e.show&&a.handleMouseEnter(v)}function y(v){e.trigger==="hover"&&e.keepAliveOnHover&&a.handleMouseLeave(v)}function P(v){e.trigger==="hover"&&!C().contains(xo(v))&&a.handleMouseMoveOutside(v)}function b(v){(e.trigger==="click"&&!C().contains(xo(v))||e.onClickoutside)&&a.handleClickOutside(v)}function C(){return a.getTriggerElement()}ze(pi,c),ze(Fs,null),ze(Ns,null);function S(){if(g==null||g.onRender(),!(e.displayDirective==="show"||e.show||e.animated&&d.value))return null;let E;const A=a.internalRenderBodyRef.value,{value:F}=o;if(A)E=A([`${F}-popover-shared`,g==null?void 0:g.themeClass.value,e.overlap&&`${F}-popover-shared--overlap`,e.showArrow&&`${F}-popover-shared--show-arrow`,e.arrowPointToCenter&&`${F}-popover-shared--center-arrow`],c,p.value,h,y);else{const{value:B}=a.extraClassRef,{internalTrapFocus:R}=e,j=!sa(t.header)||!sa(t.footer),Q=()=>{var H,te;const W=j?T(We,null,Yt(t.header,pe=>pe?T("div",{class:[`${F}-popover__header`,e.headerClass],style:e.headerStyle},pe):null),Yt(t.default,pe=>pe?T("div",{class:[`${F}-popover__content`,e.contentClass],style:e.contentStyle},t):null),Yt(t.footer,pe=>pe?T("div",{class:[`${F}-popover__footer`,e.footerClass],style:e.footerStyle},pe):null)):e.scrollable?(H=t.default)===null||H===void 0?void 0:H.call(t):T("div",{class:[`${F}-popover__content`,e.contentClass],style:e.contentStyle},t),re=e.scrollable?T(Hg,{contentClass:j?void 0:`${F}-popover__content ${(te=e.contentClass)!==null&&te!==void 0?te:""}`,contentStyle:j?void 0:e.contentStyle},{default:()=>W}):W,ue=e.showArrow?Gg({arrowClass:e.arrowClass,arrowStyle:e.arrowStyle,arrowWrapperClass:e.arrowWrapperClass,arrowWrapperStyle:e.arrowWrapperStyle,clsPrefix:F}):null;return[re,ue]};E=T("div",Ur({class:[`${F}-popover`,`${F}-popover-shared`,g==null?void 0:g.themeClass.value,B.map(H=>`${F}-${H}`),{[`${F}-popover--scrollable`]:e.scrollable,[`${F}-popover--show-header-or-footer`]:j,[`${F}-popover--raw`]:e.raw,[`${F}-popover-shared--overlap`]:e.overlap,[`${F}-popover-shared--show-arrow`]:e.showArrow,[`${F}-popover-shared--center-arrow`]:e.arrowPointToCenter}],ref:c,style:p.value,onKeydown:a.handleKeydown,onMouseenter:h,onMouseleave:y},n),R?T(Xp,{active:e.show,autoFocus:!0},{default:Q}):Q())}return Un(E,f.value)}return{displayed:d,namespace:r,isMounted:a.isMountedRef,zIndex:a.zIndexRef,followerRef:l,adjustedTo:wo(e),followerEnabled:u,renderContentNode:S}},render(){return T(Dp,{ref:"followerRef",zIndex:this.zIndex,show:this.show,enabled:this.followerEnabled,to:this.adjustedTo,x:this.x,y:this.y,flip:this.flip,placement:this.placement,containerClass:this.namespace,overlap:this.overlap,width:this.width==="trigger"?"target":void 0,teleportDisabled:this.adjustedTo===wo.tdkey},{default:()=>this.animated?T(mn,{name:"popover-transition",appear:this.isMounted,onEnter:()=>{this.followerEnabled=!0},onAfterLeave:()=>{var e;(e=this.internalOnAfterLeave)===null||e===void 0||e.call(this),this.followerEnabled=!1,this.displayed=!1}},{default:this.renderContentNode}):this.renderContentNode()})}}),q2=Object.keys(Kg),Z2={focus:["onFocus","onBlur"],click:["onClick"],hover:["onMouseenter","onMouseleave"],manual:[],nested:["onFocus","onBlur","onMouseenter","onMouseleave","onClick"]};function J2(e,t,n){Z2[t].forEach(r=>{e.props?e.props=Object.assign({},e.props):e.props={};const o=e.props[r],i=n[r];o?e.props[r]=(...s)=>{o(...s),i(...s)}:e.props[r]=i})}const xc={show:{type:Boolean,default:void 0},defaultShow:Boolean,showArrow:{type:Boolean,default:!0},trigger:{type:String,default:"hover"},delay:{type:Number,default:100},duration:{type:Number,default:100},raw:Boolean,placement:{type:String,default:"top"},x:Number,y:Number,arrowPointToCenter:Boolean,disabled:Boolean,getDisabled:Function,displayDirective:{type:String,default:"if"},arrowClass:String,arrowStyle:[String,Object],arrowWrapperClass:String,arrowWrapperStyle:[String,Object],flip:{type:Boolean,default:!0},animated:{type:Boolean,default:!0},width:{type:[Number,String],default:void 0},overlap:Boolean,keepAliveOnHover:{type:Boolean,default:!0},zIndex:Number,to:wo.propTo,scrollable:Boolean,contentClass:String,contentStyle:[Object,String],headerClass:String,headerStyle:[Object,String],footerClass:String,footerStyle:[Object,String],onClickoutside:Function,"onUpdate:show":[Function,Array],onUpdateShow:[Function,Array],internalDeactivateImmediately:Boolean,internalSyncTargetWithParent:Boolean,internalInheritedEventHandlers:{type:Array,default:()=>[]},internalTrapFocus:Boolean,internalExtraClass:{type:Array,default:()=>[]},onShow:[Function,Array],onHide:[Function,Array],arrow:{type:Boolean,default:void 0},minWidth:Number,maxWidth:Number},Q2=Object.assign(Object.assign(Object.assign({},it.props),xc),{internalOnAfterLeave:Function,internalRenderBody:Function}),Yg=ge({name:"Popover",inheritAttrs:!1,props:Q2,slots:Object,__popover__:!0,setup(e){const t=hi(),n=D(null),r=V(()=>e.show),o=D(e.defaultShow),i=Za(r,o),s=an(()=>e.disabled?!1:i.value),l=()=>{if(e.disabled)return!0;const{getDisabled:H}=e;return!!(H!=null&&H())},a=()=>l()?!1:i.value,c=My(e,["arrow","showArrow"]),u=V(()=>e.overlap?!1:c.value);let d=null;const f=D(null),m=D(null),p=an(()=>e.x!==void 0&&e.y!==void 0);function g(H){const{"onUpdate:show":te,onUpdateShow:W,onShow:re,onHide:ue}=e;o.value=H,te&&Xt(te,H),W&&Xt(W,H),H&&re&&Xt(re,!0),H&&ue&&Xt(ue,!1)}function x(){d&&d.syncPosition()}function h(){const{value:H}=f;H&&(window.clearTimeout(H),f.value=null)}function y(){const{value:H}=m;H&&(window.clearTimeout(H),m.value=null)}function P(){const H=l();if(e.trigger==="focus"&&!H){if(a())return;g(!0)}}function b(){const H=l();if(e.trigger==="focus"&&!H){if(!a())return;g(!1)}}function C(){const H=l();if(e.trigger==="hover"&&!H){if(y(),f.value!==null||a())return;const te=()=>{g(!0),f.value=null},{delay:W}=e;W===0?te():f.value=window.setTimeout(te,W)}}function S(){const H=l();if(e.trigger==="hover"&&!H){if(h(),m.value!==null||!a())return;const te=()=>{g(!1),m.value=null},{duration:W}=e;W===0?te():m.value=window.setTimeout(te,W)}}function v(){S()}function E(H){var te;a()&&(e.trigger==="click"&&(h(),y(),g(!1)),(te=e.onClickoutside)===null||te===void 0||te.call(e,H))}function A(){if(e.trigger==="click"&&!l()){h(),y();const H=!a();g(H)}}function F(H){e.internalTrapFocus&&H.key==="Escape"&&(h(),y(),g(!1))}function B(H){o.value=H}function R(){var H;return(H=n.value)===null||H===void 0?void 0:H.targetRef}function j(H){d=H}return ze("NPopover",{getTriggerElement:R,handleKeydown:F,handleMouseEnter:C,handleMouseLeave:S,handleClickOutside:E,handleMouseMoveOutside:v,setBodyInstance:j,positionManuallyRef:p,isMountedRef:t,zIndexRef:Qe(e,"zIndex"),extraClassRef:Qe(e,"internalExtraClass"),internalRenderBodyRef:Qe(e,"internalRenderBody")}),Wr(()=>{i.value&&l()&&g(!1)}),{binderInstRef:n,positionManually:p,mergedShowConsideringDisabledProp:s,uncontrolledShow:o,mergedShowArrow:u,getMergedShow:a,setShow:B,handleClick:A,handleMouseEnter:C,handleMouseLeave:S,handleFocus:P,handleBlur:b,syncPosition:x}},render(){var e;const{positionManually:t,$slots:n}=this;let r,o=!1;if(!t&&(r=Fx(n,"trigger"),r)){r=cn(r),r=r.type===_o?T("span",[r]):r;const i={onClick:this.handleClick,onMouseenter:this.handleMouseEnter,onMouseleave:this.handleMouseLeave,onFocus:this.handleFocus,onBlur:this.handleBlur};if(!((e=r.type)===null||e===void 0)&&e.__popover__)o=!0,r.props||(r.props={internalSyncTargetWithParent:!0,internalInheritedEventHandlers:[]}),r.props.internalSyncTargetWithParent=!0,r.props.internalInheritedEventHandlers?r.props.internalInheritedEventHandlers=[i,...r.props.internalInheritedEventHandlers]:r.props.internalInheritedEventHandlers=[i];else{const{internalInheritedEventHandlers:s}=this,l=[i,...s],a={onBlur:c=>{l.forEach(u=>{u.onBlur(c)})},onFocus:c=>{l.forEach(u=>{u.onFocus(c)})},onClick:c=>{l.forEach(u=>{u.onClick(c)})},onMouseenter:c=>{l.forEach(u=>{u.onMouseenter(c)})},onMouseleave:c=>{l.forEach(u=>{u.onMouseleave(c)})}};J2(r,s?"nested":t?"manual":this.trigger,a)}}return T(Mp,{ref:"binderInstRef",syncTarget:!o,syncTargetWithParent:this.internalSyncTargetWithParent},{default:()=>{this.mergedShowConsideringDisabledProp;const i=this.getMergedShow();return[this.internalTrapFocus&&i?Un(T("div",{style:{position:"fixed",top:0,right:0,bottom:0,left:0}}),[[Qa,{enabled:i,zIndex:this.zIndex}]]):null,t?null:T(Fp,null,{default:()=>r}),T(X2,Vn(this.$props,q2,Object.assign(Object.assign({},this.$attrs),{showArrow:this.mergedShowArrow,show:i})),{default:()=>{var s,l;return(l=(s=this.$slots).default)===null||l===void 0?void 0:l.call(s)},header:()=>{var s,l;return(l=(s=this.$slots).header)===null||l===void 0?void 0:l.call(s)},footer:()=>{var s,l;return(l=(s=this.$slots).footer)===null||l===void 0?void 0:l.call(s)}})]}})}}),{cubicBezierEaseInOut:er}=Xr;function eT({duration:e=".2s",delay:t=".1s"}={}){return[U("&.fade-in-width-expand-transition-leave-from, &.fade-in-width-expand-transition-enter-to",{opacity:1}),U("&.fade-in-width-expand-transition-leave-to, &.fade-in-width-expand-transition-enter-from",`
 opacity: 0!important;
 margin-left: 0!important;
 margin-right: 0!important;
 `),U("&.fade-in-width-expand-transition-leave-active",`
 overflow: hidden;
 transition:
 opacity ${e} ${er},
 max-width ${e} ${er} ${t},
 margin-left ${e} ${er} ${t},
 margin-right ${e} ${er} ${t};
 `),U("&.fade-in-width-expand-transition-enter-active",`
 overflow: hidden;
 transition:
 opacity ${e} ${er} ${t},
 max-width ${e} ${er},
 margin-left ${e} ${er},
 margin-right ${e} ${er};
 `)]}const tT=oe("base-wave",`
 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 border-radius: inherit;
`),nT=ge({name:"BaseWave",props:{clsPrefix:{type:String,required:!0}},setup(e){Us("-base-wave",tT,Qe(e,"clsPrefix"));const t=D(null),n=D(!1);let r=null;return pt(()=>{r!==null&&window.clearTimeout(r)}),{active:n,selfRef:t,play(){r!==null&&(window.clearTimeout(r),n.value=!1,r=null),qt(()=>{var o;(o=t.value)===null||o===void 0||o.offsetHeight,n.value=!0,r=window.setTimeout(()=>{n.value=!1,r=null},1e3)})}}},render(){const{clsPrefix:e}=this;return T("div",{ref:"selfRef","aria-hidden":!0,class:[`${e}-base-wave`,this.active&&`${e}-base-wave--active`]})}}),{cubicBezierEaseInOut:Sn,cubicBezierEaseOut:rT,cubicBezierEaseIn:oT}=Xr;function iT({overflow:e="hidden",duration:t=".3s",originalTransition:n="",leavingDelay:r="0s",foldPadding:o=!1,enterToProps:i=void 0,leaveToProps:s=void 0,reverse:l=!1}={}){const a=l?"leave":"enter",c=l?"enter":"leave";return[U(`&.fade-in-height-expand-transition-${c}-from,
 &.fade-in-height-expand-transition-${a}-to`,Object.assign(Object.assign({},i),{opacity:1})),U(`&.fade-in-height-expand-transition-${c}-to,
 &.fade-in-height-expand-transition-${a}-from`,Object.assign(Object.assign({},s),{opacity:0,marginTop:"0 !important",marginBottom:"0 !important",paddingTop:o?"0 !important":void 0,paddingBottom:o?"0 !important":void 0})),U(`&.fade-in-height-expand-transition-${c}-active`,`
 overflow: ${e};
 transition:
 max-height ${t} ${Sn} ${r},
 opacity ${t} ${rT} ${r},
 margin-top ${t} ${Sn} ${r},
 margin-bottom ${t} ${Sn} ${r},
 padding-top ${t} ${Sn} ${r},
 padding-bottom ${t} ${Sn} ${r}
 ${n?`,${n}`:""}
 `),U(`&.fade-in-height-expand-transition-${a}-active`,`
 overflow: ${e};
 transition:
 max-height ${t} ${Sn},
 opacity ${t} ${oT},
 margin-top ${t} ${Sn},
 margin-bottom ${t} ${Sn},
 padding-top ${t} ${Sn},
 padding-bottom ${t} ${Sn}
 ${n?`,${n}`:""}
 `)]}const sT=Co&&"chrome"in window;Co&&navigator.userAgent.includes("Firefox");const lT=Co&&navigator.userAgent.includes("Safari")&&!sT;function $r(e){return Ka(e,[255,255,255,.16])}function zi(e){return Ka(e,[0,0,0,.12])}const aT="n-button-group",cT={paddingTiny:"0 6px",paddingSmall:"0 10px",paddingMedium:"0 14px",paddingLarge:"0 18px",paddingRoundTiny:"0 10px",paddingRoundSmall:"0 14px",paddingRoundMedium:"0 18px",paddingRoundLarge:"0 22px",iconMarginTiny:"6px",iconMarginSmall:"6px",iconMarginMedium:"6px",iconMarginLarge:"6px",iconSizeTiny:"14px",iconSizeSmall:"18px",iconSizeMedium:"18px",iconSizeLarge:"20px",rippleDuration:".6s"};function uT(e){const{heightTiny:t,heightSmall:n,heightMedium:r,heightLarge:o,borderRadius:i,fontSizeTiny:s,fontSizeSmall:l,fontSizeMedium:a,fontSizeLarge:c,opacityDisabled:u,textColor2:d,textColor3:f,primaryColorHover:m,primaryColorPressed:p,borderColor:g,primaryColor:x,baseColor:h,infoColor:y,infoColorHover:P,infoColorPressed:b,successColor:C,successColorHover:S,successColorPressed:v,warningColor:E,warningColorHover:A,warningColorPressed:F,errorColor:B,errorColorHover:R,errorColorPressed:j,fontWeight:Q,buttonColor2:H,buttonColor2Hover:te,buttonColor2Pressed:W,fontWeightStrong:re}=e;return Object.assign(Object.assign({},cT),{heightTiny:t,heightSmall:n,heightMedium:r,heightLarge:o,borderRadiusTiny:i,borderRadiusSmall:i,borderRadiusMedium:i,borderRadiusLarge:i,fontSizeTiny:s,fontSizeSmall:l,fontSizeMedium:a,fontSizeLarge:c,opacityDisabled:u,colorOpacitySecondary:"0.16",colorOpacitySecondaryHover:"0.22",colorOpacitySecondaryPressed:"0.28",colorSecondary:H,colorSecondaryHover:te,colorSecondaryPressed:W,colorTertiary:H,colorTertiaryHover:te,colorTertiaryPressed:W,colorQuaternary:"#0000",colorQuaternaryHover:te,colorQuaternaryPressed:W,color:"#0000",colorHover:"#0000",colorPressed:"#0000",colorFocus:"#0000",colorDisabled:"#0000",textColor:d,textColorTertiary:f,textColorHover:m,textColorPressed:p,textColorFocus:m,textColorDisabled:d,textColorText:d,textColorTextHover:m,textColorTextPressed:p,textColorTextFocus:m,textColorTextDisabled:d,textColorGhost:d,textColorGhostHover:m,textColorGhostPressed:p,textColorGhostFocus:m,textColorGhostDisabled:d,border:`1px solid ${g}`,borderHover:`1px solid ${m}`,borderPressed:`1px solid ${p}`,borderFocus:`1px solid ${m}`,borderDisabled:`1px solid ${g}`,rippleColor:x,colorPrimary:x,colorHoverPrimary:m,colorPressedPrimary:p,colorFocusPrimary:m,colorDisabledPrimary:x,textColorPrimary:h,textColorHoverPrimary:h,textColorPressedPrimary:h,textColorFocusPrimary:h,textColorDisabledPrimary:h,textColorTextPrimary:x,textColorTextHoverPrimary:m,textColorTextPressedPrimary:p,textColorTextFocusPrimary:m,textColorTextDisabledPrimary:d,textColorGhostPrimary:x,textColorGhostHoverPrimary:m,textColorGhostPressedPrimary:p,textColorGhostFocusPrimary:m,textColorGhostDisabledPrimary:x,borderPrimary:`1px solid ${x}`,borderHoverPrimary:`1px solid ${m}`,borderPressedPrimary:`1px solid ${p}`,borderFocusPrimary:`1px solid ${m}`,borderDisabledPrimary:`1px solid ${x}`,rippleColorPrimary:x,colorInfo:y,colorHoverInfo:P,colorPressedInfo:b,colorFocusInfo:P,colorDisabledInfo:y,textColorInfo:h,textColorHoverInfo:h,textColorPressedInfo:h,textColorFocusInfo:h,textColorDisabledInfo:h,textColorTextInfo:y,textColorTextHoverInfo:P,textColorTextPressedInfo:b,textColorTextFocusInfo:P,textColorTextDisabledInfo:d,textColorGhostInfo:y,textColorGhostHoverInfo:P,textColorGhostPressedInfo:b,textColorGhostFocusInfo:P,textColorGhostDisabledInfo:y,borderInfo:`1px solid ${y}`,borderHoverInfo:`1px solid ${P}`,borderPressedInfo:`1px solid ${b}`,borderFocusInfo:`1px solid ${P}`,borderDisabledInfo:`1px solid ${y}`,rippleColorInfo:y,colorSuccess:C,colorHoverSuccess:S,colorPressedSuccess:v,colorFocusSuccess:S,colorDisabledSuccess:C,textColorSuccess:h,textColorHoverSuccess:h,textColorPressedSuccess:h,textColorFocusSuccess:h,textColorDisabledSuccess:h,textColorTextSuccess:C,textColorTextHoverSuccess:S,textColorTextPressedSuccess:v,textColorTextFocusSuccess:S,textColorTextDisabledSuccess:d,textColorGhostSuccess:C,textColorGhostHoverSuccess:S,textColorGhostPressedSuccess:v,textColorGhostFocusSuccess:S,textColorGhostDisabledSuccess:C,borderSuccess:`1px solid ${C}`,borderHoverSuccess:`1px solid ${S}`,borderPressedSuccess:`1px solid ${v}`,borderFocusSuccess:`1px solid ${S}`,borderDisabledSuccess:`1px solid ${C}`,rippleColorSuccess:C,colorWarning:E,colorHoverWarning:A,colorPressedWarning:F,colorFocusWarning:A,colorDisabledWarning:E,textColorWarning:h,textColorHoverWarning:h,textColorPressedWarning:h,textColorFocusWarning:h,textColorDisabledWarning:h,textColorTextWarning:E,textColorTextHoverWarning:A,textColorTextPressedWarning:F,textColorTextFocusWarning:A,textColorTextDisabledWarning:d,textColorGhostWarning:E,textColorGhostHoverWarning:A,textColorGhostPressedWarning:F,textColorGhostFocusWarning:A,textColorGhostDisabledWarning:E,borderWarning:`1px solid ${E}`,borderHoverWarning:`1px solid ${A}`,borderPressedWarning:`1px solid ${F}`,borderFocusWarning:`1px solid ${A}`,borderDisabledWarning:`1px solid ${E}`,rippleColorWarning:E,colorError:B,colorHoverError:R,colorPressedError:j,colorFocusError:R,colorDisabledError:B,textColorError:h,textColorHoverError:h,textColorPressedError:h,textColorFocusError:h,textColorDisabledError:h,textColorTextError:B,textColorTextHoverError:R,textColorTextPressedError:j,textColorTextFocusError:R,textColorTextDisabledError:d,textColorGhostError:B,textColorGhostHoverError:R,textColorGhostPressedError:j,textColorGhostFocusError:R,textColorGhostDisabledError:B,borderError:`1px solid ${B}`,borderHoverError:`1px solid ${R}`,borderPressedError:`1px solid ${j}`,borderFocusError:`1px solid ${R}`,borderDisabledError:`1px solid ${B}`,rippleColorError:B,waveOpacity:"0.6",fontWeight:Q,fontWeightStrong:re})}const Xg={name:"Button",common:fn,self:uT},dT=U([oe("button",`
 margin: 0;
 font-weight: var(--n-font-weight);
 line-height: 1;
 font-family: inherit;
 padding: var(--n-padding);
 height: var(--n-height);
 font-size: var(--n-font-size);
 border-radius: var(--n-border-radius);
 color: var(--n-text-color);
 background-color: var(--n-color);
 width: var(--n-width);
 white-space: nowrap;
 outline: none;
 position: relative;
 z-index: auto;
 border: none;
 display: inline-flex;
 flex-wrap: nowrap;
 flex-shrink: 0;
 align-items: center;
 justify-content: center;
 user-select: none;
 -webkit-user-select: none;
 text-align: center;
 cursor: pointer;
 text-decoration: none;
 transition:
 color .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 opacity .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 `,[X("color",[Z("border",{borderColor:"var(--n-border-color)"}),X("disabled",[Z("border",{borderColor:"var(--n-border-color-disabled)"})]),ur("disabled",[U("&:focus",[Z("state-border",{borderColor:"var(--n-border-color-focus)"})]),U("&:hover",[Z("state-border",{borderColor:"var(--n-border-color-hover)"})]),U("&:active",[Z("state-border",{borderColor:"var(--n-border-color-pressed)"})]),X("pressed",[Z("state-border",{borderColor:"var(--n-border-color-pressed)"})])])]),X("disabled",{backgroundColor:"var(--n-color-disabled)",color:"var(--n-text-color-disabled)"},[Z("border",{border:"var(--n-border-disabled)"})]),ur("disabled",[U("&:focus",{backgroundColor:"var(--n-color-focus)",color:"var(--n-text-color-focus)"},[Z("state-border",{border:"var(--n-border-focus)"})]),U("&:hover",{backgroundColor:"var(--n-color-hover)",color:"var(--n-text-color-hover)"},[Z("state-border",{border:"var(--n-border-hover)"})]),U("&:active",{backgroundColor:"var(--n-color-pressed)",color:"var(--n-text-color-pressed)"},[Z("state-border",{border:"var(--n-border-pressed)"})]),X("pressed",{backgroundColor:"var(--n-color-pressed)",color:"var(--n-text-color-pressed)"},[Z("state-border",{border:"var(--n-border-pressed)"})])]),X("loading","cursor: wait;"),oe("base-wave",`
 pointer-events: none;
 top: 0;
 right: 0;
 bottom: 0;
 left: 0;
 animation-iteration-count: 1;
 animation-duration: var(--n-ripple-duration);
 animation-timing-function: var(--n-bezier-ease-out), var(--n-bezier-ease-out);
 `,[X("active",{zIndex:1,animationName:"button-wave-spread, button-wave-opacity"})]),Co&&"MozBoxSizing"in document.createElement("div").style?U("&::moz-focus-inner",{border:0}):null,Z("border, state-border",`
 position: absolute;
 left: 0;
 top: 0;
 right: 0;
 bottom: 0;
 border-radius: inherit;
 transition: border-color .3s var(--n-bezier);
 pointer-events: none;
 `),Z("border",{border:"var(--n-border)"}),Z("state-border",{border:"var(--n-border)",borderColor:"#0000",zIndex:1}),Z("icon",`
 margin: var(--n-icon-margin);
 margin-left: 0;
 height: var(--n-icon-size);
 width: var(--n-icon-size);
 max-width: var(--n-icon-size);
 font-size: var(--n-icon-size);
 position: relative;
 flex-shrink: 0;
 `,[oe("icon-slot",`
 height: var(--n-icon-size);
 width: var(--n-icon-size);
 position: absolute;
 left: 0;
 top: 50%;
 transform: translateY(-50%);
 display: flex;
 align-items: center;
 justify-content: center;
 `,[bs({top:"50%",originalTransform:"translateY(-50%)"})]),eT()]),Z("content",`
 display: flex;
 align-items: center;
 flex-wrap: nowrap;
 min-width: 0;
 `,[U("~",[Z("icon",{margin:"var(--n-icon-margin)",marginRight:0})])]),X("block",`
 display: flex;
 width: 100%;
 `),X("dashed",[Z("border, state-border",{borderStyle:"dashed !important"})]),X("disabled",{cursor:"not-allowed",opacity:"var(--n-opacity-disabled)"})]),U("@keyframes button-wave-spread",{from:{boxShadow:"0 0 0.5px 0 var(--n-ripple-color)"},to:{boxShadow:"0 0 0.5px 4.5px var(--n-ripple-color)"}}),U("@keyframes button-wave-opacity",{from:{opacity:"var(--n-wave-opacity)"},to:{opacity:0}})]),fT=Object.assign(Object.assign({},it.props),{color:String,textColor:String,text:Boolean,block:Boolean,loading:Boolean,disabled:Boolean,circle:Boolean,size:String,ghost:Boolean,round:Boolean,secondary:Boolean,tertiary:Boolean,quaternary:Boolean,strong:Boolean,focusable:{type:Boolean,default:!0},keyboard:{type:Boolean,default:!0},tag:{type:String,default:"button"},type:{type:String,default:"default"},dashed:Boolean,renderIcon:Function,iconPlacement:{type:String,default:"left"},attrType:{type:String,default:"button"},bordered:{type:Boolean,default:!0},onClick:[Function,Array],nativeFocusBehavior:{type:Boolean,default:!lT}}),df=ge({name:"Button",props:fT,slots:Object,setup(e){const t=D(null),n=D(null),r=D(!1),o=an(()=>!e.quaternary&&!e.tertiary&&!e.secondary&&!e.text&&(!e.color||e.ghost||e.dashed)&&e.bordered),i=Se(aT,{}),{mergedSizeRef:s}=Dx({},{defaultSize:"medium",mergedSize:b=>{const{size:C}=e;if(C)return C;const{size:S}=i;if(S)return S;const{mergedSize:v}=b||{};return v?v.value:"medium"}}),l=V(()=>e.focusable&&!e.disabled),a=b=>{var C;l.value||b.preventDefault(),!e.nativeFocusBehavior&&(b.preventDefault(),!e.disabled&&l.value&&((C=t.value)===null||C===void 0||C.focus({preventScroll:!0})))},c=b=>{var C;if(!e.disabled&&!e.loading){const{onClick:S}=e;S&&Xt(S,b),e.text||(C=n.value)===null||C===void 0||C.play()}},u=b=>{switch(b.key){case"Enter":if(!e.keyboard)return;r.value=!1}},d=b=>{switch(b.key){case"Enter":if(!e.keyboard||e.loading){b.preventDefault();return}r.value=!0}},f=()=>{r.value=!1},{inlineThemeDisabled:m,mergedClsPrefixRef:p,mergedRtlRef:g}=Pt(e),x=it("Button","-button",dT,Xg,e,p),h=Eo("Button",g,p),y=V(()=>{const b=x.value,{common:{cubicBezierEaseInOut:C,cubicBezierEaseOut:S},self:v}=b,{rippleDuration:E,opacityDisabled:A,fontWeight:F,fontWeightStrong:B}=v,R=s.value,{dashed:j,type:Q,ghost:H,text:te,color:W,round:re,circle:ue,textColor:pe,secondary:we,tertiary:Te,quaternary:Ue,strong:st}=e,gt={"--n-font-weight":st?B:F};let be={"--n-color":"initial","--n-color-hover":"initial","--n-color-pressed":"initial","--n-color-focus":"initial","--n-color-disabled":"initial","--n-ripple-color":"initial","--n-text-color":"initial","--n-text-color-hover":"initial","--n-text-color-pressed":"initial","--n-text-color-focus":"initial","--n-text-color-disabled":"initial"};const me=Q==="tertiary",St=Q==="default",$e=me?"default":Q;if(te){const O=pe||W;be={"--n-color":"#0000","--n-color-hover":"#0000","--n-color-pressed":"#0000","--n-color-focus":"#0000","--n-color-disabled":"#0000","--n-ripple-color":"#0000","--n-text-color":O||v[he("textColorText",$e)],"--n-text-color-hover":O?$r(O):v[he("textColorTextHover",$e)],"--n-text-color-pressed":O?zi(O):v[he("textColorTextPressed",$e)],"--n-text-color-focus":O?$r(O):v[he("textColorTextHover",$e)],"--n-text-color-disabled":O||v[he("textColorTextDisabled",$e)]}}else if(H||j){const O=pe||W;be={"--n-color":"#0000","--n-color-hover":"#0000","--n-color-pressed":"#0000","--n-color-focus":"#0000","--n-color-disabled":"#0000","--n-ripple-color":W||v[he("rippleColor",$e)],"--n-text-color":O||v[he("textColorGhost",$e)],"--n-text-color-hover":O?$r(O):v[he("textColorGhostHover",$e)],"--n-text-color-pressed":O?zi(O):v[he("textColorGhostPressed",$e)],"--n-text-color-focus":O?$r(O):v[he("textColorGhostHover",$e)],"--n-text-color-disabled":O||v[he("textColorGhostDisabled",$e)]}}else if(we){const O=St?v.textColor:me?v.textColorTertiary:v[he("color",$e)],M=W||O,ne=Q!=="default"&&Q!=="tertiary";be={"--n-color":ne?Lo(M,{alpha:Number(v.colorOpacitySecondary)}):v.colorSecondary,"--n-color-hover":ne?Lo(M,{alpha:Number(v.colorOpacitySecondaryHover)}):v.colorSecondaryHover,"--n-color-pressed":ne?Lo(M,{alpha:Number(v.colorOpacitySecondaryPressed)}):v.colorSecondaryPressed,"--n-color-focus":ne?Lo(M,{alpha:Number(v.colorOpacitySecondaryHover)}):v.colorSecondaryHover,"--n-color-disabled":v.colorSecondary,"--n-ripple-color":"#0000","--n-text-color":M,"--n-text-color-hover":M,"--n-text-color-pressed":M,"--n-text-color-focus":M,"--n-text-color-disabled":M}}else if(Te||Ue){const O=St?v.textColor:me?v.textColorTertiary:v[he("color",$e)],M=W||O;Te?(be["--n-color"]=v.colorTertiary,be["--n-color-hover"]=v.colorTertiaryHover,be["--n-color-pressed"]=v.colorTertiaryPressed,be["--n-color-focus"]=v.colorSecondaryHover,be["--n-color-disabled"]=v.colorTertiary):(be["--n-color"]=v.colorQuaternary,be["--n-color-hover"]=v.colorQuaternaryHover,be["--n-color-pressed"]=v.colorQuaternaryPressed,be["--n-color-focus"]=v.colorQuaternaryHover,be["--n-color-disabled"]=v.colorQuaternary),be["--n-ripple-color"]="#0000",be["--n-text-color"]=M,be["--n-text-color-hover"]=M,be["--n-text-color-pressed"]=M,be["--n-text-color-focus"]=M,be["--n-text-color-disabled"]=M}else be={"--n-color":W||v[he("color",$e)],"--n-color-hover":W?$r(W):v[he("colorHover",$e)],"--n-color-pressed":W?zi(W):v[he("colorPressed",$e)],"--n-color-focus":W?$r(W):v[he("colorFocus",$e)],"--n-color-disabled":W||v[he("colorDisabled",$e)],"--n-ripple-color":W||v[he("rippleColor",$e)],"--n-text-color":pe||(W?v.textColorPrimary:me?v.textColorTertiary:v[he("textColor",$e)]),"--n-text-color-hover":pe||(W?v.textColorHoverPrimary:v[he("textColorHover",$e)]),"--n-text-color-pressed":pe||(W?v.textColorPressedPrimary:v[he("textColorPressed",$e)]),"--n-text-color-focus":pe||(W?v.textColorFocusPrimary:v[he("textColorFocus",$e)]),"--n-text-color-disabled":pe||(W?v.textColorDisabledPrimary:v[he("textColorDisabled",$e)])};let tt={"--n-border":"initial","--n-border-hover":"initial","--n-border-pressed":"initial","--n-border-focus":"initial","--n-border-disabled":"initial"};te?tt={"--n-border":"none","--n-border-hover":"none","--n-border-pressed":"none","--n-border-focus":"none","--n-border-disabled":"none"}:tt={"--n-border":v[he("border",$e)],"--n-border-hover":v[he("borderHover",$e)],"--n-border-pressed":v[he("borderPressed",$e)],"--n-border-focus":v[he("borderFocus",$e)],"--n-border-disabled":v[he("borderDisabled",$e)]};const{[he("height",R)]:Ot,[he("fontSize",R)]:$,[he("padding",R)]:k,[he("paddingRound",R)]:z,[he("iconSize",R)]:J,[he("borderRadius",R)]:K,[he("iconMargin",R)]:q,waveOpacity:w}=v,_={"--n-width":ue&&!te?Ot:"initial","--n-height":te?"initial":Ot,"--n-font-size":$,"--n-padding":ue||te?"initial":re?z:k,"--n-icon-size":J,"--n-icon-margin":q,"--n-border-radius":te?"initial":ue||re?Ot:K};return Object.assign(Object.assign(Object.assign(Object.assign({"--n-bezier":C,"--n-bezier-ease-out":S,"--n-ripple-duration":E,"--n-opacity-disabled":A,"--n-wave-opacity":w},gt),be),tt),_)}),P=m?dn("button",V(()=>{let b="";const{dashed:C,type:S,ghost:v,text:E,color:A,round:F,circle:B,textColor:R,secondary:j,tertiary:Q,quaternary:H,strong:te}=e;C&&(b+="a"),v&&(b+="b"),E&&(b+="c"),F&&(b+="d"),B&&(b+="e"),j&&(b+="f"),Q&&(b+="g"),H&&(b+="h"),te&&(b+="i"),A&&(b+=`j${xd(A)}`),R&&(b+=`k${xd(R)}`);const{value:W}=s;return b+=`l${W[0]}`,b+=`m${S[0]}`,b}),y,e):void 0;return{selfElRef:t,waveElRef:n,mergedClsPrefix:p,mergedFocusable:l,mergedSize:s,showBorder:o,enterPressed:r,rtlEnabled:h,handleMousedown:a,handleKeydown:d,handleBlur:f,handleKeyup:u,handleClick:c,customColorCssVars:V(()=>{const{color:b}=e;if(!b)return null;const C=$r(b);return{"--n-border-color":b,"--n-border-color-hover":C,"--n-border-color-pressed":zi(b),"--n-border-color-focus":C,"--n-border-color-disabled":b}}),cssVars:m?void 0:y,themeClass:P==null?void 0:P.themeClass,onRender:P==null?void 0:P.onRender}},render(){const{mergedClsPrefix:e,tag:t,onRender:n}=this;n==null||n();const r=Yt(this.$slots.default,o=>o&&T("span",{class:`${e}-button__content`},o));return T(t,{ref:"selfElRef",class:[this.themeClass,`${e}-button`,`${e}-button--${this.type}-type`,`${e}-button--${this.mergedSize}-type`,this.rtlEnabled&&`${e}-button--rtl`,this.disabled&&`${e}-button--disabled`,this.block&&`${e}-button--block`,this.enterPressed&&`${e}-button--pressed`,!this.text&&this.dashed&&`${e}-button--dashed`,this.color&&`${e}-button--color`,this.secondary&&`${e}-button--secondary`,this.loading&&`${e}-button--loading`,this.ghost&&`${e}-button--ghost`],tabindex:this.mergedFocusable?0:-1,type:this.attrType,style:this.cssVars,disabled:this.disabled,onClick:this.handleClick,onBlur:this.handleBlur,onMousedown:this.handleMousedown,onKeyup:this.handleKeyup,onKeydown:this.handleKeydown},this.iconPlacement==="right"&&r,T(Dg,{width:!0},{default:()=>Yt(this.$slots.icon,o=>(this.loading||this.renderIcon||o)&&T("span",{class:`${e}-button__icon`,style:{margin:sa(this.$slots.default)?"0":""}},T(fc,null,{default:()=>this.loading?T(zg,{clsPrefix:e,key:"loading",class:`${e}-icon-slot`,strokeWidth:20}):T("div",{key:"icon",class:`${e}-icon-slot`,role:"none"},this.renderIcon?this.renderIcon():o)})))}),this.iconPlacement==="left"&&r,this.text?null:T(nT,{ref:"waveElRef",clsPrefix:e}),this.showBorder?T("div",{"aria-hidden":!0,class:`${e}-button__border`,style:this.customColorCssVars}):null,this.showBorder?T("div",{"aria-hidden":!0,class:`${e}-button__state-border`,style:this.customColorCssVars}):null)}}),hT={paddingSmall:"12px 16px 12px",paddingMedium:"19px 24px 20px",paddingLarge:"23px 32px 24px",paddingHuge:"27px 40px 28px",titleFontSizeSmall:"16px",titleFontSizeMedium:"18px",titleFontSizeLarge:"18px",titleFontSizeHuge:"18px",closeIconSize:"18px",closeSize:"22px"};function pT(e){const{primaryColor:t,borderRadius:n,lineHeight:r,fontSize:o,cardColor:i,textColor2:s,textColor1:l,dividerColor:a,fontWeightStrong:c,closeIconColor:u,closeIconColorHover:d,closeIconColorPressed:f,closeColorHover:m,closeColorPressed:p,modalColor:g,boxShadow1:x,popoverColor:h,actionColor:y}=e;return Object.assign(Object.assign({},hT),{lineHeight:r,color:i,colorModal:g,colorPopover:h,colorTarget:t,colorEmbedded:y,colorEmbeddedModal:y,colorEmbeddedPopover:y,textColor:s,titleTextColor:l,borderColor:a,actionColor:y,titleFontWeight:c,closeColorHover:m,closeColorPressed:p,closeBorderRadius:n,closeIconColor:u,closeIconColorHover:d,closeIconColorPressed:f,fontSizeSmall:o,fontSizeMedium:o,fontSizeLarge:o,fontSizeHuge:o,boxShadow:x,borderRadius:n})}const qg={name:"Card",common:fn,self:pT},gT=U([oe("card",`
 font-size: var(--n-font-size);
 line-height: var(--n-line-height);
 display: flex;
 flex-direction: column;
 width: 100%;
 box-sizing: border-box;
 position: relative;
 border-radius: var(--n-border-radius);
 background-color: var(--n-color);
 color: var(--n-text-color);
 word-break: break-word;
 transition: 
 color .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 box-shadow .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 `,[Sp({background:"var(--n-color-modal)"}),X("hoverable",[U("&:hover","box-shadow: var(--n-box-shadow);")]),X("content-segmented",[U(">",[Z("content",{paddingTop:"var(--n-padding-bottom)"})])]),X("content-soft-segmented",[U(">",[Z("content",`
 margin: 0 var(--n-padding-left);
 padding: var(--n-padding-bottom) 0;
 `)])]),X("footer-segmented",[U(">",[Z("footer",{paddingTop:"var(--n-padding-bottom)"})])]),X("footer-soft-segmented",[U(">",[Z("footer",`
 padding: var(--n-padding-bottom) 0;
 margin: 0 var(--n-padding-left);
 `)])]),U(">",[oe("card-header",`
 box-sizing: border-box;
 display: flex;
 align-items: center;
 font-size: var(--n-title-font-size);
 padding:
 var(--n-padding-top)
 var(--n-padding-left)
 var(--n-padding-bottom)
 var(--n-padding-left);
 `,[Z("main",`
 font-weight: var(--n-title-font-weight);
 transition: color .3s var(--n-bezier);
 flex: 1;
 min-width: 0;
 color: var(--n-title-text-color);
 `),Z("extra",`
 display: flex;
 align-items: center;
 font-size: var(--n-font-size);
 font-weight: 400;
 transition: color .3s var(--n-bezier);
 color: var(--n-text-color);
 `),Z("close",`
 margin: 0 0 0 8px;
 transition:
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier);
 `)]),Z("action",`
 box-sizing: border-box;
 transition:
 background-color .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 background-clip: padding-box;
 background-color: var(--n-action-color);
 `),Z("content","flex: 1; min-width: 0;"),Z("content, footer",`
 box-sizing: border-box;
 padding: 0 var(--n-padding-left) var(--n-padding-bottom) var(--n-padding-left);
 font-size: var(--n-font-size);
 `,[U("&:first-child",{paddingTop:"var(--n-padding-bottom)"})]),Z("action",`
 background-color: var(--n-action-color);
 padding: var(--n-padding-bottom) var(--n-padding-left);
 border-bottom-left-radius: var(--n-border-radius);
 border-bottom-right-radius: var(--n-border-radius);
 `)]),oe("card-cover",`
 overflow: hidden;
 width: 100%;
 border-radius: var(--n-border-radius) var(--n-border-radius) 0 0;
 `,[U("img",`
 display: block;
 width: 100%;
 `)]),X("bordered",`
 border: 1px solid var(--n-border-color);
 `,[U("&:target","border-color: var(--n-color-target);")]),X("action-segmented",[U(">",[Z("action",[U("&:not(:first-child)",{borderTop:"1px solid var(--n-border-color)"})])])]),X("content-segmented, content-soft-segmented",[U(">",[Z("content",{transition:"border-color 0.3s var(--n-bezier)"},[U("&:not(:first-child)",{borderTop:"1px solid var(--n-border-color)"})])])]),X("footer-segmented, footer-soft-segmented",[U(">",[Z("footer",{transition:"border-color 0.3s var(--n-bezier)"},[U("&:not(:first-child)",{borderTop:"1px solid var(--n-border-color)"})])])]),X("embedded",`
 background-color: var(--n-color-embedded);
 `)]),Cp(oe("card",`
 background: var(--n-color-modal);
 `,[X("embedded",`
 background-color: var(--n-color-embedded-modal);
 `)])),cy(oe("card",`
 background: var(--n-color-popover);
 `,[X("embedded",`
 background-color: var(--n-color-embedded-popover);
 `)]))]),wc={title:[String,Function],contentClass:String,contentStyle:[Object,String],headerClass:String,headerStyle:[Object,String],headerExtraClass:String,headerExtraStyle:[Object,String],footerClass:String,footerStyle:[Object,String],embedded:Boolean,segmented:{type:[Boolean,Object],default:!1},size:{type:String,default:"medium"},bordered:{type:Boolean,default:!0},closable:Boolean,hoverable:Boolean,role:String,onClose:[Function,Array],tag:{type:String,default:"div"},cover:Function,content:[String,Function],footer:Function,action:Function,headerExtra:Function},vT=Ds(wc),mT=Object.assign(Object.assign({},it.props),wc),bT=ge({name:"Card",props:mT,slots:Object,setup(e){const t=()=>{const{onClose:c}=e;c&&Xt(c)},{inlineThemeDisabled:n,mergedClsPrefixRef:r,mergedRtlRef:o}=Pt(e),i=it("Card","-card",gT,qg,e,r),s=Eo("Card",o,r),l=V(()=>{const{size:c}=e,{self:{color:u,colorModal:d,colorTarget:f,textColor:m,titleTextColor:p,titleFontWeight:g,borderColor:x,actionColor:h,borderRadius:y,lineHeight:P,closeIconColor:b,closeIconColorHover:C,closeIconColorPressed:S,closeColorHover:v,closeColorPressed:E,closeBorderRadius:A,closeIconSize:F,closeSize:B,boxShadow:R,colorPopover:j,colorEmbedded:Q,colorEmbeddedModal:H,colorEmbeddedPopover:te,[he("padding",c)]:W,[he("fontSize",c)]:re,[he("titleFontSize",c)]:ue},common:{cubicBezierEaseInOut:pe}}=i.value,{top:we,left:Te,bottom:Ue}=lr(W);return{"--n-bezier":pe,"--n-border-radius":y,"--n-color":u,"--n-color-modal":d,"--n-color-popover":j,"--n-color-embedded":Q,"--n-color-embedded-modal":H,"--n-color-embedded-popover":te,"--n-color-target":f,"--n-text-color":m,"--n-line-height":P,"--n-action-color":h,"--n-title-text-color":p,"--n-title-font-weight":g,"--n-close-icon-color":b,"--n-close-icon-color-hover":C,"--n-close-icon-color-pressed":S,"--n-close-color-hover":v,"--n-close-color-pressed":E,"--n-border-color":x,"--n-box-shadow":R,"--n-padding-top":we,"--n-padding-bottom":Ue,"--n-padding-left":Te,"--n-font-size":re,"--n-title-font-size":ue,"--n-close-size":B,"--n-close-icon-size":F,"--n-close-border-radius":A}}),a=n?dn("card",V(()=>e.size[0]),l,e):void 0;return{rtlEnabled:s,mergedClsPrefix:r,mergedTheme:i,handleCloseClick:t,cssVars:n?void 0:l,themeClass:a==null?void 0:a.themeClass,onRender:a==null?void 0:a.onRender}},render(){const{segmented:e,bordered:t,hoverable:n,mergedClsPrefix:r,rtlEnabled:o,onRender:i,embedded:s,tag:l,$slots:a}=this;return i==null||i(),T(l,{class:[`${r}-card`,this.themeClass,s&&`${r}-card--embedded`,{[`${r}-card--rtl`]:o,[`${r}-card--content${typeof e!="boolean"&&e.content==="soft"?"-soft":""}-segmented`]:e===!0||e!==!1&&e.content,[`${r}-card--footer${typeof e!="boolean"&&e.footer==="soft"?"-soft":""}-segmented`]:e===!0||e!==!1&&e.footer,[`${r}-card--action-segmented`]:e===!0||e!==!1&&e.action,[`${r}-card--bordered`]:t,[`${r}-card--hoverable`]:n}],style:this.cssVars,role:this.role},Yt(a.cover,c=>{const u=this.cover?hn([this.cover()]):c;return u&&T("div",{class:`${r}-card-cover`,role:"none"},u)}),Yt(a.header,c=>{const{title:u}=this,d=u?hn(typeof u=="function"?[u()]:[u]):c;return d||this.closable?T("div",{class:[`${r}-card-header`,this.headerClass],style:this.headerStyle,role:"heading"},T("div",{class:`${r}-card-header__main`,role:"heading"},d),Yt(a["header-extra"],f=>{const m=this.headerExtra?hn([this.headerExtra()]):f;return m&&T("div",{class:[`${r}-card-header__extra`,this.headerExtraClass],style:this.headerExtraStyle},m)}),this.closable&&T(Ks,{clsPrefix:r,class:`${r}-card-header__close`,onClick:this.handleCloseClick,absolute:!0})):null}),Yt(a.default,c=>{const{content:u}=this,d=u?hn(typeof u=="function"?[u()]:[u]):c;return d&&T("div",{class:[`${r}-card__content`,this.contentClass],style:this.contentStyle,role:"none"},d)}),Yt(a.footer,c=>{const u=this.footer?hn([this.footer()]):c;return u&&T("div",{class:[`${r}-card__footer`,this.footerClass],style:this.footerStyle,role:"none"},u)}),Yt(a.action,c=>{const u=this.action?hn([this.action()]):c;return u&&T("div",{class:`${r}-card__action`,role:"none"},u)}))}});function yT(){return{dotSize:"8px",dotColor:"rgba(255, 255, 255, .3)",dotColorActive:"rgba(255, 255, 255, 1)",dotColorFocus:"rgba(255, 255, 255, .5)",dotLineWidth:"16px",dotLineWidthActive:"24px",arrowColor:"#eee"}}const xT={common:fn,self:yT},Zg="n-carousel-methods";function wT(e){ze(Zg,e)}function _c(e="unknown",t="component"){const n=Se(Zg);return n||Vr(e,`\`${t}\` must be placed inside \`n-carousel\`.`),n}function _T(){return T("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},T("g",{fill:"none"},T("path",{d:"M10.26 3.2a.75.75 0 0 1 .04 1.06L6.773 8l3.527 3.74a.75.75 0 1 1-1.1 1.02l-4-4.25a.75.75 0 0 1 0-1.02l4-4.25a.75.75 0 0 1 1.06-.04z",fill:"currentColor"})))}function CT(){return T("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},T("g",{fill:"none"},T("path",{d:"M5.74 3.2a.75.75 0 0 0-.04 1.06L9.227 8L5.7 11.74a.75.75 0 1 0 1.1 1.02l4-4.25a.75.75 0 0 0 0-1.02l-4-4.25a.75.75 0 0 0-1.06-.04z",fill:"currentColor"})))}const ST=ge({name:"CarouselArrow",setup(e){const{mergedClsPrefixRef:t}=Pt(e),{isVertical:n,isPrevDisabled:r,isNextDisabled:o,prev:i,next:s}=_c();return{mergedClsPrefix:t,isVertical:n,isPrevDisabled:r,isNextDisabled:o,prev:i,next:s}},render(){const{mergedClsPrefix:e}=this;return T("div",{class:`${e}-carousel__arrow-group`},T("div",{class:[`${e}-carousel__arrow`,this.isPrevDisabled()&&`${e}-carousel__arrow--disabled`],role:"button",onClick:this.prev},_T()),T("div",{class:[`${e}-carousel__arrow`,this.isNextDisabled()&&`${e}-carousel__arrow--disabled`],role:"button",onClick:this.next},CT()))}}),ET={total:{type:Number,default:0},currentIndex:{type:Number,default:0},dotType:{type:String,default:"dot"},trigger:{type:String,default:"click"},keyboard:Boolean},TT=ge({name:"CarouselDots",props:ET,setup(e){const{mergedClsPrefixRef:t}=Pt(e),n=D([]),r=_c();function o(c,u){switch(c.key){case"Enter":case" ":c.preventDefault(),r.to(u);return}e.keyboard&&l(c)}function i(c){e.trigger==="hover"&&r.to(c)}function s(c){e.trigger==="click"&&r.to(c)}function l(c){var u;if(c.shiftKey||c.altKey||c.ctrlKey||c.metaKey)return;const d=(u=document.activeElement)===null||u===void 0?void 0:u.nodeName.toLowerCase();if(d==="input"||d==="textarea")return;const{code:f}=c,m=f==="PageUp"||f==="ArrowUp",p=f==="PageDown"||f==="ArrowDown",g=f==="PageUp"||f==="ArrowRight",x=f==="PageDown"||f==="ArrowLeft",h=r.isVertical(),y=h?m:g,P=h?p:x;!y&&!P||(c.preventDefault(),y&&!r.isNextDisabled()?(r.next(),a(r.currentIndexRef.value)):P&&!r.isPrevDisabled()&&(r.prev(),a(r.currentIndexRef.value)))}function a(c){var u;(u=n.value[c])===null||u===void 0||u.focus()}return hh(()=>n.value.length=0),{mergedClsPrefix:t,dotEls:n,handleKeydown:o,handleMouseenter:i,handleClick:s}},render(){const{mergedClsPrefix:e,dotEls:t}=this;return T("div",{class:[`${e}-carousel__dots`,`${e}-carousel__dots--${this.dotType}`],role:"tablist"},Ey(this.total,n=>{const r=n===this.currentIndex;return T("div",{"aria-selected":r,ref:o=>t.push(o),role:"button",tabindex:"0",class:[`${e}-carousel__dot`,r&&`${e}-carousel__dot--active`],key:n,onClick:()=>{this.handleClick(n)},onMouseenter:()=>{this.handleMouseenter(n)},onKeydown:o=>{this.handleKeydown(o,n)}})}))}}),Ji="CarouselItem";function $T(e){var t;return((t=e.type)===null||t===void 0?void 0:t.name)===Ji}const PT=ge({name:Ji,setup(e){const{mergedClsPrefixRef:t}=Pt(e),n=_c(Ud(Ji),`n-${Ud(Ji)}`),r=D(),o=V(()=>{const{value:u}=r;return u?n.getSlideIndex(u):-1}),i=V(()=>n.isPrev(o.value)),s=V(()=>n.isNext(o.value)),l=V(()=>n.isActive(o.value)),a=V(()=>n.getSlideStyle(o.value));mt(()=>{n.addSlide(r.value)}),pt(()=>{n.removeSlide(r.value)});function c(u){const{value:d}=o;d!==void 0&&(n==null||n.onCarouselItemClick(d,u))}return{mergedClsPrefix:t,selfElRef:r,isPrev:i,isNext:s,isActive:l,index:o,style:a,handleClick:c}},render(){var e;const{$slots:t,mergedClsPrefix:n,isPrev:r,isNext:o,isActive:i,index:s,style:l}=this,a=[`${n}-carousel__slide`,{[`${n}-carousel__slide--current`]:i,[`${n}-carousel__slide--prev`]:r,[`${n}-carousel__slide--next`]:o}];return T("div",{ref:"selfElRef",class:a,role:"option",tabindex:"-1","data-index":s,"aria-hidden":!i,style:l,onClickCapture:this.handleClick},(e=t.default)===null||e===void 0?void 0:e.call(t,{isPrev:r,isNext:o,isActive:i,index:s}))}}),OT=oe("carousel",`
 position: relative;
 width: 100%;
 height: 100%;
 touch-action: pan-y;
 overflow: hidden;
`,[Z("slides",`
 display: flex;
 width: 100%;
 height: 100%;
 transition-timing-function: var(--n-bezier);
 transition-property: transform;
 `,[Z("slide",`
 flex-shrink: 0;
 position: relative;
 width: 100%;
 height: 100%;
 outline: none;
 overflow: hidden;
 `,[U("> img",`
 display: block;
 `)])]),Z("dots",`
 position: absolute;
 display: flex;
 flex-wrap: nowrap;
 `,[X("dot",[Z("dot",`
 height: var(--n-dot-size);
 width: var(--n-dot-size);
 background-color: var(--n-dot-color);
 border-radius: 50%;
 cursor: pointer;
 transition:
 box-shadow .3s var(--n-bezier),
 background-color .3s var(--n-bezier);
 outline: none;
 `,[U("&:focus",`
 background-color: var(--n-dot-color-focus);
 `),X("active",`
 background-color: var(--n-dot-color-active);
 `)])]),X("line",[Z("dot",`
 border-radius: 9999px;
 width: var(--n-dot-line-width);
 height: 4px;
 background-color: var(--n-dot-color);
 cursor: pointer;
 transition:
 width .3s var(--n-bezier),
 box-shadow .3s var(--n-bezier),
 background-color .3s var(--n-bezier);
 outline: none;
 `,[U("&:focus",`
 background-color: var(--n-dot-color-focus);
 `),X("active",`
 width: var(--n-dot-line-width-active);
 background-color: var(--n-dot-color-active);
 `)])])]),Z("arrow",`
 transition: background-color .3s var(--n-bezier);
 cursor: pointer;
 height: 28px;
 width: 28px;
 display: flex;
 align-items: center;
 justify-content: center;
 background-color: rgba(255, 255, 255, .2);
 color: var(--n-arrow-color);
 border-radius: 8px;
 user-select: none;
 -webkit-user-select: none;
 font-size: 18px;
 `,[U("svg",`
 height: 1em;
 width: 1em;
 `),U("&:hover",`
 background-color: rgba(255, 255, 255, .3);
 `)]),X("vertical",`
 touch-action: pan-x;
 `,[Z("slides",`
 flex-direction: column;
 `),X("fade",[Z("slide",`
 top: 50%;
 left: unset;
 transform: translateY(-50%);
 `)]),X("card",[Z("slide",`
 top: 50%;
 left: unset;
 transform: translateY(-50%) translateZ(-400px);
 `,[X("current",`
 transform: translateY(-50%) translateZ(0);
 `),X("prev",`
 transform: translateY(-100%) translateZ(-200px);
 `),X("next",`
 transform: translateY(0%) translateZ(-200px);
 `)])])]),X("usercontrol",[Z("slides",[U(">",[U("div",`
 position: absolute;
 top: 50%;
 left: 50%;
 width: 100%;
 height: 100%;
 transform: translate(-50%, -50%);
 `)])])]),X("left",[Z("dots",`
 transform: translateY(-50%);
 top: 50%;
 left: 12px;
 flex-direction: column;
 `,[X("line",[Z("dot",`
 width: 4px;
 height: var(--n-dot-line-width);
 margin: 4px 0;
 transition:
 height .3s var(--n-bezier),
 box-shadow .3s var(--n-bezier),
 background-color .3s var(--n-bezier);
 outline: none;
 `,[X("active",`
 height: var(--n-dot-line-width-active);
 `)])])]),Z("dot",`
 margin: 4px 0;
 `)]),Z("arrow-group",`
 position: absolute;
 display: flex;
 flex-wrap: nowrap;
 `),X("vertical",[Z("arrow",`
 transform: rotate(90deg);
 `)]),X("show-arrow",[X("bottom",[Z("dots",`
 transform: translateX(0);
 bottom: 18px;
 left: 18px;
 `)]),X("top",[Z("dots",`
 transform: translateX(0);
 top: 18px;
 left: 18px;
 `)]),X("left",[Z("dots",`
 transform: translateX(0);
 top: 18px;
 left: 18px;
 `)]),X("right",[Z("dots",`
 transform: translateX(0);
 top: 18px;
 right: 18px;
 `)])]),X("left",[Z("arrow-group",`
 bottom: 12px;
 left: 12px;
 flex-direction: column;
 `,[U("> *:first-child",`
 margin-bottom: 12px;
 `)])]),X("right",[Z("dots",`
 transform: translateY(-50%);
 top: 50%;
 right: 12px;
 flex-direction: column;
 `,[X("line",[Z("dot",`
 width: 4px;
 height: var(--n-dot-line-width);
 margin: 4px 0;
 transition:
 height .3s var(--n-bezier),
 box-shadow .3s var(--n-bezier),
 background-color .3s var(--n-bezier);
 outline: none;
 `,[X("active",`
 height: var(--n-dot-line-width-active);
 `)])])]),Z("dot",`
 margin: 4px 0;
 `),Z("arrow-group",`
 bottom: 12px;
 right: 12px;
 flex-direction: column;
 `,[U("> *:first-child",`
 margin-bottom: 12px;
 `)])]),X("top",[Z("dots",`
 transform: translateX(-50%);
 top: 12px;
 left: 50%;
 `,[X("line",[Z("dot",`
 margin: 0 4px;
 `)])]),Z("dot",`
 margin: 0 4px;
 `),Z("arrow-group",`
 top: 12px;
 right: 12px;
 `,[U("> *:first-child",`
 margin-right: 12px;
 `)])]),X("bottom",[Z("dots",`
 transform: translateX(-50%);
 bottom: 12px;
 left: 50%;
 `,[X("line",[Z("dot",`
 margin: 0 4px;
 `)])]),Z("dot",`
 margin: 0 4px;
 `),Z("arrow-group",`
 bottom: 12px;
 right: 12px;
 `,[U("> *:first-child",`
 margin-right: 12px;
 `)])]),X("fade",[Z("slide",`
 position: absolute;
 opacity: 0;
 transition-property: opacity;
 pointer-events: none;
 `,[X("current",`
 opacity: 1;
 pointer-events: auto;
 `)])]),X("card",[Z("slides",`
 perspective: 1000px;
 `),Z("slide",`
 position: absolute;
 left: 50%;
 opacity: 0;
 transform: translateX(-50%) translateZ(-400px);
 transition-property: opacity, transform;
 `,[X("current",`
 opacity: 1;
 transform: translateX(-50%) translateZ(0);
 z-index: 1;
 `),X("prev",`
 opacity: 0.4;
 transform: translateX(-100%) translateZ(-200px);
 `),X("next",`
 opacity: 0.4;
 transform: translateX(0%) translateZ(-200px);
 `)])])]);function kT(e){const{length:t}=e;return t>1&&(e.push(ff(e[0],0,"append")),e.unshift(ff(e[t-1],t-1,"prepend"))),e}function ff(e,t,n){return cn(e,{key:`carousel-item-duplicate-${t}-${n}`})}function hf(e,t,n){return t===1?0:n?e===0?t-3:e===t-1?0:e-1:e}function Ol(e,t){return t?e+1:e}function AT(e,t,n){return e<0?null:e===0?n?t-1:null:e-1}function IT(e,t,n){return e>t-1?null:e===t-1?n?0:null:e+1}function RT(e,t){return t&&e>3?e-2:e}function pf(e){return window.TouchEvent&&e instanceof window.TouchEvent}function gf(e,t){let{offsetWidth:n,offsetHeight:r}=e;if(t){const o=getComputedStyle(e);n=n-Number.parseFloat(o.getPropertyValue("padding-left"))-Number.parseFloat(o.getPropertyValue("padding-right")),r=r-Number.parseFloat(o.getPropertyValue("padding-top"))-Number.parseFloat(o.getPropertyValue("padding-bottom"))}return{width:n,height:r}}function Bi(e,t,n){return e<t?t:e>n?n:e}function LT(e){if(e===void 0)return 0;if(typeof e=="number")return e;const t=/^((\d+)?\.?\d+?)(ms|s)?$/,n=e.match(t);if(n){const[,r,,o="ms"]=n;return Number(r)*(o==="ms"?1:1e3)}return 0}const MT=["transitionDuration","transitionTimingFunction"],FT=Object.assign(Object.assign({},it.props),{defaultIndex:{type:Number,default:0},currentIndex:Number,showArrow:Boolean,dotType:{type:String,default:"dot"},dotPlacement:{type:String,default:"bottom"},slidesPerView:{type:[Number,String],default:1},spaceBetween:{type:Number,default:0},centeredSlides:Boolean,direction:{type:String,default:"horizontal"},autoplay:Boolean,interval:{type:Number,default:5e3},loop:{type:Boolean,default:!0},effect:{type:String,default:"slide"},showDots:{type:Boolean,default:!0},trigger:{type:String,default:"click"},transitionStyle:{type:Object,default:()=>({transitionDuration:"300ms"})},transitionProps:Object,draggable:Boolean,prevSlideStyle:[Object,String],nextSlideStyle:[Object,String],touchable:{type:Boolean,default:!0},mousewheel:Boolean,keyboard:Boolean,"onUpdate:currentIndex":Function,onUpdateCurrentIndex:Function});let kl=!1;const NT=ge({name:"Carousel",props:FT,slots:Object,setup(e){const{mergedClsPrefixRef:t,inlineThemeDisabled:n}=Pt(e),r=D(null),o=D(null),i=D([]),s={value:[]},l=V(()=>e.direction==="vertical"),a=V(()=>l.value?"height":"width"),c=V(()=>l.value?"bottom":"right"),u=V(()=>e.effect==="slide"),d=V(()=>e.loop&&e.slidesPerView===1&&u.value),f=V(()=>e.effect==="custom"),m=V(()=>!u.value||e.centeredSlides?1:e.slidesPerView),p=V(()=>f.value?1:e.slidesPerView),g=V(()=>m.value==="auto"||e.slidesPerView==="auto"&&e.centeredSlides),x=D({width:0,height:0}),h=D(0),y=V(()=>{const{value:G}=i;if(!G.length)return[];h.value;const{value:se}=g;if(se)return G.map(ut=>gf(ut));const{value:fe}=p,{value:ke}=x,{value:Le}=a;let Pe=ke[Le];if(fe!=="auto"){const{spaceBetween:ut}=e,Ut=Pe-(fe-1)*ut,qr=1/Math.max(1,fe);Pe=Ut*qr}const Je=Object.assign(Object.assign({},ke),{[Le]:Pe});return G.map(()=>Je)}),P=V(()=>{const{value:G}=y;if(!G.length)return[];const{centeredSlides:se,spaceBetween:fe}=e,{value:ke}=a,{[ke]:Le}=x.value;let Pe=0;return G.map(({[ke]:Je})=>{let ut=Pe;return se&&(ut+=(Je-Le)/2),Pe+=Je+fe,ut})}),b=D(!1),C=V(()=>{const{transitionStyle:G}=e;return G?Vn(G,MT):{}}),S=V(()=>f.value?0:LT(C.value.transitionDuration)),v=V(()=>{const{value:G}=i;if(!G.length)return[];const se=!(g.value||p.value===1),fe=Je=>{if(se){const{value:ut}=a;return{[ut]:`${y.value[Je][ut]}px`}}};if(f.value)return G.map((Je,ut)=>fe(ut));const{effect:ke,spaceBetween:Le}=e,{value:Pe}=c;return G.reduce((Je,ut,Ut)=>{const qr=Object.assign(Object.assign({},fe(Ut)),{[`margin-${Pe}`]:`${Le}px`});return Je.push(qr),b.value&&(ke==="fade"||ke==="card")&&Object.assign(qr,C.value),Je},[])}),E=V(()=>{const{value:G}=m,{length:se}=i.value;if(G!=="auto")return Math.max(se-G,0)+1;{const{value:fe}=y,{length:ke}=fe;if(!ke)return se;const{value:Le}=P,{value:Pe}=a,Je=x.value[Pe];let ut=fe[fe.length-1][Pe],Ut=ke;for(;Ut>1&&ut<Je;)Ut--,ut+=Le[Ut]-Le[Ut-1];return Bi(Ut+1,1,ke)}}),A=V(()=>RT(E.value,d.value)),F=Ol(e.defaultIndex,d.value),B=D(hf(F,E.value,d.value)),R=Za(Qe(e,"currentIndex"),B),j=V(()=>Ol(R.value,d.value));function Q(G){var se,fe;G=Bi(G,0,E.value-1);const ke=hf(G,E.value,d.value),{value:Le}=R;ke!==R.value&&(B.value=ke,(se=e["onUpdate:currentIndex"])===null||se===void 0||se.call(e,ke,Le),(fe=e.onUpdateCurrentIndex)===null||fe===void 0||fe.call(e,ke,Le))}function H(G=j.value){return AT(G,E.value,e.loop)}function te(G=j.value){return IT(G,E.value,e.loop)}function W(G){const se=O(G);return se!==null&&H()===se}function re(G){const se=O(G);return se!==null&&te()===se}function ue(G){return j.value===O(G)}function pe(G){return R.value===G}function we(){return H()===null}function Te(){return te()===null}let Ue=0;function st(G){const se=Bi(Ol(G,d.value),0,E.value);(G!==R.value||se!==j.value)&&Q(se)}function gt(){const G=H();G!==null&&(Ue=-1,Q(G))}function be(){const G=te();G!==null&&(Ue=1,Q(G))}let me=!1;function St(){(!me||!d.value)&&gt()}function $e(){(!me||!d.value)&&be()}let tt=0;const Ot=D({});function $(G,se=0){Ot.value=Object.assign({},C.value,{transform:l.value?`translateY(${-G}px)`:`translateX(${-G}px)`,transitionDuration:`${se}ms`})}function k(G=0){u.value?z(j.value,G):tt!==0&&(!me&&G>0&&(me=!0),$(tt=0,G))}function z(G,se){const fe=J(G);fe!==tt&&se>0&&(me=!0),tt=J(j.value),$(fe,se)}function J(G){let se;return G>=E.value-1?se=K():se=P.value[G]||0,se}function K(){if(m.value==="auto"){const{value:G}=a,{[G]:se}=x.value,{value:fe}=P,ke=fe[fe.length-1];let Le;if(ke===void 0)Le=se;else{const{value:Pe}=y;Le=ke+Pe[Pe.length-1][G]}return Le-se}else{const{value:G}=P;return G[E.value-1]||0}}const q={currentIndexRef:R,to:st,prev:St,next:$e,isVertical:()=>l.value,isHorizontal:()=>!l.value,isPrev:W,isNext:re,isActive:ue,isPrevDisabled:we,isNextDisabled:Te,getSlideIndex:O,getSlideStyle:M,addSlide:w,removeSlide:_,onCarouselItemClick:_e};wT(q);function w(G){G&&i.value.push(G)}function _(G){if(!G)return;const se=O(G);se!==-1&&i.value.splice(se,1)}function O(G){return typeof G=="number"?G:G?i.value.indexOf(G):-1}function M(G){const se=O(G);if(se!==-1){const fe=[v.value[se]],ke=q.isPrev(se),Le=q.isNext(se);return ke&&fe.push(e.prevSlideStyle||""),Le&&fe.push(e.nextSlideStyle||""),Tt(fe)}}let ne=0,Y=0,I=0,N=0,ie=!1,de=!1;function _e(G,se){let fe=!me&&!ie&&!de;e.effect==="card"&&fe&&!ue(G)&&(st(G),fe=!1),fe||(se.preventDefault(),se.stopPropagation())}let Xe=null;function He(){Xe&&(clearInterval(Xe),Xe=null)}function nt(){He(),!e.autoplay||A.value<2||(Xe=window.setInterval(be,e.interval))}function vt(G){var se;if(kl||!(!((se=o.value)===null||se===void 0)&&se.contains(xo(G))))return;kl=!0,ie=!0,de=!1,N=Date.now(),He(),G.type!=="touchstart"&&!G.target.isContentEditable&&G.preventDefault();const fe=pf(G)?G.touches[0]:G;l.value?Y=fe.clientY:ne=fe.clientX,e.touchable&&(De("touchmove",document,Nt),De("touchend",document,Et),De("touchcancel",document,Et)),e.draggable&&(De("mousemove",document,Nt),De("mouseup",document,Et))}function Nt(G){const{value:se}=l,{value:fe}=a,ke=pf(G)?G.touches[0]:G,Le=se?ke.clientY-Y:ke.clientX-ne,Pe=x.value[fe];I=Bi(Le,-Pe,Pe),G.cancelable&&G.preventDefault(),u.value&&$(tt-I,0)}function Et(){const{value:G}=j;let se=G;if(!me&&I!==0&&u.value){const fe=tt-I,ke=[...P.value.slice(0,E.value-1),K()];let Le=null;for(let Pe=0;Pe<ke.length;Pe++){const Je=Math.abs(ke[Pe]-fe);if(Le!==null&&Le<Je)break;Le=Je,se=Pe}}if(se===G){const fe=Date.now()-N,{value:ke}=a,Le=x.value[ke];I>Le/2||I/fe>.4?gt():(I<-Le/2||I/fe<-.4)&&be()}se!==null&&se!==G?(de=!0,Q(se),qt(()=>{(!d.value||B.value!==R.value)&&k(S.value)})):k(S.value),bt(),nt()}function bt(){ie&&(kl=!1),ie=!1,ne=0,Y=0,I=0,N=0,Ne("touchmove",document,Nt),Ne("touchend",document,Et),Ne("touchcancel",document,Et),Ne("mousemove",document,Nt),Ne("mouseup",document,Et)}function L(){if(u.value&&me){const{value:G}=j;z(G,0)}else nt();u.value&&(Ot.value.transitionDuration="0ms"),me=!1}function ee(G){if(G.preventDefault(),me)return;let{deltaX:se,deltaY:fe}=G;G.shiftKey&&!se&&(se=fe);const ke=-1,Le=1,Pe=(se||fe)>0?Le:ke;let Je=0,ut=0;l.value?ut=Pe:Je=Pe;const Ut=10;(ut*fe>=Ut||Je*se>=Ut)&&(Pe===Le&&!Te()?be():Pe===ke&&!we()&&gt())}function ce(){x.value=gf(r.value,!0),nt()}function Re(){g.value&&h.value++}function Oe(){e.autoplay&&He()}function Ze(){e.autoplay&&nt()}mt(()=>{Wr(nt),requestAnimationFrame(()=>b.value=!0)}),pt(()=>{bt(),He()}),Ia(()=>{const{value:G}=i,{value:se}=s,fe=new Map,ke=Pe=>fe.has(Pe)?fe.get(Pe):-1;let Le=!1;for(let Pe=0;Pe<G.length;Pe++){const Je=se.findIndex(ut=>ut.el===G[Pe]);Je!==Pe&&(Le=!0),fe.set(G[Pe],Je)}Le&&G.sort((Pe,Je)=>ke(Pe)-ke(Je))}),Ye(j,(G,se)=>{if(G===se){Ue=0;return}if(nt(),u.value){if(d.value){const{value:fe}=E;Ue===-1&&se===1&&G===fe-2?G=0:Ue===1&&se===fe-2&&G===1&&(G=fe-1)}z(G,S.value)}else k();Ue=0},{immediate:!0}),Ye([d,m],()=>void qt(()=>{Q(j.value)})),Ye(P,()=>{u.value&&k()},{deep:!0}),Ye(u,G=>{G?k():(me=!1,$(tt=0))});const yt=V(()=>({onTouchstartPassive:e.touchable?vt:void 0,onMousedown:e.draggable?vt:void 0,onWheel:e.mousewheel?ee:void 0})),lt=V(()=>Object.assign(Object.assign({},Vn(q,["to","prev","next","isPrevDisabled","isNextDisabled"])),{total:A.value,currentIndex:R.value})),Wt=V(()=>({total:A.value,currentIndex:R.value,to:q.to})),xn={getCurrentIndex:()=>R.value,to:st,prev:gt,next:be},Zs=it("Carousel","-carousel",OT,xT,e,t),yi=V(()=>{const{common:{cubicBezierEaseInOut:G},self:{dotSize:se,dotColor:fe,dotColorActive:ke,dotColorFocus:Le,dotLineWidth:Pe,dotLineWidthActive:Je,arrowColor:ut}}=Zs.value;return{"--n-bezier":G,"--n-dot-color":fe,"--n-dot-color-focus":Le,"--n-dot-color-active":ke,"--n-dot-size":se,"--n-dot-line-width":Pe,"--n-dot-line-width-active":Je,"--n-arrow-color":ut}}),qn=n?dn("carousel",void 0,yi,e):void 0;return Object.assign(Object.assign({mergedClsPrefix:t,selfElRef:r,slidesElRef:o,slideVNodes:s,duplicatedable:d,userWantsControl:f,autoSlideSize:g,realIndex:j,slideStyles:v,translateStyle:Ot,slidesControlListeners:yt,handleTransitionEnd:L,handleResize:ce,handleSlideResize:Re,handleMouseenter:Oe,handleMouseleave:Ze,isActive:pe,arrowSlotProps:lt,dotSlotProps:Wt},xn),{cssVars:n?void 0:yi,themeClass:qn==null?void 0:qn.themeClass,onRender:qn==null?void 0:qn.onRender})},render(){var e;const{mergedClsPrefix:t,showArrow:n,userWantsControl:r,slideStyles:o,dotType:i,dotPlacement:s,slidesControlListeners:l,transitionProps:a={},arrowSlotProps:c,dotSlotProps:u,$slots:{default:d,dots:f,arrow:m}}=this,p=d&&si(d())||[];let g=DT(p);return g.length||(g=p.map(x=>T(PT,null,{default:()=>cn(x)}))),this.duplicatedable&&(g=kT(g)),this.slideVNodes.value=g,this.autoSlideSize&&(g=g.map(x=>T(ds,{onResize:this.handleSlideResize},{default:()=>x}))),(e=this.onRender)===null||e===void 0||e.call(this),T("div",Object.assign({ref:"selfElRef",class:[this.themeClass,`${t}-carousel`,this.direction==="vertical"&&`${t}-carousel--vertical`,this.showArrow&&`${t}-carousel--show-arrow`,`${t}-carousel--${s}`,`${t}-carousel--${this.direction}`,`${t}-carousel--${this.effect}`,r&&`${t}-carousel--usercontrol`],style:this.cssVars},l,{onMouseenter:this.handleMouseenter,onMouseleave:this.handleMouseleave}),T(ds,{onResize:this.handleResize},{default:()=>T("div",{ref:"slidesElRef",class:`${t}-carousel__slides`,role:"listbox",style:this.translateStyle,onTransitionend:this.handleTransitionEnd},r?g.map((x,h)=>T("div",{style:o[h],key:h},Un(T(mn,Object.assign({},a),{default:()=>x}),[[Jo,this.isActive(h)]]))):g)}),this.showDots&&u.total>1&&Sd(f,u,()=>[T(TT,{key:i+s,total:u.total,currentIndex:u.currentIndex,dotType:i,trigger:this.trigger,keyboard:this.keyboard})]),n&&Sd(m,c,()=>[T(ST,null)]))}});function DT(e){return e.reduce((t,n)=>($T(n)&&t.push(n),t),[])}const zT={abstract:Boolean,bordered:{type:Boolean,default:void 0},clsPrefix:String,locale:Object,dateLocale:Object,namespace:String,rtl:Array,tag:{type:String,default:"div"},hljs:Object,katex:Object,theme:Object,themeOverrides:Object,componentOptions:Object,icons:Object,breakpoints:Object,preflightStyleDisabled:Boolean,styleMountTarget:Object,inlineThemeDisabled:{type:Boolean,default:void 0},as:{type:String,validator:()=>(hr("config-provider","`as` is deprecated, please use `tag` instead."),!0),default:void 0}},BT=ge({name:"ConfigProvider",alias:["App"],props:zT,setup(e){const t=Se(pr,null),n=V(()=>{const{theme:g}=e;if(g===null)return;const x=t==null?void 0:t.mergedThemeRef.value;return g===void 0?x:x===void 0?g:Object.assign({},x,g)}),r=V(()=>{const{themeOverrides:g}=e;if(g!==null){if(g===void 0)return t==null?void 0:t.mergedThemeOverridesRef.value;{const x=t==null?void 0:t.mergedThemeOverridesRef.value;return x===void 0?g:Fo({},x,g)}}}),o=an(()=>{const{namespace:g}=e;return g===void 0?t==null?void 0:t.mergedNamespaceRef.value:g}),i=an(()=>{const{bordered:g}=e;return g===void 0?t==null?void 0:t.mergedBorderedRef.value:g}),s=V(()=>{const{icons:g}=e;return g===void 0?t==null?void 0:t.mergedIconsRef.value:g}),l=V(()=>{const{componentOptions:g}=e;return g!==void 0?g:t==null?void 0:t.mergedComponentPropsRef.value}),a=V(()=>{const{clsPrefix:g}=e;return g!==void 0?g:t?t.mergedClsPrefixRef.value:la}),c=V(()=>{var g;const{rtl:x}=e;if(x===void 0)return t==null?void 0:t.mergedRtlRef.value;const h={};for(const y of x)h[y.name]=Ll(y),(g=y.peers)===null||g===void 0||g.forEach(P=>{P.name in h||(h[P.name]=Ll(P))});return h}),u=V(()=>e.breakpoints||(t==null?void 0:t.mergedBreakpointsRef.value)),d=e.inlineThemeDisabled||(t==null?void 0:t.inlineThemeDisabled),f=e.preflightStyleDisabled||(t==null?void 0:t.preflightStyleDisabled),m=e.styleMountTarget||(t==null?void 0:t.styleMountTarget),p=V(()=>{const{value:g}=n,{value:x}=r,h=x&&Object.keys(x).length!==0,y=g==null?void 0:g.name;return y?h?`${y}-${ri(JSON.stringify(r.value))}`:y:h?ri(JSON.stringify(r.value)):""});return ze(pr,{mergedThemeHashRef:p,mergedBreakpointsRef:u,mergedRtlRef:c,mergedIconsRef:s,mergedComponentPropsRef:l,mergedBorderedRef:i,mergedNamespaceRef:o,mergedClsPrefixRef:a,mergedLocaleRef:V(()=>{const{locale:g}=e;if(g!==null)return g===void 0?t==null?void 0:t.mergedLocaleRef.value:g}),mergedDateLocaleRef:V(()=>{const{dateLocale:g}=e;if(g!==null)return g===void 0?t==null?void 0:t.mergedDateLocaleRef.value:g}),mergedHljsRef:V(()=>{const{hljs:g}=e;return g===void 0?t==null?void 0:t.mergedHljsRef.value:g}),mergedKatexRef:V(()=>{const{katex:g}=e;return g===void 0?t==null?void 0:t.mergedKatexRef.value:g}),mergedThemeRef:n,mergedThemeOverridesRef:r,inlineThemeDisabled:d||!1,preflightStyleDisabled:f||!1,styleMountTarget:m}),{mergedClsPrefix:a,mergedBordered:i,mergedNamespace:o,mergedTheme:n,mergedThemeOverrides:r}},render(){var e,t,n,r;return this.abstract?(r=(n=this.$slots).default)===null||r===void 0?void 0:r.call(n):T(this.as||this.tag,{class:`${this.mergedClsPrefix||la}-config-provider`},(t=(e=this.$slots).default)===null||t===void 0?void 0:t.call(e))}}),HT={padding:"4px 0",optionIconSizeSmall:"14px",optionIconSizeMedium:"16px",optionIconSizeLarge:"16px",optionIconSizeHuge:"18px",optionSuffixWidthSmall:"14px",optionSuffixWidthMedium:"14px",optionSuffixWidthLarge:"16px",optionSuffixWidthHuge:"16px",optionIconSuffixWidthSmall:"32px",optionIconSuffixWidthMedium:"32px",optionIconSuffixWidthLarge:"36px",optionIconSuffixWidthHuge:"36px",optionPrefixWidthSmall:"14px",optionPrefixWidthMedium:"14px",optionPrefixWidthLarge:"16px",optionPrefixWidthHuge:"16px",optionIconPrefixWidthSmall:"36px",optionIconPrefixWidthMedium:"36px",optionIconPrefixWidthLarge:"40px",optionIconPrefixWidthHuge:"40px"};function jT(e){const{primaryColor:t,textColor2:n,dividerColor:r,hoverColor:o,popoverColor:i,invertedColor:s,borderRadius:l,fontSizeSmall:a,fontSizeMedium:c,fontSizeLarge:u,fontSizeHuge:d,heightSmall:f,heightMedium:m,heightLarge:p,heightHuge:g,textColor3:x,opacityDisabled:h}=e;return Object.assign(Object.assign({},HT),{optionHeightSmall:f,optionHeightMedium:m,optionHeightLarge:p,optionHeightHuge:g,borderRadius:l,fontSizeSmall:a,fontSizeMedium:c,fontSizeLarge:u,fontSizeHuge:d,optionTextColor:n,optionTextColorHover:n,optionTextColorActive:t,optionTextColorChildActive:t,color:i,dividerColor:r,suffixColor:n,prefixColor:n,optionColorHover:o,optionColorActive:Lo(t,{alpha:.1}),groupHeaderTextColor:x,optionTextColorInverted:"#BBB",optionTextColorHoverInverted:"#FFF",optionTextColorActiveInverted:"#FFF",optionTextColorChildActiveInverted:"#FFF",colorInverted:s,dividerColorInverted:"#BBB",suffixColorInverted:"#BBB",prefixColorInverted:"#BBB",optionColorHoverInverted:t,optionColorActiveInverted:t,groupHeaderTextColorInverted:"#AAA",optionOpacityDisabled:h})}const WT={name:"Dropdown",common:fn,peers:{Popover:Vg},self:jT},Cc="n-dropdown-menu",Gs="n-dropdown",vf="n-dropdown-option",Jg=ge({name:"DropdownDivider",props:{clsPrefix:{type:String,required:!0}},render(){return T("div",{class:`${this.clsPrefix}-dropdown-divider`})}}),UT=ge({name:"DropdownGroupHeader",props:{clsPrefix:{type:String,required:!0},tmNode:{type:Object,required:!0}},setup(){const{showIconRef:e,hasSubmenuRef:t}=Se(Cc),{renderLabelRef:n,labelFieldRef:r,nodePropsRef:o,renderOptionRef:i}=Se(Gs);return{labelField:r,showIcon:e,hasSubmenu:t,renderLabel:n,nodeProps:o,renderOption:i}},render(){var e;const{clsPrefix:t,hasSubmenu:n,showIcon:r,nodeProps:o,renderLabel:i,renderOption:s}=this,{rawNode:l}=this.tmNode,a=T("div",Object.assign({class:`${t}-dropdown-option`},o==null?void 0:o(l)),T("div",{class:`${t}-dropdown-option-body ${t}-dropdown-option-body--group`},T("div",{"data-dropdown-option":!0,class:[`${t}-dropdown-option-body__prefix`,r&&`${t}-dropdown-option-body__prefix--show-icon`]},kt(l.icon)),T("div",{class:`${t}-dropdown-option-body__label`,"data-dropdown-option":!0},i?i(l):kt((e=l.title)!==null&&e!==void 0?e:l[this.labelField])),T("div",{class:[`${t}-dropdown-option-body__suffix`,n&&`${t}-dropdown-option-body__suffix--has-submenu`],"data-dropdown-option":!0})));return s?s({node:a,option:l}):a}});function VT(e){const{textColorBase:t,opacity1:n,opacity2:r,opacity3:o,opacity4:i,opacity5:s}=e;return{color:t,opacity1Depth:n,opacity2Depth:r,opacity3Depth:o,opacity4Depth:i,opacity5Depth:s}}const KT={common:fn,self:VT},GT=oe("icon",`
 height: 1em;
 width: 1em;
 line-height: 1em;
 text-align: center;
 display: inline-block;
 position: relative;
 fill: currentColor;
 transform: translateZ(0);
`,[X("color-transition",{transition:"color .3s var(--n-bezier)"}),X("depth",{color:"var(--n-color)"},[U("svg",{opacity:"var(--n-opacity)",transition:"opacity .3s var(--n-bezier)"})]),U("svg",{height:"1em",width:"1em"})]),YT=Object.assign(Object.assign({},it.props),{depth:[String,Number],size:[Number,String],color:String,component:[Object,Function]}),XT=ge({_n_icon__:!0,name:"Icon",inheritAttrs:!1,props:YT,setup(e){const{mergedClsPrefixRef:t,inlineThemeDisabled:n}=Pt(e),r=it("Icon","-icon",GT,KT,e,t),o=V(()=>{const{depth:s}=e,{common:{cubicBezierEaseInOut:l},self:a}=r.value;if(s!==void 0){const{color:c,[`opacity${s}Depth`]:u}=a;return{"--n-bezier":l,"--n-color":c,"--n-opacity":u}}return{"--n-bezier":l,"--n-color":"","--n-opacity":""}}),i=n?dn("icon",V(()=>`${e.depth||"d"}`),o,e):void 0;return{mergedClsPrefix:t,mergedStyle:V(()=>{const{size:s,color:l}=e;return{fontSize:Zi(s),color:l}}),cssVars:n?void 0:o,themeClass:i==null?void 0:i.themeClass,onRender:i==null?void 0:i.onRender}},render(){var e;const{$parent:t,depth:n,mergedClsPrefix:r,component:o,onRender:i,themeClass:s}=this;return!((e=t==null?void 0:t.$options)===null||e===void 0)&&e._n_icon__&&hr("icon","don't wrap `n-icon` inside `n-icon`"),i==null||i(),T("i",Ur(this.$attrs,{role:"img",class:[`${r}-icon`,s,{[`${r}-icon--depth`]:n,[`${r}-icon--color-transition`]:n!==void 0}],style:[this.cssVars,this.mergedStyle]}),o?T(o):this.$slots)}});function ma(e,t){return e.type==="submenu"||e.type===void 0&&e[t]!==void 0}function qT(e){return e.type==="group"}function Qg(e){return e.type==="divider"}function ZT(e){return e.type==="render"}const ev=ge({name:"DropdownOption",props:{clsPrefix:{type:String,required:!0},tmNode:{type:Object,required:!0},parentKey:{type:[String,Number],default:null},placement:{type:String,default:"right-start"},props:Object,scrollable:Boolean},setup(e){const t=Se(Gs),{hoverKeyRef:n,keyboardKeyRef:r,lastToggledSubmenuKeyRef:o,pendingKeyPathRef:i,activeKeyPathRef:s,animatedRef:l,mergedShowRef:a,renderLabelRef:c,renderIconRef:u,labelFieldRef:d,childrenFieldRef:f,renderOptionRef:m,nodePropsRef:p,menuPropsRef:g}=t,x=Se(vf,null),h=Se(Cc),y=Se(pi),P=V(()=>e.tmNode.rawNode),b=V(()=>{const{value:W}=f;return ma(e.tmNode.rawNode,W)}),C=V(()=>{const{disabled:W}=e.tmNode;return W}),S=V(()=>{if(!b.value)return!1;const{key:W,disabled:re}=e.tmNode;if(re)return!1;const{value:ue}=n,{value:pe}=r,{value:we}=o,{value:Te}=i;return ue!==null?Te.includes(W):pe!==null?Te.includes(W)&&Te[Te.length-1]!==W:we!==null?Te.includes(W):!1}),v=V(()=>r.value===null&&!l.value),E=Hy(S,300,v),A=V(()=>!!(x!=null&&x.enteringSubmenuRef.value)),F=D(!1);ze(vf,{enteringSubmenuRef:F});function B(){F.value=!0}function R(){F.value=!1}function j(){const{parentKey:W,tmNode:re}=e;re.disabled||a.value&&(o.value=W,r.value=null,n.value=re.key)}function Q(){const{tmNode:W}=e;W.disabled||a.value&&n.value!==W.key&&j()}function H(W){if(e.tmNode.disabled||!a.value)return;const{relatedTarget:re}=W;re&&!Uu({target:re},"dropdownOption")&&!Uu({target:re},"scrollbarRail")&&(n.value=null)}function te(){const{value:W}=b,{tmNode:re}=e;a.value&&!W&&!re.disabled&&(t.doSelect(re.key,re.rawNode),t.doUpdateShow(!1))}return{labelField:d,renderLabel:c,renderIcon:u,siblingHasIcon:h.showIconRef,siblingHasSubmenu:h.hasSubmenuRef,menuProps:g,popoverBody:y,animated:l,mergedShowSubmenu:V(()=>E.value&&!A.value),rawNode:P,hasSubmenu:b,pending:an(()=>{const{value:W}=i,{key:re}=e.tmNode;return W.includes(re)}),childActive:an(()=>{const{value:W}=s,{key:re}=e.tmNode,ue=W.findIndex(pe=>re===pe);return ue===-1?!1:ue<W.length-1}),active:an(()=>{const{value:W}=s,{key:re}=e.tmNode,ue=W.findIndex(pe=>re===pe);return ue===-1?!1:ue===W.length-1}),mergedDisabled:C,renderOption:m,nodeProps:p,handleClick:te,handleMouseMove:Q,handleMouseEnter:j,handleMouseLeave:H,handleSubmenuBeforeEnter:B,handleSubmenuAfterEnter:R}},render(){var e,t;const{animated:n,rawNode:r,mergedShowSubmenu:o,clsPrefix:i,siblingHasIcon:s,siblingHasSubmenu:l,renderLabel:a,renderIcon:c,renderOption:u,nodeProps:d,props:f,scrollable:m}=this;let p=null;if(o){const y=(e=this.menuProps)===null||e===void 0?void 0:e.call(this,r,r.children);p=T(tv,Object.assign({},y,{clsPrefix:i,scrollable:this.scrollable,tmNodes:this.tmNode.children,parentKey:this.tmNode.key}))}const g={class:[`${i}-dropdown-option-body`,this.pending&&`${i}-dropdown-option-body--pending`,this.active&&`${i}-dropdown-option-body--active`,this.childActive&&`${i}-dropdown-option-body--child-active`,this.mergedDisabled&&`${i}-dropdown-option-body--disabled`],onMousemove:this.handleMouseMove,onMouseenter:this.handleMouseEnter,onMouseleave:this.handleMouseLeave,onClick:this.handleClick},x=d==null?void 0:d(r),h=T("div",Object.assign({class:[`${i}-dropdown-option`,x==null?void 0:x.class],"data-dropdown-option":!0},x),T("div",Ur(g,f),[T("div",{class:[`${i}-dropdown-option-body__prefix`,s&&`${i}-dropdown-option-body__prefix--show-icon`]},[c?c(r):kt(r.icon)]),T("div",{"data-dropdown-option":!0,class:`${i}-dropdown-option-body__label`},a?a(r):kt((t=r[this.labelField])!==null&&t!==void 0?t:r.title)),T("div",{"data-dropdown-option":!0,class:[`${i}-dropdown-option-body__suffix`,l&&`${i}-dropdown-option-body__suffix--has-submenu`]},this.hasSubmenu?T(XT,null,{default:()=>T(u2,null)}):null)]),this.hasSubmenu?T(Mp,null,{default:()=>[T(Fp,null,{default:()=>T("div",{class:`${i}-dropdown-offset-container`},T(Dp,{show:this.mergedShowSubmenu,placement:this.placement,to:m&&this.popoverBody||void 0,teleportDisabled:!m},{default:()=>T("div",{class:`${i}-dropdown-menu-wrapper`},n?T(mn,{onBeforeEnter:this.handleSubmenuBeforeEnter,onAfterEnter:this.handleSubmenuAfterEnter,name:"fade-in-scale-up-transition",appear:!0},{default:()=>p}):p)}))})]}):null);return u?u({node:h,option:r}):h}}),JT=ge({name:"NDropdownGroup",props:{clsPrefix:{type:String,required:!0},tmNode:{type:Object,required:!0},parentKey:{type:[String,Number],default:null}},render(){const{tmNode:e,parentKey:t,clsPrefix:n}=this,{children:r}=e;return T(We,null,T(UT,{clsPrefix:n,tmNode:e,key:e.key}),r==null?void 0:r.map(o=>{const{rawNode:i}=o;return i.show===!1?null:Qg(i)?T(Jg,{clsPrefix:n,key:o.key}):o.isGroup?(hr("dropdown","`group` node is not allowed to be put in `group` node."),null):T(ev,{clsPrefix:n,tmNode:o,parentKey:t,key:o.key})}))}}),QT=ge({name:"DropdownRenderOption",props:{tmNode:{type:Object,required:!0}},render(){const{rawNode:{render:e,props:t}}=this.tmNode;return T("div",t,[e==null?void 0:e()])}}),tv=ge({name:"DropdownMenu",props:{scrollable:Boolean,showArrow:Boolean,arrowStyle:[String,Object],clsPrefix:{type:String,required:!0},tmNodes:{type:Array,default:()=>[]},parentKey:{type:[String,Number],default:null}},setup(e){const{renderIconRef:t,childrenFieldRef:n}=Se(Gs);ze(Cc,{showIconRef:V(()=>{const o=t.value;return e.tmNodes.some(i=>{var s;if(i.isGroup)return(s=i.children)===null||s===void 0?void 0:s.some(({rawNode:a})=>o?o(a):a.icon);const{rawNode:l}=i;return o?o(l):l.icon})}),hasSubmenuRef:V(()=>{const{value:o}=n;return e.tmNodes.some(i=>{var s;if(i.isGroup)return(s=i.children)===null||s===void 0?void 0:s.some(({rawNode:a})=>ma(a,o));const{rawNode:l}=i;return ma(l,o)})})});const r=D(null);return ze(Ns,null),ze(Fs,null),ze(pi,r),{bodyRef:r}},render(){const{parentKey:e,clsPrefix:t,scrollable:n}=this,r=this.tmNodes.map(o=>{const{rawNode:i}=o;return i.show===!1?null:ZT(i)?T(QT,{tmNode:o,key:o.key}):Qg(i)?T(Jg,{clsPrefix:t,key:o.key}):qT(i)?T(JT,{clsPrefix:t,tmNode:o,parentKey:e,key:o.key}):T(ev,{clsPrefix:t,tmNode:o,parentKey:e,key:o.key,props:i.props,scrollable:n})});return T("div",{class:[`${t}-dropdown-menu`,n&&`${t}-dropdown-menu--scrollable`],ref:"bodyRef"},n?T(Hg,{contentClass:`${t}-dropdown-menu__content`},{default:()=>r}):r,this.showArrow?Gg({clsPrefix:t,arrowStyle:this.arrowStyle,arrowClass:void 0,arrowWrapperClass:void 0,arrowWrapperStyle:void 0}):null)}}),e$=oe("dropdown-menu",`
 transform-origin: var(--v-transform-origin);
 background-color: var(--n-color);
 border-radius: var(--n-border-radius);
 box-shadow: var(--n-box-shadow);
 position: relative;
 transition:
 background-color .3s var(--n-bezier),
 box-shadow .3s var(--n-bezier);
`,[Ug(),oe("dropdown-option",`
 position: relative;
 `,[U("a",`
 text-decoration: none;
 color: inherit;
 outline: none;
 `,[U("&::before",`
 content: "";
 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 `)]),oe("dropdown-option-body",`
 display: flex;
 cursor: pointer;
 position: relative;
 height: var(--n-option-height);
 line-height: var(--n-option-height);
 font-size: var(--n-font-size);
 color: var(--n-option-text-color);
 transition: color .3s var(--n-bezier);
 `,[U("&::before",`
 content: "";
 position: absolute;
 top: 0;
 bottom: 0;
 left: 4px;
 right: 4px;
 transition: background-color .3s var(--n-bezier);
 border-radius: var(--n-border-radius);
 `),ur("disabled",[X("pending",`
 color: var(--n-option-text-color-hover);
 `,[Z("prefix, suffix",`
 color: var(--n-option-text-color-hover);
 `),U("&::before","background-color: var(--n-option-color-hover);")]),X("active",`
 color: var(--n-option-text-color-active);
 `,[Z("prefix, suffix",`
 color: var(--n-option-text-color-active);
 `),U("&::before","background-color: var(--n-option-color-active);")]),X("child-active",`
 color: var(--n-option-text-color-child-active);
 `,[Z("prefix, suffix",`
 color: var(--n-option-text-color-child-active);
 `)])]),X("disabled",`
 cursor: not-allowed;
 opacity: var(--n-option-opacity-disabled);
 `),X("group",`
 font-size: calc(var(--n-font-size) - 1px);
 color: var(--n-group-header-text-color);
 `,[Z("prefix",`
 width: calc(var(--n-option-prefix-width) / 2);
 `,[X("show-icon",`
 width: calc(var(--n-option-icon-prefix-width) / 2);
 `)])]),Z("prefix",`
 width: var(--n-option-prefix-width);
 display: flex;
 justify-content: center;
 align-items: center;
 color: var(--n-prefix-color);
 transition: color .3s var(--n-bezier);
 z-index: 1;
 `,[X("show-icon",`
 width: var(--n-option-icon-prefix-width);
 `),oe("icon",`
 font-size: var(--n-option-icon-size);
 `)]),Z("label",`
 white-space: nowrap;
 flex: 1;
 z-index: 1;
 `),Z("suffix",`
 box-sizing: border-box;
 flex-grow: 0;
 flex-shrink: 0;
 display: flex;
 justify-content: flex-end;
 align-items: center;
 min-width: var(--n-option-suffix-width);
 padding: 0 8px;
 transition: color .3s var(--n-bezier);
 color: var(--n-suffix-color);
 z-index: 1;
 `,[X("has-submenu",`
 width: var(--n-option-icon-suffix-width);
 `),oe("icon",`
 font-size: var(--n-option-icon-size);
 `)]),oe("dropdown-menu","pointer-events: all;")]),oe("dropdown-offset-container",`
 pointer-events: none;
 position: absolute;
 left: 0;
 right: 0;
 top: -4px;
 bottom: -4px;
 `)]),oe("dropdown-divider",`
 transition: background-color .3s var(--n-bezier);
 background-color: var(--n-divider-color);
 height: 1px;
 margin: 4px 0;
 `),oe("dropdown-menu-wrapper",`
 transform-origin: var(--v-transform-origin);
 width: fit-content;
 `),U(">",[oe("scrollbar",`
 height: inherit;
 max-height: inherit;
 `)]),ur("scrollable",`
 padding: var(--n-padding);
 `),X("scrollable",[Z("content",`
 padding: var(--n-padding);
 `)])]),t$={animated:{type:Boolean,default:!0},keyboard:{type:Boolean,default:!0},size:{type:String,default:"medium"},inverted:Boolean,placement:{type:String,default:"bottom"},onSelect:[Function,Array],options:{type:Array,default:()=>[]},menuProps:Function,showArrow:Boolean,renderLabel:Function,renderIcon:Function,renderOption:Function,nodeProps:Function,labelField:{type:String,default:"label"},keyField:{type:String,default:"key"},childrenField:{type:String,default:"children"},value:[String,Number]},n$=Object.keys(xc),r$=Object.assign(Object.assign(Object.assign({},xc),t$),it.props),mf=ge({name:"Dropdown",inheritAttrs:!1,props:r$,setup(e){const t=D(!1),n=Za(Qe(e,"show"),t),r=V(()=>{const{keyField:R,childrenField:j}=e;return V2(e.options,{getKey(Q){return Q[R]},getDisabled(Q){return Q.disabled===!0},getIgnored(Q){return Q.type==="divider"||Q.type==="render"},getChildren(Q){return Q[j]}})}),o=V(()=>r.value.treeNodes),i=D(null),s=D(null),l=D(null),a=V(()=>{var R,j,Q;return(Q=(j=(R=i.value)!==null&&R!==void 0?R:s.value)!==null&&j!==void 0?j:l.value)!==null&&Q!==void 0?Q:null}),c=V(()=>r.value.getPath(a.value).keyPath),u=V(()=>r.value.getPath(e.value).keyPath),d=an(()=>e.keyboard&&n.value);Dy({keydown:{ArrowUp:{prevent:!0,handler:C},ArrowRight:{prevent:!0,handler:b},ArrowDown:{prevent:!0,handler:S},ArrowLeft:{prevent:!0,handler:P},Enter:{prevent:!0,handler:v},Escape:y}},d);const{mergedClsPrefixRef:f,inlineThemeDisabled:m}=Pt(e),p=it("Dropdown","-dropdown",e$,WT,e,f);ze(Gs,{labelFieldRef:Qe(e,"labelField"),childrenFieldRef:Qe(e,"childrenField"),renderLabelRef:Qe(e,"renderLabel"),renderIconRef:Qe(e,"renderIcon"),hoverKeyRef:i,keyboardKeyRef:s,lastToggledSubmenuKeyRef:l,pendingKeyPathRef:c,activeKeyPathRef:u,animatedRef:Qe(e,"animated"),mergedShowRef:n,nodePropsRef:Qe(e,"nodeProps"),renderOptionRef:Qe(e,"renderOption"),menuPropsRef:Qe(e,"menuProps"),doSelect:g,doUpdateShow:x}),Ye(n,R=>{!e.animated&&!R&&h()});function g(R,j){const{onSelect:Q}=e;Q&&Xt(Q,R,j)}function x(R){const{"onUpdate:show":j,onUpdateShow:Q}=e;j&&Xt(j,R),Q&&Xt(Q,R),t.value=R}function h(){i.value=null,s.value=null,l.value=null}function y(){x(!1)}function P(){A("left")}function b(){A("right")}function C(){A("up")}function S(){A("down")}function v(){const R=E();R!=null&&R.isLeaf&&n.value&&(g(R.key,R.rawNode),x(!1))}function E(){var R;const{value:j}=r,{value:Q}=a;return!j||Q===null?null:(R=j.getNode(Q))!==null&&R!==void 0?R:null}function A(R){const{value:j}=a,{value:{getFirstAvailableNode:Q}}=r;let H=null;if(j===null){const te=Q();te!==null&&(H=te.key)}else{const te=E();if(te){let W;switch(R){case"down":W=te.getNext();break;case"up":W=te.getPrev();break;case"right":W=te.getChild();break;case"left":W=te.getParent();break}W&&(H=W.key)}}H!==null&&(i.value=null,s.value=H)}const F=V(()=>{const{size:R,inverted:j}=e,{common:{cubicBezierEaseInOut:Q},self:H}=p.value,{padding:te,dividerColor:W,borderRadius:re,optionOpacityDisabled:ue,[he("optionIconSuffixWidth",R)]:pe,[he("optionSuffixWidth",R)]:we,[he("optionIconPrefixWidth",R)]:Te,[he("optionPrefixWidth",R)]:Ue,[he("fontSize",R)]:st,[he("optionHeight",R)]:gt,[he("optionIconSize",R)]:be}=H,me={"--n-bezier":Q,"--n-font-size":st,"--n-padding":te,"--n-border-radius":re,"--n-option-height":gt,"--n-option-prefix-width":Ue,"--n-option-icon-prefix-width":Te,"--n-option-suffix-width":we,"--n-option-icon-suffix-width":pe,"--n-option-icon-size":be,"--n-divider-color":W,"--n-option-opacity-disabled":ue};return j?(me["--n-color"]=H.colorInverted,me["--n-option-color-hover"]=H.optionColorHoverInverted,me["--n-option-color-active"]=H.optionColorActiveInverted,me["--n-option-text-color"]=H.optionTextColorInverted,me["--n-option-text-color-hover"]=H.optionTextColorHoverInverted,me["--n-option-text-color-active"]=H.optionTextColorActiveInverted,me["--n-option-text-color-child-active"]=H.optionTextColorChildActiveInverted,me["--n-prefix-color"]=H.prefixColorInverted,me["--n-suffix-color"]=H.suffixColorInverted,me["--n-group-header-text-color"]=H.groupHeaderTextColorInverted):(me["--n-color"]=H.color,me["--n-option-color-hover"]=H.optionColorHover,me["--n-option-color-active"]=H.optionColorActive,me["--n-option-text-color"]=H.optionTextColor,me["--n-option-text-color-hover"]=H.optionTextColorHover,me["--n-option-text-color-active"]=H.optionTextColorActive,me["--n-option-text-color-child-active"]=H.optionTextColorChildActive,me["--n-prefix-color"]=H.prefixColor,me["--n-suffix-color"]=H.suffixColor,me["--n-group-header-text-color"]=H.groupHeaderTextColor),me}),B=m?dn("dropdown",V(()=>`${e.size[0]}${e.inverted?"i":""}`),F,e):void 0;return{mergedClsPrefix:f,mergedTheme:p,tmNodes:o,mergedShow:n,handleAfterLeave:()=>{e.animated&&h()},doUpdateShow:x,cssVars:m?void 0:F,themeClass:B==null?void 0:B.themeClass,onRender:B==null?void 0:B.onRender}},render(){const e=(r,o,i,s,l)=>{var a;const{mergedClsPrefix:c,menuProps:u}=this;(a=this.onRender)===null||a===void 0||a.call(this);const d=(u==null?void 0:u(void 0,this.tmNodes.map(m=>m.rawNode)))||{},f={ref:Mx(o),class:[r,`${c}-dropdown`,this.themeClass],clsPrefix:c,tmNodes:this.tmNodes,style:[...i,this.cssVars],showArrow:this.showArrow,arrowStyle:this.arrowStyle,scrollable:this.scrollable,onMouseenter:s,onMouseleave:l};return T(tv,Ur(this.$attrs,f,d))},{mergedTheme:t}=this,n={show:this.mergedShow,theme:t.peers.Popover,themeOverrides:t.peerOverrides.Popover,internalOnAfterLeave:this.handleAfterLeave,internalRenderBody:e,onUpdateShow:this.doUpdateShow,"onUpdate:show":void 0};return T(Yg,Object.assign({},Vn(this.$props,n$),n),{trigger:()=>{var r,o;return(o=(r=this.$slots).default)===null||o===void 0?void 0:o.call(r)}})}}),nv="n-dialog-provider",rv="n-dialog-api",o$="n-dialog-reactive-list";function i$(){const e=Se(rv,null);return e===null&&Vr("use-dialog","No outer <n-dialog-provider /> founded."),e}const s$={titleFontSize:"18px",padding:"16px 28px 20px 28px",iconSize:"28px",actionSpace:"12px",contentMargin:"8px 0 16px 0",iconMargin:"0 4px 0 0",iconMarginIconTop:"4px 0 8px 0",closeSize:"22px",closeIconSize:"18px",closeMargin:"20px 26px 0 0",closeMarginIconTop:"10px 16px 0 0"};function l$(e){const{textColor1:t,textColor2:n,modalColor:r,closeIconColor:o,closeIconColorHover:i,closeIconColorPressed:s,closeColorHover:l,closeColorPressed:a,infoColor:c,successColor:u,warningColor:d,errorColor:f,primaryColor:m,dividerColor:p,borderRadius:g,fontWeightStrong:x,lineHeight:h,fontSize:y}=e;return Object.assign(Object.assign({},s$),{fontSize:y,lineHeight:h,border:`1px solid ${p}`,titleTextColor:t,textColor:n,color:r,closeColorHover:l,closeColorPressed:a,closeIconColor:o,closeIconColorHover:i,closeIconColorPressed:s,closeBorderRadius:g,iconColor:m,iconColorInfo:c,iconColorSuccess:u,iconColorWarning:d,iconColorError:f,borderRadius:g,titleFontWeight:x})}const ov={name:"Dialog",common:fn,peers:{Button:Xg},self:l$},Ys={icon:Function,type:{type:String,default:"default"},title:[String,Function],closable:{type:Boolean,default:!0},negativeText:String,positiveText:String,positiveButtonProps:Object,negativeButtonProps:Object,content:[String,Function],action:Function,showIcon:{type:Boolean,default:!0},loading:Boolean,bordered:Boolean,iconPlacement:String,titleClass:[String,Array],titleStyle:[String,Object],contentClass:[String,Array],contentStyle:[String,Object],actionClass:[String,Array],actionStyle:[String,Object],onPositiveClick:Function,onNegativeClick:Function,onClose:Function},iv=Ds(Ys),a$=U([oe("dialog",`
 --n-icon-margin: var(--n-icon-margin-top) var(--n-icon-margin-right) var(--n-icon-margin-bottom) var(--n-icon-margin-left);
 word-break: break-word;
 line-height: var(--n-line-height);
 position: relative;
 background: var(--n-color);
 color: var(--n-text-color);
 box-sizing: border-box;
 margin: auto;
 border-radius: var(--n-border-radius);
 padding: var(--n-padding);
 transition: 
 border-color .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier);
 `,[Z("icon",{color:"var(--n-icon-color)"}),X("bordered",{border:"var(--n-border)"}),X("icon-top",[Z("close",{margin:"var(--n-close-margin)"}),Z("icon",{margin:"var(--n-icon-margin)"}),Z("content",{textAlign:"center"}),Z("title",{justifyContent:"center"}),Z("action",{justifyContent:"center"})]),X("icon-left",[Z("icon",{margin:"var(--n-icon-margin)"}),X("closable",[Z("title",`
 padding-right: calc(var(--n-close-size) + 6px);
 `)])]),Z("close",`
 position: absolute;
 right: 0;
 top: 0;
 margin: var(--n-close-margin);
 transition:
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier);
 z-index: 1;
 `),Z("content",`
 font-size: var(--n-font-size);
 margin: var(--n-content-margin);
 position: relative;
 word-break: break-word;
 `,[X("last","margin-bottom: 0;")]),Z("action",`
 display: flex;
 justify-content: flex-end;
 `,[U("> *:not(:last-child)",`
 margin-right: var(--n-action-space);
 `)]),Z("icon",`
 font-size: var(--n-icon-size);
 transition: color .3s var(--n-bezier);
 `),Z("title",`
 transition: color .3s var(--n-bezier);
 display: flex;
 align-items: center;
 font-size: var(--n-title-font-size);
 font-weight: var(--n-title-font-weight);
 color: var(--n-title-text-color);
 `),oe("dialog-icon-container",`
 display: flex;
 justify-content: center;
 `)]),Cp(oe("dialog",`
 width: 446px;
 max-width: calc(100vw - 32px);
 `)),oe("dialog",[Sp(`
 width: 446px;
 max-width: calc(100vw - 32px);
 `)])]),c$={default:()=>T(ms,null),info:()=>T(ms,null),success:()=>T(pc,null),warning:()=>T(gc,null),error:()=>T(hc,null)},sv=ge({name:"Dialog",alias:["NimbusConfirmCard","Confirm"],props:Object.assign(Object.assign({},it.props),Ys),slots:Object,setup(e){const{mergedComponentPropsRef:t,mergedClsPrefixRef:n,inlineThemeDisabled:r,mergedRtlRef:o}=Pt(e),i=Eo("Dialog",o,n),s=V(()=>{var m,p;const{iconPlacement:g}=e;return g||((p=(m=t==null?void 0:t.value)===null||m===void 0?void 0:m.Dialog)===null||p===void 0?void 0:p.iconPlacement)||"left"});function l(m){const{onPositiveClick:p}=e;p&&p(m)}function a(m){const{onNegativeClick:p}=e;p&&p(m)}function c(){const{onClose:m}=e;m&&m()}const u=it("Dialog","-dialog",a$,ov,e,n),d=V(()=>{const{type:m}=e,p=s.value,{common:{cubicBezierEaseInOut:g},self:{fontSize:x,lineHeight:h,border:y,titleTextColor:P,textColor:b,color:C,closeBorderRadius:S,closeColorHover:v,closeColorPressed:E,closeIconColor:A,closeIconColorHover:F,closeIconColorPressed:B,closeIconSize:R,borderRadius:j,titleFontWeight:Q,titleFontSize:H,padding:te,iconSize:W,actionSpace:re,contentMargin:ue,closeSize:pe,[p==="top"?"iconMarginIconTop":"iconMargin"]:we,[p==="top"?"closeMarginIconTop":"closeMargin"]:Te,[he("iconColor",m)]:Ue}}=u.value,st=lr(we);return{"--n-font-size":x,"--n-icon-color":Ue,"--n-bezier":g,"--n-close-margin":Te,"--n-icon-margin-top":st.top,"--n-icon-margin-right":st.right,"--n-icon-margin-bottom":st.bottom,"--n-icon-margin-left":st.left,"--n-icon-size":W,"--n-close-size":pe,"--n-close-icon-size":R,"--n-close-border-radius":S,"--n-close-color-hover":v,"--n-close-color-pressed":E,"--n-close-icon-color":A,"--n-close-icon-color-hover":F,"--n-close-icon-color-pressed":B,"--n-color":C,"--n-text-color":b,"--n-border-radius":j,"--n-padding":te,"--n-line-height":h,"--n-border":y,"--n-content-margin":ue,"--n-title-font-size":H,"--n-title-font-weight":Q,"--n-title-text-color":P,"--n-action-space":re}}),f=r?dn("dialog",V(()=>`${e.type[0]}${s.value[0]}`),d,e):void 0;return{mergedClsPrefix:n,rtlEnabled:i,mergedIconPlacement:s,mergedTheme:u,handlePositiveClick:l,handleNegativeClick:a,handleCloseClick:c,cssVars:r?void 0:d,themeClass:f==null?void 0:f.themeClass,onRender:f==null?void 0:f.onRender}},render(){var e;const{bordered:t,mergedIconPlacement:n,cssVars:r,closable:o,showIcon:i,title:s,content:l,action:a,negativeText:c,positiveText:u,positiveButtonProps:d,negativeButtonProps:f,handlePositiveClick:m,handleNegativeClick:p,mergedTheme:g,loading:x,type:h,mergedClsPrefix:y}=this;(e=this.onRender)===null||e===void 0||e.call(this);const P=i?T(Vs,{clsPrefix:y,class:`${y}-dialog__icon`},{default:()=>Yt(this.$slots.icon,C=>C||(this.icon?kt(this.icon):c$[this.type]()))}):null,b=Yt(this.$slots.action,C=>C||u||c||a?T("div",{class:[`${y}-dialog__action`,this.actionClass],style:this.actionStyle},C||(a?[kt(a)]:[this.negativeText&&T(df,Object.assign({theme:g.peers.Button,themeOverrides:g.peerOverrides.Button,ghost:!0,size:"small",onClick:p},f),{default:()=>kt(this.negativeText)}),this.positiveText&&T(df,Object.assign({theme:g.peers.Button,themeOverrides:g.peerOverrides.Button,size:"small",type:h==="default"?"primary":h,disabled:x,loading:x,onClick:m},d),{default:()=>kt(this.positiveText)})])):null);return T("div",{class:[`${y}-dialog`,this.themeClass,this.closable&&`${y}-dialog--closable`,`${y}-dialog--icon-${n}`,t&&`${y}-dialog--bordered`,this.rtlEnabled&&`${y}-dialog--rtl`],style:r,role:"dialog"},o?Yt(this.$slots.close,C=>{const S=[`${y}-dialog__close`,this.rtlEnabled&&`${y}-dialog--rtl`];return C?T("div",{class:S},C):T(Ks,{clsPrefix:y,class:S,onClick:this.handleCloseClick})}):null,i&&n==="top"?T("div",{class:`${y}-dialog-icon-container`},P):null,T("div",{class:[`${y}-dialog__title`,this.titleClass],style:this.titleStyle},i&&n==="left"?P:null,Cd(this.$slots.header,()=>[kt(s)])),T("div",{class:[`${y}-dialog__content`,b?"":`${y}-dialog__content--last`,this.contentClass],style:this.contentStyle},Cd(this.$slots.default,()=>[kt(l)])),b)}});function u$(e){const{modalColor:t,textColor2:n,boxShadow3:r}=e;return{color:t,textColor:n,boxShadow:r}}const d$={name:"Modal",common:fn,peers:{Scrollbar:mc,Dialog:ov,Card:qg},self:u$},f$="n-modal-provider",lv="n-modal-api",h$="n-modal-reactive-list";function p$(){const e=Se(lv,null);return e===null&&Vr("use-modal","No outer <n-modal-provider /> founded."),e}const ba="n-draggable";function g$(e,t){let n;const r=V(()=>e.value!==!1),o=V(()=>r.value?ba:""),i=V(()=>{const a=e.value;return a===!0||a===!1?!0:a?a.bounds!=="none":!0});function s(a){const c=a.querySelector(`.${ba}`);if(!c||!o.value)return;let u=0,d=0,f=0,m=0,p=0,g=0,x;function h(b){b.preventDefault(),x=b;const{x:C,y:S,right:v,bottom:E}=a.getBoundingClientRect();d=C,m=S,u=window.innerWidth-v,f=window.innerHeight-E;const{left:A,top:F}=a.style;p=+F.slice(0,-2),g=+A.slice(0,-2)}function y(b){if(!x)return;const{clientX:C,clientY:S}=x;let v=b.clientX-C,E=b.clientY-S;i.value&&(v>u?v=u:-v>d&&(v=-d),E>f?E=f:-E>m&&(E=-m));const A=v+g,F=E+p;a.style.top=`${F}px`,a.style.left=`${A}px`}function P(){x=void 0,t.onEnd(a)}De("mousedown",c,h),De("mousemove",window,y),De("mouseup",window,P),n=()=>{Ne("mousedown",c,h),De("mousemove",window,y),De("mouseup",window,P)}}function l(){n&&(n(),n=void 0)}return zr(l),{stopDrag:l,startDrag:s,draggableRef:r,draggableClassRef:o}}const Sc=Object.assign(Object.assign({},wc),Ys),v$=Ds(Sc),m$=ge({name:"ModalBody",inheritAttrs:!1,slots:Object,props:Object.assign(Object.assign({show:{type:Boolean,required:!0},preset:String,displayDirective:{type:String,required:!0},trapFocus:{type:Boolean,default:!0},autoFocus:{type:Boolean,default:!0},blockScroll:Boolean,draggable:{type:[Boolean,Object],default:!1}},Sc),{renderMask:Function,onClickoutside:Function,onBeforeLeave:{type:Function,required:!0},onAfterLeave:{type:Function,required:!0},onPositiveClick:{type:Function,required:!0},onNegativeClick:{type:Function,required:!0},onClose:{type:Function,required:!0},onAfterEnter:Function,onEsc:Function}),setup(e){const t=D(null),n=D(null),r=D(e.show),o=D(null),i=D(null),s=Se(Ap);let l=null;Ye(Qe(e,"show"),E=>{E&&(l=s.getMousePosition())},{immediate:!0});const{stopDrag:a,startDrag:c,draggableRef:u,draggableClassRef:d}=g$(Qe(e,"draggable"),{onEnd:E=>{g(E)}}),f=V(()=>Wn([e.titleClass,d.value])),m=V(()=>Wn([e.headerClass,d.value]));Ye(Qe(e,"show"),E=>{E&&(r.value=!0)}),Wy(V(()=>e.blockScroll&&r.value));function p(){if(s.transformOriginRef.value==="center")return"";const{value:E}=o,{value:A}=i;if(E===null||A===null)return"";if(n.value){const F=n.value.containerScrollTop;return`${E}px ${A+F}px`}return""}function g(E){if(s.transformOriginRef.value==="center"||!l||!n.value)return;const A=n.value.containerScrollTop,{offsetLeft:F,offsetTop:B}=E,R=l.y,j=l.x;o.value=-(F-j),i.value=-(B-R-A),E.style.transformOrigin=p()}function x(E){qt(()=>{g(E)})}function h(E){E.style.transformOrigin=p(),e.onBeforeLeave()}function y(E){const A=E;u.value&&c(A),e.onAfterEnter&&e.onAfterEnter(A)}function P(){r.value=!1,o.value=null,i.value=null,a(),e.onAfterLeave()}function b(){const{onClose:E}=e;E&&E()}function C(){e.onNegativeClick()}function S(){e.onPositiveClick()}const v=D(null);return Ye(v,E=>{E&&qt(()=>{const A=E.el;A&&t.value!==A&&(t.value=A)})}),ze(Ns,t),ze(Fs,null),ze(pi,null),{mergedTheme:s.mergedThemeRef,appear:s.appearRef,isMounted:s.isMountedRef,mergedClsPrefix:s.mergedClsPrefixRef,bodyRef:t,scrollbarRef:n,draggableClass:d,displayed:r,childNodeRef:v,cardHeaderClass:m,dialogTitleClass:f,handlePositiveClick:S,handleNegativeClick:C,handleCloseClick:b,handleAfterEnter:y,handleAfterLeave:P,handleBeforeLeave:h,handleEnter:x}},render(){const{$slots:e,$attrs:t,handleEnter:n,handleAfterEnter:r,handleAfterLeave:o,handleBeforeLeave:i,preset:s,mergedClsPrefix:l}=this;let a=null;if(!s){if(a=Nx("default",e.default,{draggableClass:this.draggableClass}),!a){hr("modal","default slot is empty");return}a=cn(a),a.props=Ur({class:`${l}-modal`},t,a.props||{})}return this.displayDirective==="show"||this.displayed||this.show?Un(T("div",{role:"none",class:`${l}-modal-body-wrapper`},T(bc,{ref:"scrollbarRef",theme:this.mergedTheme.peers.Scrollbar,themeOverrides:this.mergedTheme.peerOverrides.Scrollbar,contentClass:`${l}-modal-scroll-content`},{default:()=>{var c;return[(c=this.renderMask)===null||c===void 0?void 0:c.call(this),T(Xp,{disabled:!this.trapFocus,active:this.show,onEsc:this.onEsc,autoFocus:this.autoFocus},{default:()=>{var u;return T(mn,{name:"fade-in-scale-up-transition",appear:(u=this.appear)!==null&&u!==void 0?u:this.isMounted,onEnter:n,onAfterEnter:r,onAfterLeave:o,onBeforeLeave:i},{default:()=>{const d=[[Jo,this.show]],{onClickoutside:f}=this;return f&&d.push([oa,this.onClickoutside,void 0,{capture:!0}]),Un(this.preset==="confirm"||this.preset==="dialog"?T(sv,Object.assign({},this.$attrs,{class:[`${l}-modal`,this.$attrs.class],ref:"bodyRef",theme:this.mergedTheme.peers.Dialog,themeOverrides:this.mergedTheme.peerOverrides.Dialog},Vn(this.$props,iv),{titleClass:this.dialogTitleClass,"aria-modal":"true"}),e):this.preset==="card"?T(bT,Object.assign({},this.$attrs,{ref:"bodyRef",class:[`${l}-modal`,this.$attrs.class],theme:this.mergedTheme.peers.Card,themeOverrides:this.mergedTheme.peerOverrides.Card},Vn(this.$props,vT),{headerClass:this.cardHeaderClass,"aria-modal":"true",role:"dialog"}),e):this.childNodeRef=a,d)}})}})]}})),[[Jo,this.displayDirective==="if"||this.displayed||this.show]]):null}}),b$=U([oe("modal-container",`
 position: fixed;
 left: 0;
 top: 0;
 height: 0;
 width: 0;
 display: flex;
 `),oe("modal-mask",`
 position: fixed;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 background-color: rgba(0, 0, 0, .4);
 `,[vc({enterDuration:".25s",leaveDuration:".25s",enterCubicBezier:"var(--n-bezier-ease-out)",leaveCubicBezier:"var(--n-bezier-ease-out)"})]),oe("modal-body-wrapper",`
 position: fixed;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 overflow: visible;
 `,[oe("modal-scroll-content",`
 min-height: 100%;
 display: flex;
 position: relative;
 `)]),oe("modal",`
 position: relative;
 align-self: center;
 color: var(--n-text-color);
 margin: auto;
 box-shadow: var(--n-box-shadow);
 `,[Ug({duration:".25s",enterScale:".5"}),U(`.${ba}`,`
 cursor: move;
 user-select: none;
 `)])]),av=Object.assign(Object.assign(Object.assign(Object.assign({},it.props),{show:Boolean,unstableShowMask:{type:Boolean,default:!0},maskClosable:{type:Boolean,default:!0},preset:String,to:[String,Object],displayDirective:{type:String,default:"if"},transformOrigin:{type:String,default:"mouse"},zIndex:Number,autoFocus:{type:Boolean,default:!0},trapFocus:{type:Boolean,default:!0},closeOnEsc:{type:Boolean,default:!0},blockScroll:{type:Boolean,default:!0}}),Sc),{draggable:[Boolean,Object],onEsc:Function,"onUpdate:show":[Function,Array],onUpdateShow:[Function,Array],onAfterEnter:Function,onBeforeLeave:Function,onAfterLeave:Function,onClose:Function,onPositiveClick:Function,onNegativeClick:Function,onMaskClick:Function,internalDialog:Boolean,internalModal:Boolean,internalAppear:{type:Boolean,default:void 0},overlayStyle:[String,Object],onBeforeHide:Function,onAfterHide:Function,onHide:Function}),cv=ge({name:"Modal",inheritAttrs:!1,props:av,slots:Object,setup(e){const t=D(null),{mergedClsPrefixRef:n,namespaceRef:r,inlineThemeDisabled:o}=Pt(e),i=it("Modal","-modal",b$,d$,e,n),s=qa(64),l=Xa(),a=hi(),c=e.internalDialog?Se(nv,null):null,u=e.internalModal?Se(By,null):null,d=jy();function f(S){const{onUpdateShow:v,"onUpdate:show":E,onHide:A}=e;v&&Xt(v,S),E&&Xt(E,S),A&&!S&&A(S)}function m(){const{onClose:S}=e;S?Promise.resolve(S()).then(v=>{v!==!1&&f(!1)}):f(!1)}function p(){const{onPositiveClick:S}=e;S?Promise.resolve(S()).then(v=>{v!==!1&&f(!1)}):f(!1)}function g(){const{onNegativeClick:S}=e;S?Promise.resolve(S()).then(v=>{v!==!1&&f(!1)}):f(!1)}function x(){const{onBeforeLeave:S,onBeforeHide:v}=e;S&&Xt(S),v&&v()}function h(){const{onAfterLeave:S,onAfterHide:v}=e;S&&Xt(S),v&&v()}function y(S){var v;const{onMaskClick:E}=e;E&&E(S),e.maskClosable&&!((v=t.value)===null||v===void 0)&&v.contains(xo(S))&&f(!1)}function P(S){var v;(v=e.onEsc)===null||v===void 0||v.call(e),e.show&&e.closeOnEsc&&Lx(S)&&(d.value||f(!1))}ze(Ap,{getMousePosition:()=>{const S=c||u;if(S){const{clickedRef:v,clickedPositionRef:E}=S;if(v.value&&E.value)return E.value}return s.value?l.value:null},mergedClsPrefixRef:n,mergedThemeRef:i,isMountedRef:a,appearRef:Qe(e,"internalAppear"),transformOriginRef:Qe(e,"transformOrigin")});const b=V(()=>{const{common:{cubicBezierEaseOut:S},self:{boxShadow:v,color:E,textColor:A}}=i.value;return{"--n-bezier-ease-out":S,"--n-box-shadow":v,"--n-color":E,"--n-text-color":A}}),C=o?dn("theme-class",void 0,b,e):void 0;return{mergedClsPrefix:n,namespace:r,isMounted:a,containerRef:t,presetProps:V(()=>Vn(e,v$)),handleEsc:P,handleAfterLeave:h,handleClickoutside:y,handleBeforeLeave:x,doUpdateShow:f,handleNegativeClick:g,handlePositiveClick:p,handleCloseClick:m,cssVars:o?void 0:b,themeClass:C==null?void 0:C.themeClass,onRender:C==null?void 0:C.onRender}},render(){const{mergedClsPrefix:e}=this;return T(Np,{to:this.to,show:this.show},{default:()=>{var t;(t=this.onRender)===null||t===void 0||t.call(this);const{unstableShowMask:n}=this;return Un(T("div",{role:"none",ref:"containerRef",class:[`${e}-modal-container`,this.themeClass,this.namespace],style:this.cssVars},T(m$,Object.assign({style:this.overlayStyle},this.$attrs,{ref:"bodyWrapper",displayDirective:this.displayDirective,show:this.show,preset:this.preset,autoFocus:this.autoFocus,trapFocus:this.trapFocus,draggable:this.draggable,blockScroll:this.blockScroll},this.presetProps,{onEsc:this.handleEsc,onClose:this.handleCloseClick,onNegativeClick:this.handleNegativeClick,onPositiveClick:this.handlePositiveClick,onBeforeLeave:this.handleBeforeLeave,onAfterEnter:this.onAfterEnter,onAfterLeave:this.handleAfterLeave,onClickoutside:n?void 0:this.handleClickoutside,renderMask:n?()=>{var r;return T(mn,{name:"fade-in-transition",key:"mask",appear:(r=this.internalAppear)!==null&&r!==void 0?r:this.isMounted},{default:()=>this.show?T("div",{"aria-hidden":!0,ref:"containerRef",class:`${e}-modal-mask`,onClick:this.handleClickoutside}):null})}:void 0}),this.$slots)),[[Qa,{zIndex:this.zIndex,enabled:this.show}]])}})}}),y$=Object.assign(Object.assign({},Ys),{onAfterEnter:Function,onAfterLeave:Function,transformOrigin:String,blockScroll:{type:Boolean,default:!0},closeOnEsc:{type:Boolean,default:!0},onEsc:Function,autoFocus:{type:Boolean,default:!0},internalStyle:[String,Object],maskClosable:{type:Boolean,default:!0},onPositiveClick:Function,onNegativeClick:Function,onClose:Function,onMaskClick:Function,draggable:[Boolean,Object]}),x$=ge({name:"DialogEnvironment",props:Object.assign(Object.assign({},y$),{internalKey:{type:String,required:!0},to:[String,Object],onInternalAfterLeave:{type:Function,required:!0}}),setup(e){const t=D(!0);function n(){const{onInternalAfterLeave:u,internalKey:d,onAfterLeave:f}=e;u&&u(d),f&&f()}function r(u){const{onPositiveClick:d}=e;d?Promise.resolve(d(u)).then(f=>{f!==!1&&a()}):a()}function o(u){const{onNegativeClick:d}=e;d?Promise.resolve(d(u)).then(f=>{f!==!1&&a()}):a()}function i(){const{onClose:u}=e;u?Promise.resolve(u()).then(d=>{d!==!1&&a()}):a()}function s(u){const{onMaskClick:d,maskClosable:f}=e;d&&(d(u),f&&a())}function l(){const{onEsc:u}=e;u&&u()}function a(){t.value=!1}function c(u){t.value=u}return{show:t,hide:a,handleUpdateShow:c,handleAfterLeave:n,handleCloseClick:i,handleNegativeClick:o,handlePositiveClick:r,handleMaskClick:s,handleEsc:l}},render(){const{handlePositiveClick:e,handleUpdateShow:t,handleNegativeClick:n,handleCloseClick:r,handleAfterLeave:o,handleMaskClick:i,handleEsc:s,to:l,maskClosable:a,show:c}=this;return T(cv,{show:c,onUpdateShow:t,onMaskClick:i,onEsc:s,to:l,maskClosable:a,onAfterEnter:this.onAfterEnter,onAfterLeave:o,closeOnEsc:this.closeOnEsc,blockScroll:this.blockScroll,autoFocus:this.autoFocus,transformOrigin:this.transformOrigin,draggable:this.draggable,internalAppear:!0,internalDialog:!0},{default:({draggableClass:u})=>T(sv,Object.assign({},Vn(this.$props,iv),{titleClass:Wn([this.titleClass,u]),style:this.internalStyle,onClose:r,onNegativeClick:n,onPositiveClick:e}))})}}),w$={injectionKey:String,to:[String,Object]},_$=ge({name:"DialogProvider",props:w$,setup(){const e=D([]),t={};function n(l={}){const a=fi(),c=yr(Object.assign(Object.assign({},l),{key:a,destroy:()=>{var u;(u=t[`n-dialog-${a}`])===null||u===void 0||u.hide()}}));return e.value.push(c),c}const r=["info","success","warning","error"].map(l=>a=>n(Object.assign(Object.assign({},a),{type:l})));function o(l){const{value:a}=e;a.splice(a.findIndex(c=>c.key===l),1)}function i(){Object.values(t).forEach(l=>{l==null||l.hide()})}const s={create:n,destroyAll:i,info:r[0],success:r[1],warning:r[2],error:r[3]};return ze(rv,s),ze(nv,{clickedRef:qa(64),clickedPositionRef:Xa()}),ze(o$,e),Object.assign(Object.assign({},s),{dialogList:e,dialogInstRefs:t,handleAfterLeave:o})},render(){var e,t;return T(We,null,[this.dialogList.map(n=>T(x$,zs(n,["destroy","style"],{internalStyle:n.style,to:this.to,ref:r=>{r===null?delete this.dialogInstRefs[`n-dialog-${n.key}`]:this.dialogInstRefs[`n-dialog-${n.key}`]=r},internalKey:n.key,onInternalAfterLeave:this.handleAfterLeave}))),(t=(e=this.$slots).default)===null||t===void 0?void 0:t.call(e)])}}),uv="n-loading-bar",dv="n-loading-bar-api";function C$(e){const{primaryColor:t,errorColor:n}=e;return{colorError:n,colorLoading:t,height:"2px"}}const S$={common:fn,self:C$},E$=oe("loading-bar-container",`
 z-index: 5999;
 position: fixed;
 top: 0;
 left: 0;
 right: 0;
 height: 2px;
`,[vc({enterDuration:"0.3s",leaveDuration:"0.8s"}),oe("loading-bar",`
 width: 100%;
 transition:
 max-width 4s linear,
 background .2s linear;
 height: var(--n-height);
 `,[X("starting",`
 background: var(--n-color-loading);
 `),X("finishing",`
 background: var(--n-color-loading);
 transition:
 max-width .2s linear,
 background .2s linear;
 `),X("error",`
 background: var(--n-color-error);
 transition:
 max-width .2s linear,
 background .2s linear;
 `)])]);var Hi=function(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function l(u){try{c(r.next(u))}catch(d){s(d)}}function a(u){try{c(r.throw(u))}catch(d){s(d)}}function c(u){u.done?i(u.value):o(u.value).then(l,a)}c((r=r.apply(e,t||[])).next())})};function ji(e,t){return`${t}-loading-bar ${t}-loading-bar--${e}`}const T$=ge({name:"LoadingBar",props:{containerClass:String,containerStyle:[String,Object]},setup(){const{inlineThemeDisabled:e}=Pt(),{props:t,mergedClsPrefixRef:n}=Se(uv),r=D(null),o=D(!1),i=D(!1),s=D(!1),l=D(!1);let a=!1;const c=D(!1),u=V(()=>{const{loadingBarStyle:C}=t;return C?C[c.value?"error":"loading"]:""});function d(){return Hi(this,void 0,void 0,function*(){o.value=!1,s.value=!1,a=!1,c.value=!1,l.value=!0,yield qt(),l.value=!1})}function f(){return Hi(this,arguments,void 0,function*(C=0,S=80,v="starting"){if(i.value=!0,yield d(),a)return;s.value=!0,yield qt();const E=r.value;E&&(E.style.maxWidth=`${C}%`,E.style.transition="none",E.offsetWidth,E.className=ji(v,n.value),E.style.transition="",E.style.maxWidth=`${S}%`)})}function m(){return Hi(this,void 0,void 0,function*(){if(a||c.value)return;i.value&&(yield qt()),a=!0;const C=r.value;C&&(C.className=ji("finishing",n.value),C.style.maxWidth="100%",C.offsetWidth,s.value=!1)})}function p(){if(!(a||c.value))if(!s.value)f(100,100,"error").then(()=>{c.value=!0;const C=r.value;C&&(C.className=ji("error",n.value),C.offsetWidth,s.value=!1)});else{c.value=!0;const C=r.value;if(!C)return;C.className=ji("error",n.value),C.style.maxWidth="100%",C.offsetWidth,s.value=!1}}function g(){o.value=!0}function x(){o.value=!1}function h(){return Hi(this,void 0,void 0,function*(){yield d()})}const y=it("LoadingBar","-loading-bar",E$,S$,t,n),P=V(()=>{const{self:{height:C,colorError:S,colorLoading:v}}=y.value;return{"--n-height":C,"--n-color-loading":v,"--n-color-error":S}}),b=e?dn("loading-bar",void 0,P,t):void 0;return{mergedClsPrefix:n,loadingBarRef:r,started:i,loading:s,entering:o,transitionDisabled:l,start:f,error:p,finish:m,handleEnter:g,handleAfterEnter:x,handleAfterLeave:h,mergedLoadingBarStyle:u,cssVars:e?void 0:P,themeClass:b==null?void 0:b.themeClass,onRender:b==null?void 0:b.onRender}},render(){if(!this.started)return null;const{mergedClsPrefix:e}=this;return T(mn,{name:"fade-in-transition",appear:!0,onEnter:this.handleEnter,onAfterEnter:this.handleAfterEnter,onAfterLeave:this.handleAfterLeave,css:!this.transitionDisabled},{default:()=>{var t;return(t=this.onRender)===null||t===void 0||t.call(this),Un(T("div",{class:[`${e}-loading-bar-container`,this.themeClass,this.containerClass],style:this.containerStyle},T("div",{ref:"loadingBarRef",class:[`${e}-loading-bar`],style:[this.cssVars,this.mergedLoadingBarStyle]})),[[Jo,this.loading||!this.loading&&this.entering]])}})}}),$$=Object.assign(Object.assign({},it.props),{to:{type:[String,Object,Boolean],default:void 0},containerClass:String,containerStyle:[String,Object],loadingBarStyle:{type:Object}}),P$=ge({name:"LoadingBarProvider",props:$$,setup(e){const t=hi(),n=D(null),r={start(){var i;t.value?(i=n.value)===null||i===void 0||i.start():qt(()=>{var s;(s=n.value)===null||s===void 0||s.start()})},error(){var i;t.value?(i=n.value)===null||i===void 0||i.error():qt(()=>{var s;(s=n.value)===null||s===void 0||s.error()})},finish(){var i;t.value?(i=n.value)===null||i===void 0||i.finish():qt(()=>{var s;(s=n.value)===null||s===void 0||s.finish()})}},{mergedClsPrefixRef:o}=Pt(e);return ze(dv,r),ze(uv,{props:e,mergedClsPrefixRef:o}),Object.assign(r,{loadingBarRef:n})},render(){var e,t;return T(We,null,T(Ts,{disabled:this.to===!1,to:this.to||"body"},T(T$,{ref:"loadingBarRef",containerStyle:this.containerStyle,containerClass:this.containerClass})),(t=(e=this.$slots).default)===null||t===void 0?void 0:t.call(e))}});function O$(){const e=Se(dv,null);return e===null&&Vr("use-loading-bar","No outer <n-loading-bar-provider /> founded."),e}const fv="n-message-api",hv="n-message-provider",k$={margin:"0 0 8px 0",padding:"10px 20px",maxWidth:"720px",minWidth:"420px",iconMargin:"0 10px 0 0",closeMargin:"0 0 0 10px",closeSize:"20px",closeIconSize:"16px",iconSize:"20px",fontSize:"14px"};function A$(e){const{textColor2:t,closeIconColor:n,closeIconColorHover:r,closeIconColorPressed:o,infoColor:i,successColor:s,errorColor:l,warningColor:a,popoverColor:c,boxShadow2:u,primaryColor:d,lineHeight:f,borderRadius:m,closeColorHover:p,closeColorPressed:g}=e;return Object.assign(Object.assign({},k$),{closeBorderRadius:m,textColor:t,textColorInfo:t,textColorSuccess:t,textColorError:t,textColorWarning:t,textColorLoading:t,color:c,colorInfo:c,colorSuccess:c,colorError:c,colorWarning:c,colorLoading:c,boxShadow:u,boxShadowInfo:u,boxShadowSuccess:u,boxShadowError:u,boxShadowWarning:u,boxShadowLoading:u,iconColor:t,iconColorInfo:i,iconColorSuccess:s,iconColorWarning:a,iconColorError:l,iconColorLoading:d,closeColorHover:p,closeColorPressed:g,closeIconColor:n,closeIconColorHover:r,closeIconColorPressed:o,closeColorHoverInfo:p,closeColorPressedInfo:g,closeIconColorInfo:n,closeIconColorHoverInfo:r,closeIconColorPressedInfo:o,closeColorHoverSuccess:p,closeColorPressedSuccess:g,closeIconColorSuccess:n,closeIconColorHoverSuccess:r,closeIconColorPressedSuccess:o,closeColorHoverError:p,closeColorPressedError:g,closeIconColorError:n,closeIconColorHoverError:r,closeIconColorPressedError:o,closeColorHoverWarning:p,closeColorPressedWarning:g,closeIconColorWarning:n,closeIconColorHoverWarning:r,closeIconColorPressedWarning:o,closeColorHoverLoading:p,closeColorPressedLoading:g,closeIconColorLoading:n,closeIconColorHoverLoading:r,closeIconColorPressedLoading:o,loadingColor:d,lineHeight:f,borderRadius:m})}const I$={common:fn,self:A$},pv={icon:Function,type:{type:String,default:"info"},content:[String,Number,Function],showIcon:{type:Boolean,default:!0},closable:Boolean,keepAliveOnHover:Boolean,onClose:Function,onMouseenter:Function,onMouseleave:Function},R$=U([oe("message-wrapper",`
 margin: var(--n-margin);
 z-index: 0;
 transform-origin: top center;
 display: flex;
 `,[iT({overflow:"visible",originalTransition:"transform .3s var(--n-bezier)",enterToProps:{transform:"scale(1)"},leaveToProps:{transform:"scale(0.85)"}})]),oe("message",`
 box-sizing: border-box;
 display: flex;
 align-items: center;
 transition:
 color .3s var(--n-bezier),
 box-shadow .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 opacity .3s var(--n-bezier),
 transform .3s var(--n-bezier),
 margin-bottom .3s var(--n-bezier);
 padding: var(--n-padding);
 border-radius: var(--n-border-radius);
 flex-wrap: nowrap;
 overflow: hidden;
 max-width: var(--n-max-width);
 color: var(--n-text-color);
 background-color: var(--n-color);
 box-shadow: var(--n-box-shadow);
 `,[Z("content",`
 display: inline-block;
 line-height: var(--n-line-height);
 font-size: var(--n-font-size);
 `),Z("icon",`
 position: relative;
 margin: var(--n-icon-margin);
 height: var(--n-icon-size);
 width: var(--n-icon-size);
 font-size: var(--n-icon-size);
 flex-shrink: 0;
 `,[["default","info","success","warning","error","loading"].map(e=>X(`${e}-type`,[U("> *",`
 color: var(--n-icon-color-${e});
 transition: color .3s var(--n-bezier);
 `)])),U("> *",`
 position: absolute;
 left: 0;
 top: 0;
 right: 0;
 bottom: 0;
 `,[bs()])]),Z("close",`
 margin: var(--n-close-margin);
 transition:
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier);
 flex-shrink: 0;
 `,[U("&:hover",`
 color: var(--n-close-icon-color-hover);
 `),U("&:active",`
 color: var(--n-close-icon-color-pressed);
 `)])]),oe("message-container",`
 z-index: 6000;
 position: fixed;
 height: 0;
 overflow: visible;
 display: flex;
 flex-direction: column;
 align-items: center;
 `,[X("top",`
 top: 12px;
 left: 0;
 right: 0;
 `),X("top-left",`
 top: 12px;
 left: 12px;
 right: 0;
 align-items: flex-start;
 `),X("top-right",`
 top: 12px;
 left: 0;
 right: 12px;
 align-items: flex-end;
 `),X("bottom",`
 bottom: 4px;
 left: 0;
 right: 0;
 justify-content: flex-end;
 `),X("bottom-left",`
 bottom: 4px;
 left: 12px;
 right: 0;
 justify-content: flex-end;
 align-items: flex-start;
 `),X("bottom-right",`
 bottom: 4px;
 left: 0;
 right: 12px;
 justify-content: flex-end;
 align-items: flex-end;
 `)])]),L$={info:()=>T(ms,null),success:()=>T(pc,null),warning:()=>T(gc,null),error:()=>T(hc,null),default:()=>null},M$=ge({name:"Message",props:Object.assign(Object.assign({},pv),{render:Function}),setup(e){const{inlineThemeDisabled:t,mergedRtlRef:n}=Pt(e),{props:r,mergedClsPrefixRef:o}=Se(hv),i=Eo("Message",n,o),s=it("Message","-message",R$,I$,r,o),l=V(()=>{const{type:c}=e,{common:{cubicBezierEaseInOut:u},self:{padding:d,margin:f,maxWidth:m,iconMargin:p,closeMargin:g,closeSize:x,iconSize:h,fontSize:y,lineHeight:P,borderRadius:b,iconColorInfo:C,iconColorSuccess:S,iconColorWarning:v,iconColorError:E,iconColorLoading:A,closeIconSize:F,closeBorderRadius:B,[he("textColor",c)]:R,[he("boxShadow",c)]:j,[he("color",c)]:Q,[he("closeColorHover",c)]:H,[he("closeColorPressed",c)]:te,[he("closeIconColor",c)]:W,[he("closeIconColorPressed",c)]:re,[he("closeIconColorHover",c)]:ue}}=s.value;return{"--n-bezier":u,"--n-margin":f,"--n-padding":d,"--n-max-width":m,"--n-font-size":y,"--n-icon-margin":p,"--n-icon-size":h,"--n-close-icon-size":F,"--n-close-border-radius":B,"--n-close-size":x,"--n-close-margin":g,"--n-text-color":R,"--n-color":Q,"--n-box-shadow":j,"--n-icon-color-info":C,"--n-icon-color-success":S,"--n-icon-color-warning":v,"--n-icon-color-error":E,"--n-icon-color-loading":A,"--n-close-color-hover":H,"--n-close-color-pressed":te,"--n-close-icon-color":W,"--n-close-icon-color-pressed":re,"--n-close-icon-color-hover":ue,"--n-line-height":P,"--n-border-radius":b}}),a=t?dn("message",V(()=>e.type[0]),l,{}):void 0;return{mergedClsPrefix:o,rtlEnabled:i,messageProviderProps:r,handleClose(){var c;(c=e.onClose)===null||c===void 0||c.call(e)},cssVars:t?void 0:l,themeClass:a==null?void 0:a.themeClass,onRender:a==null?void 0:a.onRender,placement:r.placement}},render(){const{render:e,type:t,closable:n,content:r,mergedClsPrefix:o,cssVars:i,themeClass:s,onRender:l,icon:a,handleClose:c,showIcon:u}=this;l==null||l();let d;return T("div",{class:[`${o}-message-wrapper`,s],onMouseenter:this.onMouseenter,onMouseleave:this.onMouseleave,style:[{alignItems:this.placement.startsWith("top")?"flex-start":"flex-end"},i]},e?e(this.$props):T("div",{class:[`${o}-message ${o}-message--${t}-type`,this.rtlEnabled&&`${o}-message--rtl`]},(d=F$(a,t,o))&&u?T("div",{class:`${o}-message__icon ${o}-message__icon--${t}-type`},T(fc,null,{default:()=>d})):null,T("div",{class:`${o}-message__content`},kt(r)),n?T(Ks,{clsPrefix:o,class:`${o}-message__close`,onClick:c,absolute:!0}):null))}});function F$(e,t,n){if(typeof e=="function")return e();{const r=t==="loading"?T(zg,{clsPrefix:n,strokeWidth:24,scale:.85}):L$[t]();return r?T(Vs,{clsPrefix:n,key:t},{default:()=>r}):null}}const N$=ge({name:"MessageEnvironment",props:Object.assign(Object.assign({},pv),{duration:{type:Number,default:3e3},onAfterLeave:Function,onLeave:Function,internalKey:{type:String,required:!0},onInternalAfterLeave:Function,onHide:Function,onAfterHide:Function}),setup(e){let t=null;const n=D(!0);mt(()=>{r()});function r(){const{duration:u}=e;u&&(t=window.setTimeout(s,u))}function o(u){u.currentTarget===u.target&&t!==null&&(window.clearTimeout(t),t=null)}function i(u){u.currentTarget===u.target&&r()}function s(){const{onHide:u}=e;n.value=!1,t&&(window.clearTimeout(t),t=null),u&&u()}function l(){const{onClose:u}=e;u&&u(),s()}function a(){const{onAfterLeave:u,onInternalAfterLeave:d,onAfterHide:f,internalKey:m}=e;u&&u(),d&&d(m),f&&f()}function c(){s()}return{show:n,hide:s,handleClose:l,handleAfterLeave:a,handleMouseleave:i,handleMouseenter:o,deactivate:c}},render(){return T(Dg,{appear:!0,onAfterLeave:this.handleAfterLeave,onLeave:this.onLeave},{default:()=>[this.show?T(M$,{content:this.content,type:this.type,icon:this.icon,showIcon:this.showIcon,closable:this.closable,onClose:this.handleClose,onMouseenter:this.keepAliveOnHover?this.handleMouseenter:void 0,onMouseleave:this.keepAliveOnHover?this.handleMouseleave:void 0}):null]})}}),D$=Object.assign(Object.assign({},it.props),{to:[String,Object],duration:{type:Number,default:3e3},keepAliveOnHover:Boolean,max:Number,placement:{type:String,default:"top"},closable:Boolean,containerClass:String,containerStyle:[String,Object]}),z$=ge({name:"MessageProvider",props:D$,setup(e){const{mergedClsPrefixRef:t}=Pt(e),n=D([]),r=D({}),o={create(a,c){return i(a,Object.assign({type:"default"},c))},info(a,c){return i(a,Object.assign(Object.assign({},c),{type:"info"}))},success(a,c){return i(a,Object.assign(Object.assign({},c),{type:"success"}))},warning(a,c){return i(a,Object.assign(Object.assign({},c),{type:"warning"}))},error(a,c){return i(a,Object.assign(Object.assign({},c),{type:"error"}))},loading(a,c){return i(a,Object.assign(Object.assign({},c),{type:"loading"}))},destroyAll:l};ze(hv,{props:e,mergedClsPrefixRef:t}),ze(fv,o);function i(a,c){const u=fi(),d=yr(Object.assign(Object.assign({},c),{content:a,key:u,destroy:()=>{var m;(m=r.value[u])===null||m===void 0||m.hide()}})),{max:f}=e;return f&&n.value.length>=f&&n.value.shift(),n.value.push(d),d}function s(a){n.value.splice(n.value.findIndex(c=>c.key===a),1),delete r.value[a]}function l(){Object.values(r.value).forEach(a=>{a.hide()})}return Object.assign({mergedClsPrefix:t,messageRefs:r,messageList:n,handleAfterLeave:s},o)},render(){var e,t,n;return T(We,null,(t=(e=this.$slots).default)===null||t===void 0?void 0:t.call(e),this.messageList.length?T(Ts,{to:(n=this.to)!==null&&n!==void 0?n:"body"},T("div",{class:[`${this.mergedClsPrefix}-message-container`,`${this.mergedClsPrefix}-message-container--${this.placement}`,this.containerClass],key:"message-container",style:this.containerStyle},this.messageList.map(r=>T(N$,Object.assign({ref:o=>{o&&(this.messageRefs[r.key]=o)},internalKey:r.key,onInternalAfterLeave:this.handleAfterLeave},zs(r,["destroy"],void 0),{duration:r.duration===void 0?this.duration:r.duration,keepAliveOnHover:r.keepAliveOnHover===void 0?this.keepAliveOnHover:r.keepAliveOnHover,closable:r.closable===void 0?this.closable:r.closable}))))):null)}});function B$(){const e=Se(fv,null);return e===null&&Vr("use-message","No outer <n-message-provider /> founded. See prerequisite in https://www.naiveui.com/en-US/os-theme/components/message for more details. If you want to use `useMessage` outside setup, please check https://www.naiveui.com/zh-CN/os-theme/components/message#Q-&-A."),e}const H$=ge({name:"ModalEnvironment",props:Object.assign(Object.assign({},av),{internalKey:{type:String,required:!0},onInternalAfterLeave:{type:Function,required:!0}}),setup(e){const t=D(!0);function n(){const{onInternalAfterLeave:u,internalKey:d,onAfterLeave:f}=e;u&&u(d),f&&f()}function r(){const{onPositiveClick:u}=e;u?Promise.resolve(u()).then(d=>{d!==!1&&a()}):a()}function o(){const{onNegativeClick:u}=e;u?Promise.resolve(u()).then(d=>{d!==!1&&a()}):a()}function i(){const{onClose:u}=e;u?Promise.resolve(u()).then(d=>{d!==!1&&a()}):a()}function s(u){const{onMaskClick:d,maskClosable:f}=e;d&&(d(u),f&&a())}function l(){const{onEsc:u}=e;u&&u()}function a(){t.value=!1}function c(u){t.value=u}return{show:t,hide:a,handleUpdateShow:c,handleAfterLeave:n,handleCloseClick:i,handleNegativeClick:o,handlePositiveClick:r,handleMaskClick:s,handleEsc:l}},render(){const{handleUpdateShow:e,handleAfterLeave:t,handleMaskClick:n,handleEsc:r,show:o}=this;return T(cv,Object.assign({},this.$props,{show:o,onUpdateShow:e,onMaskClick:n,onEsc:r,onAfterLeave:t,internalAppear:!0,internalModal:!0}))}}),j$={to:[String,Object]},W$=ge({name:"ModalProvider",props:j$,setup(){const e=D([]),t={};function n(s={}){const l=fi(),a=yr(Object.assign(Object.assign({},s),{key:l,destroy:()=>{var c;(c=t[`n-modal-${l}`])===null||c===void 0||c.hide()}}));return e.value.push(a),a}function r(s){const{value:l}=e;l.splice(l.findIndex(a=>a.key===s),1)}function o(){Object.values(t).forEach(s=>{s==null||s.hide()})}const i={create:n,destroyAll:o};return ze(lv,i),ze(f$,{clickedRef:qa(64),clickedPositionRef:Xa()}),ze(h$,e),Object.assign(Object.assign({},i),{modalList:e,modalInstRefs:t,handleAfterLeave:r})},render(){var e,t;return T(We,null,[this.modalList.map(n=>{var r;return T(H$,zs(n,["destroy"],{to:(r=n.to)!==null&&r!==void 0?r:this.to,ref:o=>{o===null?delete this.modalInstRefs[`n-modal-${n.key}`]:this.modalInstRefs[`n-modal-${n.key}`]=o},internalKey:n.key,onInternalAfterLeave:this.handleAfterLeave}))}),(t=(e=this.$slots).default)===null||t===void 0?void 0:t.call(e)])}}),U$={closeMargin:"16px 12px",closeSize:"20px",closeIconSize:"16px",width:"365px",padding:"16px",titleFontSize:"16px",metaFontSize:"12px",descriptionFontSize:"12px"};function V$(e){const{textColor2:t,successColor:n,infoColor:r,warningColor:o,errorColor:i,popoverColor:s,closeIconColor:l,closeIconColorHover:a,closeIconColorPressed:c,closeColorHover:u,closeColorPressed:d,textColor1:f,textColor3:m,borderRadius:p,fontWeightStrong:g,boxShadow2:x,lineHeight:h,fontSize:y}=e;return Object.assign(Object.assign({},U$),{borderRadius:p,lineHeight:h,fontSize:y,headerFontWeight:g,iconColor:t,iconColorSuccess:n,iconColorInfo:r,iconColorWarning:o,iconColorError:i,color:s,textColor:t,closeIconColor:l,closeIconColorHover:a,closeIconColorPressed:c,closeBorderRadius:p,closeColorHover:u,closeColorPressed:d,headerTextColor:f,descriptionTextColor:m,actionTextColor:t,boxShadow:x})}const K$={name:"Notification",common:fn,peers:{Scrollbar:mc},self:V$},Xs="n-notification-provider",G$=ge({name:"NotificationContainer",props:{scrollable:{type:Boolean,required:!0},placement:{type:String,required:!0}},setup(){const{mergedThemeRef:e,mergedClsPrefixRef:t,wipTransitionCountRef:n}=Se(Xs),r=D(null);return Wr(()=>{var o,i;n.value>0?(o=r==null?void 0:r.value)===null||o===void 0||o.classList.add("transitioning"):(i=r==null?void 0:r.value)===null||i===void 0||i.classList.remove("transitioning")}),{selfRef:r,mergedTheme:e,mergedClsPrefix:t,transitioning:n}},render(){const{$slots:e,scrollable:t,mergedClsPrefix:n,mergedTheme:r,placement:o}=this;return T("div",{ref:"selfRef",class:[`${n}-notification-container`,t&&`${n}-notification-container--scrollable`,`${n}-notification-container--${o}`]},t?T(bc,{theme:r.peers.Scrollbar,themeOverrides:r.peerOverrides.Scrollbar,contentStyle:{overflow:"hidden"}},e):e)}}),Y$={info:()=>T(ms,null),success:()=>T(pc,null),warning:()=>T(gc,null),error:()=>T(hc,null),default:()=>null},Ec={closable:{type:Boolean,default:!0},type:{type:String,default:"default"},avatar:Function,title:[String,Function],description:[String,Function],content:[String,Function],meta:[String,Function],action:[String,Function],onClose:{type:Function,required:!0},keepAliveOnHover:Boolean,onMouseenter:Function,onMouseleave:Function},X$=Ds(Ec),q$=ge({name:"Notification",props:Ec,setup(e){const{mergedClsPrefixRef:t,mergedThemeRef:n,props:r}=Se(Xs),{inlineThemeDisabled:o,mergedRtlRef:i}=Pt(),s=Eo("Notification",i,t),l=V(()=>{const{type:c}=e,{self:{color:u,textColor:d,closeIconColor:f,closeIconColorHover:m,closeIconColorPressed:p,headerTextColor:g,descriptionTextColor:x,actionTextColor:h,borderRadius:y,headerFontWeight:P,boxShadow:b,lineHeight:C,fontSize:S,closeMargin:v,closeSize:E,width:A,padding:F,closeIconSize:B,closeBorderRadius:R,closeColorHover:j,closeColorPressed:Q,titleFontSize:H,metaFontSize:te,descriptionFontSize:W,[he("iconColor",c)]:re},common:{cubicBezierEaseOut:ue,cubicBezierEaseIn:pe,cubicBezierEaseInOut:we}}=n.value,{left:Te,right:Ue,top:st,bottom:gt}=lr(F);return{"--n-color":u,"--n-font-size":S,"--n-text-color":d,"--n-description-text-color":x,"--n-action-text-color":h,"--n-title-text-color":g,"--n-title-font-weight":P,"--n-bezier":we,"--n-bezier-ease-out":ue,"--n-bezier-ease-in":pe,"--n-border-radius":y,"--n-box-shadow":b,"--n-close-border-radius":R,"--n-close-color-hover":j,"--n-close-color-pressed":Q,"--n-close-icon-color":f,"--n-close-icon-color-hover":m,"--n-close-icon-color-pressed":p,"--n-line-height":C,"--n-icon-color":re,"--n-close-margin":v,"--n-close-size":E,"--n-close-icon-size":B,"--n-width":A,"--n-padding-left":Te,"--n-padding-right":Ue,"--n-padding-top":st,"--n-padding-bottom":gt,"--n-title-font-size":H,"--n-meta-font-size":te,"--n-description-font-size":W}}),a=o?dn("notification",V(()=>e.type[0]),l,r):void 0;return{mergedClsPrefix:t,showAvatar:V(()=>e.avatar||e.type!=="default"),handleCloseClick(){e.onClose()},rtlEnabled:s,cssVars:o?void 0:l,themeClass:a==null?void 0:a.themeClass,onRender:a==null?void 0:a.onRender}},render(){var e;const{mergedClsPrefix:t}=this;return(e=this.onRender)===null||e===void 0||e.call(this),T("div",{class:[`${t}-notification-wrapper`,this.themeClass],onMouseenter:this.onMouseenter,onMouseleave:this.onMouseleave,style:this.cssVars},T("div",{class:[`${t}-notification`,this.rtlEnabled&&`${t}-notification--rtl`,this.themeClass,{[`${t}-notification--closable`]:this.closable,[`${t}-notification--show-avatar`]:this.showAvatar}],style:this.cssVars},this.showAvatar?T("div",{class:`${t}-notification__avatar`},this.avatar?kt(this.avatar):this.type!=="default"?T(Vs,{clsPrefix:t},{default:()=>Y$[this.type]()}):null):null,this.closable?T(Ks,{clsPrefix:t,class:`${t}-notification__close`,onClick:this.handleCloseClick}):null,T("div",{ref:"bodyRef",class:`${t}-notification-main`},this.title?T("div",{class:`${t}-notification-main__header`},kt(this.title)):null,this.description?T("div",{class:`${t}-notification-main__description`},kt(this.description)):null,this.content?T("pre",{class:`${t}-notification-main__content`},kt(this.content)):null,this.meta||this.action?T("div",{class:`${t}-notification-main-footer`},this.meta?T("div",{class:`${t}-notification-main-footer__meta`},kt(this.meta)):null,this.action?T("div",{class:`${t}-notification-main-footer__action`},kt(this.action)):null):null)))}}),Z$=Object.assign(Object.assign({},Ec),{duration:Number,onClose:Function,onLeave:Function,onAfterEnter:Function,onAfterLeave:Function,onHide:Function,onAfterShow:Function,onAfterHide:Function}),J$=ge({name:"NotificationEnvironment",props:Object.assign(Object.assign({},Z$),{internalKey:{type:String,required:!0},onInternalAfterLeave:{type:Function,required:!0}}),setup(e){const{wipTransitionCountRef:t}=Se(Xs),n=D(!0);let r=null;function o(){n.value=!1,r&&window.clearTimeout(r)}function i(p){t.value++,qt(()=>{p.style.height=`${p.offsetHeight}px`,p.style.maxHeight="0",p.style.transition="none",p.offsetHeight,p.style.transition="",p.style.maxHeight=p.style.height})}function s(p){t.value--,p.style.height="",p.style.maxHeight="";const{onAfterEnter:g,onAfterShow:x}=e;g&&g(),x&&x()}function l(p){t.value++,p.style.maxHeight=`${p.offsetHeight}px`,p.style.height=`${p.offsetHeight}px`,p.offsetHeight}function a(p){const{onHide:g}=e;g&&g(),p.style.maxHeight="0",p.offsetHeight}function c(){t.value--;const{onAfterLeave:p,onInternalAfterLeave:g,onAfterHide:x,internalKey:h}=e;p&&p(),g(h),x&&x()}function u(){const{duration:p}=e;p&&(r=window.setTimeout(o,p))}function d(p){p.currentTarget===p.target&&r!==null&&(window.clearTimeout(r),r=null)}function f(p){p.currentTarget===p.target&&u()}function m(){const{onClose:p}=e;p?Promise.resolve(p()).then(g=>{g!==!1&&o()}):o()}return mt(()=>{e.duration&&(r=window.setTimeout(o,e.duration))}),{show:n,hide:o,handleClose:m,handleAfterLeave:c,handleLeave:a,handleBeforeLeave:l,handleAfterEnter:s,handleBeforeEnter:i,handleMouseenter:d,handleMouseleave:f}},render(){return T(mn,{name:"notification-transition",appear:!0,onBeforeEnter:this.handleBeforeEnter,onAfterEnter:this.handleAfterEnter,onBeforeLeave:this.handleBeforeLeave,onLeave:this.handleLeave,onAfterLeave:this.handleAfterLeave},{default:()=>this.show?T(q$,Object.assign({},Vn(this.$props,X$),{onClose:this.handleClose,onMouseenter:this.duration&&this.keepAliveOnHover?this.handleMouseenter:void 0,onMouseleave:this.duration&&this.keepAliveOnHover?this.handleMouseleave:void 0})):null})}}),Q$=U([oe("notification-container",`
 z-index: 4000;
 position: fixed;
 overflow: visible;
 display: flex;
 flex-direction: column;
 align-items: flex-end;
 `,[U(">",[oe("scrollbar",`
 width: initial;
 overflow: visible;
 height: -moz-fit-content !important;
 height: fit-content !important;
 max-height: 100vh !important;
 `,[U(">",[oe("scrollbar-container",`
 height: -moz-fit-content !important;
 height: fit-content !important;
 max-height: 100vh !important;
 `,[oe("scrollbar-content",`
 padding-top: 12px;
 padding-bottom: 33px;
 `)])])])]),X("top, top-right, top-left",`
 top: 12px;
 `,[U("&.transitioning >",[oe("scrollbar",[U(">",[oe("scrollbar-container",`
 min-height: 100vh !important;
 `)])])])]),X("bottom, bottom-right, bottom-left",`
 bottom: 12px;
 `,[U(">",[oe("scrollbar",[U(">",[oe("scrollbar-container",[oe("scrollbar-content",`
 padding-bottom: 12px;
 `)])])])]),oe("notification-wrapper",`
 display: flex;
 align-items: flex-end;
 margin-bottom: 0;
 margin-top: 12px;
 `)]),X("top, bottom",`
 left: 50%;
 transform: translateX(-50%);
 `,[oe("notification-wrapper",[U("&.notification-transition-enter-from, &.notification-transition-leave-to",`
 transform: scale(0.85);
 `),U("&.notification-transition-leave-from, &.notification-transition-enter-to",`
 transform: scale(1);
 `)])]),X("top",[oe("notification-wrapper",`
 transform-origin: top center;
 `)]),X("bottom",[oe("notification-wrapper",`
 transform-origin: bottom center;
 `)]),X("top-right, bottom-right",[oe("notification",`
 margin-left: 28px;
 margin-right: 16px;
 `)]),X("top-left, bottom-left",[oe("notification",`
 margin-left: 16px;
 margin-right: 28px;
 `)]),X("top-right",`
 right: 0;
 `,[Wi("top-right")]),X("top-left",`
 left: 0;
 `,[Wi("top-left")]),X("bottom-right",`
 right: 0;
 `,[Wi("bottom-right")]),X("bottom-left",`
 left: 0;
 `,[Wi("bottom-left")]),X("scrollable",[X("top-right",`
 top: 0;
 `),X("top-left",`
 top: 0;
 `),X("bottom-right",`
 bottom: 0;
 `),X("bottom-left",`
 bottom: 0;
 `)]),oe("notification-wrapper",`
 margin-bottom: 12px;
 `,[U("&.notification-transition-enter-from, &.notification-transition-leave-to",`
 opacity: 0;
 margin-top: 0 !important;
 margin-bottom: 0 !important;
 `),U("&.notification-transition-leave-from, &.notification-transition-enter-to",`
 opacity: 1;
 `),U("&.notification-transition-leave-active",`
 transition:
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier),
 opacity .3s var(--n-bezier),
 transform .3s var(--n-bezier-ease-in),
 max-height .3s var(--n-bezier),
 margin-top .3s linear,
 margin-bottom .3s linear,
 box-shadow .3s var(--n-bezier);
 `),U("&.notification-transition-enter-active",`
 transition:
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier),
 opacity .3s var(--n-bezier),
 transform .3s var(--n-bezier-ease-out),
 max-height .3s var(--n-bezier),
 margin-top .3s linear,
 margin-bottom .3s linear,
 box-shadow .3s var(--n-bezier);
 `)]),oe("notification",`
 background-color: var(--n-color);
 color: var(--n-text-color);
 transition:
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier),
 opacity .3s var(--n-bezier),
 box-shadow .3s var(--n-bezier);
 font-family: inherit;
 font-size: var(--n-font-size);
 font-weight: 400;
 position: relative;
 display: flex;
 overflow: hidden;
 flex-shrink: 0;
 padding-left: var(--n-padding-left);
 padding-right: var(--n-padding-right);
 width: var(--n-width);
 max-width: calc(100vw - 16px - 16px);
 border-radius: var(--n-border-radius);
 box-shadow: var(--n-box-shadow);
 box-sizing: border-box;
 opacity: 1;
 `,[Z("avatar",[oe("icon",`
 color: var(--n-icon-color);
 `),oe("base-icon",`
 color: var(--n-icon-color);
 `)]),X("show-avatar",[oe("notification-main",`
 margin-left: 40px;
 width: calc(100% - 40px); 
 `)]),X("closable",[oe("notification-main",[U("> *:first-child",`
 padding-right: 20px;
 `)]),Z("close",`
 position: absolute;
 top: 0;
 right: 0;
 margin: var(--n-close-margin);
 transition:
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier);
 `)]),Z("avatar",`
 position: absolute;
 top: var(--n-padding-top);
 left: var(--n-padding-left);
 width: 28px;
 height: 28px;
 font-size: 28px;
 display: flex;
 align-items: center;
 justify-content: center;
 `,[oe("icon","transition: color .3s var(--n-bezier);")]),oe("notification-main",`
 padding-top: var(--n-padding-top);
 padding-bottom: var(--n-padding-bottom);
 box-sizing: border-box;
 display: flex;
 flex-direction: column;
 margin-left: 8px;
 width: calc(100% - 8px);
 `,[oe("notification-main-footer",`
 display: flex;
 align-items: center;
 justify-content: space-between;
 margin-top: 12px;
 `,[Z("meta",`
 font-size: var(--n-meta-font-size);
 transition: color .3s var(--n-bezier-ease-out);
 color: var(--n-description-text-color);
 `),Z("action",`
 cursor: pointer;
 transition: color .3s var(--n-bezier-ease-out);
 color: var(--n-action-text-color);
 `)]),Z("header",`
 font-weight: var(--n-title-font-weight);
 font-size: var(--n-title-font-size);
 transition: color .3s var(--n-bezier-ease-out);
 color: var(--n-title-text-color);
 `),Z("description",`
 margin-top: 8px;
 font-size: var(--n-description-font-size);
 white-space: pre-wrap;
 word-wrap: break-word;
 transition: color .3s var(--n-bezier-ease-out);
 color: var(--n-description-text-color);
 `),Z("content",`
 line-height: var(--n-line-height);
 margin: 12px 0 0 0;
 font-family: inherit;
 white-space: pre-wrap;
 word-wrap: break-word;
 transition: color .3s var(--n-bezier-ease-out);
 color: var(--n-text-color);
 `,[U("&:first-child","margin: 0;")])])])])]);function Wi(e){const n=e.split("-")[1]==="left"?"calc(-100%)":"calc(100%)";return oe("notification-wrapper",[U("&.notification-transition-enter-from, &.notification-transition-leave-to",`
 transform: translate(${n}, 0);
 `),U("&.notification-transition-leave-from, &.notification-transition-enter-to",`
 transform: translate(0, 0);
 `)])}const gv="n-notification-api",eP=Object.assign(Object.assign({},it.props),{containerClass:String,containerStyle:[String,Object],to:[String,Object],scrollable:{type:Boolean,default:!0},max:Number,placement:{type:String,default:"top-right"},keepAliveOnHover:Boolean}),tP=ge({name:"NotificationProvider",props:eP,setup(e){const{mergedClsPrefixRef:t}=Pt(e),n=D([]),r={},o=new Set;function i(m){const p=fi(),g=()=>{o.add(p),r[p]&&r[p].hide()},x=yr(Object.assign(Object.assign({},m),{key:p,destroy:g,hide:g,deactivate:g})),{max:h}=e;if(h&&n.value.length-o.size>=h){let y=!1,P=0;for(const b of n.value){if(!o.has(b.key)){r[b.key]&&(b.destroy(),y=!0);break}P++}y||n.value.splice(P,1)}return n.value.push(x),x}const s=["info","success","warning","error"].map(m=>p=>i(Object.assign(Object.assign({},p),{type:m})));function l(m){o.delete(m),n.value.splice(n.value.findIndex(p=>p.key===m),1)}const a=it("Notification","-notification",Q$,K$,e,t),c={create:i,info:s[0],success:s[1],warning:s[2],error:s[3],open:d,destroyAll:f},u=D(0);ze(gv,c),ze(Xs,{props:e,mergedClsPrefixRef:t,mergedThemeRef:a,wipTransitionCountRef:u});function d(m){return i(m)}function f(){Object.values(n.value).forEach(m=>{m.hide()})}return Object.assign({mergedClsPrefix:t,notificationList:n,notificationRefs:r,handleAfterLeave:l},c)},render(){var e,t,n;const{placement:r}=this;return T(We,null,(t=(e=this.$slots).default)===null||t===void 0?void 0:t.call(e),this.notificationList.length?T(Ts,{to:(n=this.to)!==null&&n!==void 0?n:"body"},T(G$,{class:this.containerClass,style:this.containerStyle,scrollable:this.scrollable&&r!=="top"&&r!=="bottom",placement:r},{default:()=>this.notificationList.map(o=>T(J$,Object.assign({ref:i=>{const s=o.key;i===null?delete this.notificationRefs[s]:this.notificationRefs[s]=i}},zs(o,["destroy","hide","deactivate"]),{internalKey:o.key,onInternalAfterLeave:this.handleAfterLeave,keepAliveOnHover:o.keepAliveOnHover===void 0?this.keepAliveOnHover:o.keepAliveOnHover})))})):null)}});function nP(){const e=Se(gv,null);return e===null&&Vr("use-notification","No outer `n-notification-provider` found."),e}const rP=ge({name:"InjectionExtractor",props:{onSetup:Function},setup(e,{slots:t}){var n;return(n=e.onSetup)===null||n===void 0||n.call(e),()=>{var r;return(r=t.default)===null||r===void 0?void 0:r.call(t)}}}),oP={message:B$,notification:nP,loadingBar:O$,dialog:i$,modal:p$};function iP({providersAndProps:e,configProviderProps:t}){let n=Uh(o);const r={app:n};function o(){return T(BT,Ce(t),{default:()=>e.map(({type:l,Provider:a,props:c})=>T(a,Ce(c),{default:()=>T(rP,{onSetup:()=>r[l]=oP[l]()})}))})}let i;return Co&&(i=document.createElement("div"),document.body.appendChild(i),n.mount(i)),Object.assign({unmount:()=>{var l;if(n===null||i===null){hr("discrete","unmount call no need because discrete app has been unmounted");return}n.unmount(),(l=i.parentNode)===null||l===void 0||l.removeChild(i),i=null,n=null}},r)}function sP(e,{configProviderProps:t,messageProviderProps:n,dialogProviderProps:r,notificationProviderProps:o,loadingBarProviderProps:i,modalProviderProps:s}={}){const l=[];return e.forEach(c=>{switch(c){case"message":l.push({type:c,Provider:z$,props:n});break;case"notification":l.push({type:c,Provider:tP,props:o});break;case"dialog":l.push({type:c,Provider:_$,props:r});break;case"loadingBar":l.push({type:c,Provider:P$,props:i});break;case"modal":l.push({type:c,Provider:W$,props:s})}}),iP({providersAndProps:l,configProviderProps:t})}const qs=(e,t)=>{const n=e.__vccOpts||e;for(const[r,o]of t)n[r]=o;return n},lP=["src","scrolling"],aP=720*3,cP={__name:"AutoHeightIframe",props:{htmlSrc:{type:String,required:!0,default:""},minHeight:{type:Number,default:720},width:{type:Number,required:!1,default:1280},isFinished:{type:Boolean,default:!1},scrollTimeout:{type:Number,default:3e3},isSlideEditMode:{type:Boolean,default:!1}},emits:["height-updated"],setup(e,{emit:t}){const n=e,r=t,o=D(null),i=D(n.minHeight),s=D(!1),l=D(!1);let a=null;const c=D(1),u=D(null),d=e2(()=>{if(!u.value||!u.value.offsetWidth)return;console.log("onResize",u.value.offsetWidth);const p=u.value.offsetWidth;c.value=p/n.width,console.log("transformScale",c.value)},100);Ye(()=>u.value,d,{immediate:!0}),mt(()=>{window.addEventListener("resize",d)}),zr(()=>{window.removeEventListener("resize",d)});const f=()=>{s.value=!1,l.value=!1,a&&clearTimeout(a),a=setTimeout(()=>{s.value||(l.value=!0)},n.scrollTimeout)},m=p=>{try{if(p.data&&p.data.type==="resize"&&p.data.url===n.htmlSrc){const g=parseInt(p.data.height);i.value=Math.min(Math.max(g,i.value),aP),s.value=!0,l.value=!1,r("height-updated",g)}}catch(g){console.error("Error processing iframe resize message:",g)}};return mt(()=>{r("height-updated",i.value),window.addEventListener("message",m)}),zr(()=>{window.removeEventListener("message",m),a&&clearTimeout(a)}),(p,g)=>(ye(),Ae("div",{class:"html-content-viewer",ref_key:"htmlContentViewerRef",ref:u,style:Tt({height:i.value*c.value-2+"px"})},[ae("iframe",{ref_key:"iframeRef",ref:o,src:e.htmlSrc,frameborder:"0",scrolling:l.value?"yes":"no",onLoad:f,referrerpolicy:"origin",style:Tt({width:n.width?n.width+"px":"1280px",transform:`scale(${c.value})`,transformOrigin:"top left",height:i.value+"px",overflow:l.value?"auto":"hidden",display:"block",border:"none",position:"absolute"}),onMouseleave:g[0]||(g[0]=(...x)=>p.onMouseleave&&p.onMouseleave(...x))},null,44,lP)],4))}},bf=qs(cP,[["__scopeId","data-v-acf7cf76"]]),uP={xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",fill:"none",class:"slide-icon",viewBox:"0 0 16 16"};function dP(e,t){return ye(),Ae("svg",uP,t[0]||(t[0]=[ae("path",{stroke:"currentColor",d:"M3 4.517a1 1 0 0 1 .717-.96l4-1.179A1 1 0 0 1 9 3.338v9.325a1 1 0 0 1-1.283.959l-4-1.18A1 1 0 0 1 3 11.483z"},null,-1),ae("path",{stroke:"currentColor",d:"m9 4.399 2.67-.934A1 1 0 0 1 13 4.41v7.182a1 1 0 0 1-1.33.944L9 11.6"},null,-1)]))}const yf={render:dP},fP={xmlns:"http://www.w3.org/2000/svg",width:"20",height:"21",fill:"none"};function hP(e,t){return ye(),Ae("svg",fP,t[0]||(t[0]=[ae("path",{stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M16.667 11.33V9.377c0-.681 0-1.022-.127-1.328-.126-.307-.367-.547-.849-1.03l-3.947-3.946c-.416-.416-.624-.624-.881-.747a2 2 0 0 0-.165-.068c-.269-.095-.563-.095-1.15-.095-2.705 0-4.057 0-4.973.738q-.278.225-.503.503c-.738.916-.738 2.268-.738 4.972v2.954m7.5-8.75v.416c0 2.357 0 3.536.732 4.268s1.911.732 4.268.732h.417"},null,-1),ae("path",{stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.25",d:"M16.458 13.837h-2.084a.833.833 0 0 0-.833.834v1.666m0 0v2.5m0-2.5h2.5m-12.5 2.5v-2.083m0 0v-2.917h1.458a1.458 1.458 0 1 1 0 2.917zm5-2.917h1.25c.92 0 1.667.746 1.667 1.667v1.667c0 .92-.747 1.666-1.667 1.666h-1.25z"},null,-1)]))}const pP={render:hP},gP={xmlns:"http://www.w3.org/2000/svg",width:"20",height:"22",fill:"none"};function vP(e,t){return ye(),Ae("svg",gP,t[0]||(t[0]=[ae("path",{stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.25",d:"M1.4 19.34v-2.083m0 0V14.34h1.458a1.458 1.458 0 1 1 0 2.917zM6.4 19.34v-2.083m0 0V14.34h1.458a1.458 1.458 0 1 1 0 2.917zM12.2 19.34v-5m-1.5 0h3M15.4 19.34l3-5m0 5-3-5"},null,-1),ae("path",{stroke:"currentColor","stroke-linecap":"round","stroke-width":"1.5",d:"M3 12V5.052A1.4 1.4 0 0 1 4.004 3.71l5.6-1.651A1.4 1.4 0 0 1 11.4 3.401V12"},null,-1),ae("path",{fill:"currentColor",d:"m15.138 3.58.248.708zM16.25 12a.75.75 0 0 0 1.5 0zM11.4 4.887l.248.708 3.738-1.307-.248-.708-.247-.708-3.738 1.307zm3.738-1.307.248.708a.65.65 0 0 1 .864.613h1.5a2.15 2.15 0 0 0-2.86-2.03zM17 4.9h-.75V12h1.5V4.901z"},null,-1)]))}const mP={render:vP},bP={xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"none"};function yP(e,t){return ye(),Ae("svg",bP,t[0]||(t[0]=[ae("path",{stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M3 10.765v4.588c0 .437.184.856.513 1.165.328.308.773.482 1.237.482h10.5c.464 0 .91-.174 1.237-.482A1.6 1.6 0 0 0 17 15.353v-4.588m-3.5-.353L10 13.706m0 0-3.5-3.294m3.5 3.294V3"},null,-1)]))}const xP={render:yP},wP=()=>window&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches,_P=()=>{const e=navigator.userAgent;return/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(e)},CP=()=>{const e=navigator.userAgent;return e?/genspark_android|genspark_ios/i.test(e):!1},SP=()=>{const e=navigator.userAgent;return e?e.includes("genspark_ios"):!1},EP=()=>{const e=navigator.userAgent;if(e)return e.includes("genspark_android")},Gt={isDarkMode:wP,isMobile:_P,isGensparkApp:CP,isGensparkAppIos:SP,isGensparkAppAndroid:EP};async function TP(e,t,n,r){try{const o=await fetch(e,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t),signal:r});if(!o.ok)throw new Error(`HTTP error! status: ${o.status} ${o.statusText} ${e}`);const i=o.body.pipeThrough(new TextDecoderStream).getReader();let s="";for(;;){const{value:l,done:a}=await i.read();for(l!=null&&(s+=l);s.indexOf(`

`)!==-1;){let c=s.substring(0,s.indexOf(`

`));s=s.substring(s.indexOf(`

`)+2);let u=c.substring(c.indexOf("{"),c.length),d=JSON.parse(u);try{n(d)}catch(f){console.error(`processLineCallback error ${f}, ${d}`)}}if(a)break}}catch(o){throw console.error(`fetch url error ${e}`),o}}const $P={key:0,class:"pdf-exporting-overlay"},PP={key:0,class:"progress-bar-container"},OP={__name:"PdfExport",props:{showLoading:{type:Boolean,default:!1}},emits:["export-start","export-complete","export-error","export-progress"],setup(e,{expose:t,emit:n}){const r=n,o=D(!1),i=D(null),s=D(0),l=D(""),a=Se("jsBridge"),c=V(()=>s.value>0?`${s.value}% - ${l.value}`:l.value),u=D(!1);Ye(()=>a.value,p=>{p&&a.value.callHandler("support",{api:"download"},g=>{u.value=g})},{immediate:!0});const d=async(p,g)=>{let x={success:!1,pdfUrl:null,error:null},h=new AbortController;try{await TP(p,g,y=>{y.status==="processing"?(s.value=s.value+1,l.value=y.message||"",r("export-progress",s.value,l.value)):y.status==="completed"?(s.value=100,l.value=y.message||"",x={success:!0,pdfUrl:y.pdf_url,error:null}):y.status==="error"&&(x={success:!1,pdfUrl:null,error:y.message||"Unknown error"})},h.signal)}catch(y){console.error("Error reading SSE stream:",y),x.error="Error reading response stream"}return x};return t({exportToPDF:async(p,g="download.pdf",x=null,h=null)=>{if(o.value)return!1;o.value=!0,i.value=null,s.value=0,l.value="Preparing document",r("export-start");try{const y=document.createElement("iframe");y.style.position="absolute",y.style.left="-9999px",y.style.width=`${Math.min(window.innerWidth,1280)}px`,y.style.height="800px",y.style.border="none",y.srcdoc=p,document.body.appendChild(y),await new Promise(B=>{y.onload=B});let P;try{P=y.contentDocument||y.contentWindow.document}catch(B){throw console.error("Unable to access iframe content:",B),document.body.removeChild(y),new Error("Unable to access iframe content")}P.readyState!=="complete"&&await new Promise(B=>{const R=()=>{P.readyState==="complete"?B():setTimeout(R,100)};R()}),await new Promise(B=>{const R=P.getElementsByTagName("img");let j=0;const Q=R.length;if(Q===0){B();return}for(const H of R)H.complete?(j++,j===Q&&B()):(H.onload=()=>{j++,j===Q&&B()},H.onerror=()=>{j++,j===Q&&B()})});const b=P.body;if(!b)throw console.error("Unable to get the body element of the iframe"),document.body.removeChild(y),new Error("Unable to get the body element of the iframe");const S=Math.max(b.scrollHeight,b.offsetHeight,b.clientHeight);if(document.body.removeChild(y),!x||!h)throw new Error("Missing required parameters: projectId or toolCallId");const v=g.endsWith(".pdf")?g.substring(0,g.length-4):g,E={type:"webpage",project_id:x,file_id:h,params:{width:window.innerWidth,height:S+50,filename:v}},A=await d("/api/project/generate_pdf",E);if(!A.success)throw new Error(A.error||"Error generating PDF from API");const F=A.pdfUrl;if(Gt.isGensparkApp()&&a.value&&u.value)a.value.callHandler("download",{url:F,name:g});else{const B=document.createElement("a");B.href=F,B.download=g,document.body.appendChild(B),B.click(),document.body.removeChild(B)}return!0}catch(y){return console.error("Error generating PDF:",y),i.value=y.message||"Error generating PDF",r("export-error",i.value),!1}finally{o.value=!1,r("export-complete",!i.value)}},exportSlidesToPDF:async(p,g,x="slides.pdf",h=1280,y=[],P="pdf")=>{if(o.value)return!1;o.value=!0,i.value=null,s.value=0,l.value="Preparing slides",r("export-start");try{const C=await d("/api/project/generate_pdf",{type:"slides",project_id:p,file_id:g,params:{width:h,slideHeights:y,filename:x,format:P}});if(!C.success)throw new Error(C.error||"Error exporting slides to PDF");const S=C.pdfUrl;if(Gt.isGensparkApp()&&a.value&&u.value)a.value.callHandler("download",{url:S,name:x});else{const v=document.createElement("a");v.href=S,v.download=x,document.body.appendChild(v),v.click(),document.body.removeChild(v)}return!0}catch(b){return console.error("Error generating PDF:",b),i.value=b.message||"Error exporting slides to PDF",r("export-error",i.value),!1}finally{o.value=!1,r("export-complete",!i.value)}},isExporting:o}),(p,g)=>(ye(),Ae("div",null,[e.showLoading&&o.value?(ye(),Ae("div",$P,[g[0]||(g[0]=ae("div",{class:"pdf-exporting-spinner"},null,-1)),ae("p",null,Ve(p.t("components.pdfexport.exporting"))+" "+Ve(c.value),1),s.value>0?(ye(),Ae("div",PP,[ae("div",{class:"progress-bar",style:Tt({width:`${s.value}%`})},null,4)])):Kt("",!0)])):Kt("",!0)]))}},kP=qs(OP,[["__scopeId","data-v-90e5e7a5"]]),AP={xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"none"};function IP(e,t){return ye(),Ae("svg",AP,t[0]||(t[0]=[Rh('<g stroke-linecap="round" stroke-linejoin="round" stroke-width="2" clip-path="url(#a)"><path stroke="url(#b)" d="M9.997 1.668a8.333 8.333 0 1 0 8.334 8.333"></path><path stroke="url(#c)" d="M9.997 1.668a8.333 8.333 0 1 0 8.334 8.333"></path></g><defs><linearGradient id="b" x1="13.967" x2=".954" y1="3.495" y2="8.976" gradientUnits="userSpaceOnUse"><stop stop-color="#666" stop-opacity=".01"></stop><stop offset="1" stop-color="#666"></stop></linearGradient><linearGradient id="c" x1="7.73" x2="14.36" y1="2.842" y2="7.377" gradientUnits="userSpaceOnUse"><stop stop-color="#666"></stop><stop offset="1" stop-color="#666" stop-opacity=".01"></stop></linearGradient><clipPath id="a"><path fill="#fff" d="M0 0h20v20H0z"></path></clipPath></defs>',2)]))}const RP={render:IP},LP={xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",fill:"none"};function MP(e,t){return ye(),Ae("svg",LP,t[0]||(t[0]=[ae("path",{stroke:"currentColor","stroke-linejoin":"round","stroke-width":"1.8",d:"M7 12V6.741a1 1 0 0 1 1.504-.864L13 8.5l4.52 2.636a1 1 0 0 1 0 1.728L13 15.5l-4.496 2.623A1 1 0 0 1 7 17.259z"},null,-1)]))}const FP={render:MP},NP=["src","scrolling"],DP=720*3,zP={__name:"AutoHeightIframeScreen",props:{htmlSrc:{type:String,required:!0,default:""},minHeight:{type:Number,default:720},width:{type:Number,required:!1,default:1280},isFinished:{type:Boolean,default:!1},scrollTimeout:{type:Number,default:3e3},isSlideEditMode:{type:Boolean,default:!1}},emits:["height-updated"],setup(e,{emit:t}){const n=e,r=t,o=D(null),i=D(n.minHeight),s=D(!1),l=D(!1);let a=null;const c=D(1),u=D(null),d=()=>{s.value=!1,l.value=!1,a&&clearTimeout(a),a=setTimeout(()=>{s.value||(l.value=!0)},n.scrollTimeout)},f=m=>{try{if(m.data&&m.data.type==="resize"&&m.data.url===n.htmlSrc){const p=parseInt(m.data.height);i.value=Math.min(Math.max(p,i.value),DP),s.value=!0,l.value=!1;const g=Math.max(document.documentElement.clientHeight,document.body.clientHeight),x=Math.max(document.documentElement.clientWidth,document.body.clientWidth);if(n.width>=i.value){const h=x/n.width,y=i.value*h;let P=h;y>g&&(P=h*g/y),c.value=Math.min(h,P)}else c.value=g/i.value;r("height-updated",i.value)}}catch(p){console.error("Error processing iframe resize message:",p)}};return mt(()=>{r("height-updated",i.value),window.addEventListener("message",f)}),zr(()=>{window.removeEventListener("message",f),a&&clearTimeout(a)}),(m,p)=>(ye(),Ae("div",{class:"html-content-viewer",ref_key:"htmlContentViewerRef",ref:u,style:Tt({height:i.value+"px"})},[ae("iframe",{ref_key:"iframeRef",ref:o,src:n.htmlSrc,frameborder:"0",scrolling:l.value?"yes":"no",onLoad:d,referrerpolicy:"origin",class:"iframe",style:Tt({width:n.width?n.width+"px":"1280px",transform:`scale(${c.value})`,transformOrigin:"center center",height:i.value+"px",overflow:l.value?"auto":"hidden",display:"block",border:"none",position:"absolute"})},null,44,NP)],4))}},BP=qs(zP,[["__scopeId","data-v-c78466df"]]),HP={xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"none"};function jP(e,t){return ye(),Ae("svg",HP,t[0]||(t[0]=[ae("g",{"clip-path":"url(#a)"},[ae("path",{stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"m7.07 17.14 7.072-7.07L7.07 2.998"})],-1),ae("defs",null,[ae("clipPath",{id:"a"},[ae("path",{fill:"#fff",d:"M20 0H0v20h20z"})])],-1)]))}const xf={render:jP},WP={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"};function UP(e,t){return ye(),Ae("svg",WP,t[0]||(t[0]=[ae("path",{fill:"currentColor",d:"M7.75 12a1.75 1.75 0 1 1-3.5 0 1.75 1.75 0 0 1 3.5 0m6 0a1.75 1.75 0 1 1-3.5 0 1.75 1.75 0 0 1 3.5 0M18 13.75a1.75 1.75 0 1 0 0-3.5 1.75 1.75 0 0 0 0 3.5"},null,-1)]))}const VP={render:UP},KP={xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",fill:"none"};function GP(e,t){return ye(),Ae("svg",KP,t[0]||(t[0]=[ae("path",{stroke:"currentColor","stroke-width":"1.5",d:"M12 4.322a7.67 7.67 0 0 1 7.671 7.669A7.67 7.67 0 0 1 12 19.659a7.67 7.67 0 0 1-7.672-7.668A7.67 7.67 0 0 1 12 4.32Z"},null,-1),ae("path",{stroke:"currentColor","stroke-width":"1.5",d:"M12 4.315c.66 0 1.498.557 2.226 2.012.7 1.401 1.16 3.4 1.16 5.657s-.46 4.255-1.16 5.656c-.728 1.455-1.567 2.012-2.226 2.012s-1.498-.557-2.226-2.012c-.7-1.401-1.16-3.4-1.16-5.656 0-2.257.46-4.256 1.16-5.657.728-1.455 1.567-2.012 2.226-2.012Z"},null,-1),ae("path",{stroke:"currentColor","stroke-linecap":"round","stroke-width":"1.5",d:"M5.5 9.155H19M5.5 15.155h13"},null,-1)]))}const YP={render:GP},XP={xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"none"};function qP(e,t){return ye(),Ae("svg",XP,t[0]||(t[0]=[ae("path",{fill:"currentColor","fill-rule":"evenodd",d:"M4.6 2.744a2.75 2.75 0 0 0-2.75 2.75v5.83a.75.75 0 0 0 1.5 0v-5.83c0-.69.56-1.25 1.25-1.25h5.417a.75.75 0 0 0 0-1.5zm2.472 2.473a2.75 2.75 0 0 0-2.75 2.75v7.538a2.75 2.75 0 0 0 2.75 2.75h7.539a2.75 2.75 0 0 0 2.75-2.75V7.967a2.75 2.75 0 0 0-2.75-2.75zm-1.25 2.75c0-.69.56-1.25 1.25-1.25h7.539c.69 0 1.25.56 1.25 1.25v7.538c0 .69-.56 1.25-1.25 1.25H7.072c-.69 0-1.25-.56-1.25-1.25z","clip-rule":"evenodd"},null,-1)]))}const ZP={render:qP},JP={xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"none"};function QP(e,t){return ye(),Ae("svg",JP,t[0]||(t[0]=[ae("path",{stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M17 3h-5.555M17 3l-8.333 8.333M17 3v5.556"},null,-1),ae("path",{stroke:"currentColor","stroke-linecap":"round","stroke-width":"1.5",d:"M17 13.111v2.333A1.556 1.556 0 0 1 15.444 17H4.556A1.556 1.556 0 0 1 3 15.444V4.556A1.556 1.556 0 0 1 4.556 3h2.333"},null,-1)]))}const e3={render:QP},wf={SlideNext:"SLIDE_NEXT",SlidePrev:"SLIDE_PREV"},t3={key:1,class:"keynote-container"},n3={key:0,class:"export-progress-container"},r3={class:"export-progress-content"},o3={class:"export-progress-bar-wrapper"},i3={class:"export-progress-text"},s3={class:"export-progress-estimate"},l3={key:1,class:"loading-section"},a3={class:"dark:text-white"},c3={key:2,class:"error-section"},u3={key:3,class:"keynote-layout"},d3={key:0,class:"keynote-header"},f3={class:"file-info"},h3={class:"file-name"},p3={key:0,class:"page-count"},g3={class:"action-buttons"},v3={class:"button-play-slides-content"},m3={class:"play-slides-text"},b3={class:"button-play-slides-content emphasized-button"},y3={class:"play-slides-text"},x3={class:"button-play-slides"},w3={class:"button-play-slides-content"},_3={key:0,class:"play-slides-text"},C3=["disabled","title"],S3={class:"button-play-slides-content"},E3={key:2,class:"play-slides-text"},T3={class:"button-menu-icon"},$3={class:"keynote-main"},P3={key:0,class:"no-slides"},O3={key:1,class:"content-raw"},k3={class:"slide-navigation-container"},A3={class:""},I3={class:"slide-header"},R3={key:1,class:"content-raw"},L3={class:"genspark-modal-content"},M3={class:"genspark-title"},F3=["href"],N3={class:"genspark-info"},D3={class:"genspark-buttons"},z3={__name:"App",setup(e){const{t}=Ls(),{message:n}=sP(["message"]),r=D(""),o=D(""),i=D(!1),s=D(!1),l=D(!0),a=D(null),c=D(null),u=D({}),d=D([]),f=D(""),m=D(""),p=D("slide"),g=D(1280),x=D(720),h=D(0),y=D(!1),P=D([]),b=D(!1);b.value=Gt.isDarkMode();const C=D(!1),S=D(!1),v=D(null),E=D(""),A=D(!1),F=D(""),B=D(0);E.value=window.location.href;const R=D(0),j=D(null),Q=D(null);ze("jsBridge",Q);const H=()=>{R.value=window.innerWidth},te=()=>{A.value=!!document.fullscreenElement},W=L=>{A.value&&(L.data.type===wf.SlideNext?j.value.next():L.data.type===wf.SlidePrev&&j.value.prev())};function re(L){if(Gt.isGensparkAppIos()){if(window.WebViewJavascriptBridge)return L(window.WebViewJavascriptBridge);if(window.WVJBCallbacks)return window.WVJBCallbacks.push(L);window.WVJBCallbacks=[L]}if(Gt.isGensparkAppAndroid()){if(!window.AndroidBridge)return;L({callHandler:(ce,Re,Oe)=>{if(!window.AndroidBridge[ce])throw new Error(`Method ${ce} is not defined`);const Ze=`callback_${Math.random().toString(36).substring(2,15)}`;window.AndroidBridge[Ze]=yt=>{Oe&&Oe(JSON.parse(yt))},window.AndroidBridge[ce](JSON.stringify(Re),Ze)}})}}mt(()=>{H(),window.addEventListener("resize",H),document.addEventListener("fullscreenchange",te),document.addEventListener("webkitfullscreenchange",te),document.addEventListener("keydown",pe),document.addEventListener("wheel",ue),window.addEventListener("message",W),F.value=window.innerWidth,Gt.isGensparkApp()&&re(L=>{Q.value=L})}),pt(()=>{window.removeEventListener("resize",H),document.removeEventListener("fullscreenchange",te),document.removeEventListener("webkitfullscreenchange",te),document.removeEventListener("keydown",pe),document.removeEventListener("wheel",ue),window.removeEventListener("message",W)});const ue=L=>{L.deltaY>20?j.value.next():L.deltaY<-20&&j.value.prev()},pe=L=>{L.key==="ArrowRight"?j.value.next():L.key==="ArrowLeft"||L.key==="ArrowUp"?j.value.prev():L.key==="ArrowDown"&&j.value.next()},we=V(()=>{if(!g.value||!R.value)return 1;const L=R.value-40;return Math.min(1,L/g.value)}),Te=V(()=>we.value===1||!g.value?g.value:R.value-40),Ue=D({}),st=L=>{const ee=Ue.value[L]||x.value;return Math.ceil(ee*we.value)},gt=V(()=>we.value===1?{}:{transform:`scale(${we.value})`,transformOrigin:"top left",width:`${g.value}px`,height:"auto"}),be=D(x.value),me=L=>!L||typeof L!="string"?!1:L.trim().startsWith("<")&&(L.includes("</html>")||L.includes("</body>")||L.includes("</div>")),St=()=>{S.value=!0,tt()},$e=()=>{Ot()},tt=()=>{v.value&&(clearTimeout(v.value),v.value=null)},Ot=()=>{v.value=setTimeout(()=>{S.value=!1},1e3)},$=()=>{window.open("https://www.genspark.ai/pricing","_blank")},k=()=>{window.open("https://www.genspark.ai","_blank")},z=()=>{B.value>0&&B.value--},J=()=>{B.value<d.value.length-1&&B.value++};mt(()=>{var ee,ce,Re,Oe;const L=new URLSearchParams(window.location.search);r.value=((ee=window.__params)==null?void 0:ee.project_id)||L.get("project_id"),o.value=((ce=window.__params)==null?void 0:ce.slide_id)||L.get("slide_id"),i.value=((Re=window.__params)==null?void 0:Re.show_controls)==="true"||L.get("show_controls")==="true",s.value=((Oe=window.__params)==null?void 0:Oe.is_private)||L.get("is_private")==="true",r.value?_e():(l.value=!1,a.value=t("components.slides.missing-parameters"))});const K=(L,ee)=>{if(typeof L!="number"||isNaN(L)){console.warn("Invalid height received:",L);return}for(Ue.value[ee]=L,P.value.length===0&&d.value.length>0&&(P.value=Array(d.value.length).fill(x.value));P.value.length<=ee;)P.value.push(x.value);P.value[ee]=L,be.value=L},q=D(null),w=[{label:t("components.slides.export_format_pdf"),key:"pdf",icon:()=>T(pP,{class:"slide-icon"})},{label:t("components.slides.export_format_pptx"),key:"pptx",icon:()=>T(mP,{class:"slide-icon"})}],_=D(0),O=D(null);D(!1);const M=D(""),ne=D("1-10"),Y=()=>{if(_.value===100)return;const L=99-_.value,ee=M.value==="pdf"?.05:.01;_.value+=L*ee},I=()=>{_.value=0,O.value&&clearInterval(O.value),M.value==="pptx"?ne.value="1-10":M.value==="pdf"&&(ne.value="1"),O.value=setInterval(Y,3e3)},N=()=>{O.value&&(clearInterval(O.value),O.value=null),_.value=0};Ye(y,L=>{L?I():N()}),pt(()=>{N()});const ie=L=>{M.value=L,de(L)},de=async(L="pdf")=>{if(!(y.value||d.value.length===0)){y.value=!0,n.info(t(`components.webpagerender.generating-${L}`));try{P.value.length<d.value.length&&(P.value=d.value.map((Re,Oe)=>P.value[Oe]||x.value));const ee=o.value||r.value;if(!ee){console.error(t(`components.slides.${L}-export-error`)),n.error(t(`components.webpagerender.${L}-generation-error`));return}await q.value.exportSlidesToPDF(r.value,ee,p.value||ee,g.value,P.value,L)?(_.value=100,n.success(t(`components.webpagerender.${L}-generated`))):(console.error(t(`components.slides.${L}-export-failed`)),n.error(t(`components.webpagerender.${L}-generation-error`)))}catch(ee){console.error(t(`components.slides.${L}-export-error`),ee),n.error(t(`components.webpagerender.${L}-generation-error`))}finally{y.value=!1}}},_e=async()=>{l.value=!0,a.value=null;try{let L=`/api/project/slide_data?project_id=${r.value}`;o.value&&(L+=`&slide_id=${o.value}`);const ce=await(await fetch(L)).json();if(ce.status===0){c.value=ce.data;try{u.value=ce.data.meta_data||{},m.value=u.value.file_name,p.value=u.value.file_prefix,g.value=u.value.width,x.value=u.value.height,h.value=u.value.page_num}catch(Re){console.error(t("components.slides.meta-data-error"),Re),u.value={}}d.value=ce.data.file_contents||[],C.value=ce.data.display_badge||!1,f.value=ce.data.slides_url||""}else a.value=ce.message||t("components.slides.fetch-failed")}catch(L){console.error(t("components.slides.fetch-error"),L),a.value=t("components.slides.fetch-error-message")}finally{l.value=!1}},Xe=()=>{if(A.value)document.exitFullscreen?document.exitFullscreen():document.webkitExitFullscreen?document.webkitExitFullscreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.msExitFullscreen&&document.msExitFullscreen();else{const L=document.documentElement;L.requestFullscreen?L.requestFullscreen():L.webkitRequestFullscreen?L.webkitRequestFullscreen():L.mozRequestFullScreen?L.mozRequestFullScreen():L.msRequestFullscreen&&L.msRequestFullscreen()}A.value=!A.value},He=()=>{window.open("https://mainfunc.ai/blog/genspark_ai_slides_edit","_blank")};D(null);const nt=D(!1),vt=async()=>{try{if(navigator.clipboard)await navigator.clipboard.writeText(f.value);else{const L=document.createElement("textarea");L.value=f.value,L.style.position="fixed",document.body.appendChild(L),L.select(),document.execCommand("copy"),document.body.removeChild(L)}nt.value=!0,setTimeout(()=>{nt.value=!1},2e3),n.success(t("components.slides.copied")||"Copied!")}catch(L){console.error("Failed to copy text: ",L),n.error(t("components.slides.copy_failed")||"Failed to copy")}},Nt=[{label:"Copy Site Link",key:"copy",icon:()=>T(ZP,{class:"slide-icon"})},{label:"Open Site in New Tab",key:"new-window",icon:()=>T(e3,{class:"slide-icon"})}],Et=L=>{if(L==="copy")vt();else if(L==="new-window"){let ee=f.value;ee&&ee.includes("genspark.space")&&(ee=ee.replace("genspark.space","gensparkspace.com")),window.open(ee,"_blank")}},bt=()=>{window.open("https://www.genspark.ai/agents?type=slides_agent","_blank")};return(L,ee)=>{var ce,Re;return A.value?(ye(),sr(Ce(NT),{key:0,ref_key:"carouselRef",ref:j,loop:!1,keyboard:"","show-dots":!1},{default:ro(()=>[(ye(!0),Ae(We,null,Lc(d.value,(Oe,Ze)=>(ye(),Ae("div",{key:Ze,class:"iframe-screen-container"},[Ge(BP,{htmlSrc:Oe.cdn_url,isFinished:!0,width:g.value,minHeight:x.value,onHeightUpdated:yt=>K(yt,Ze)},null,8,["htmlSrc","width","minHeight","onHeightUpdated"])]))),128))]),_:1},512)):(ye(),Ae("div",t3,[y.value?(ye(),Ae("div",n3,[ae("div",r3,[ae("div",o3,[ae("div",{class:"export-progress-bar",style:Tt({width:`${_.value}%`})},null,4)]),ae("div",i3,[ae("div",null,Ve(Ce(t)(`components.slides.exporting_${M.value}`)),1),ae("div",s3,Ve(Ce(t)(`components.slides.${M.value}_export_estimate`,{minutes:ne.value})),1)])])])):Kt("",!0),l.value?(ye(),Ae("div",l3,[ee[4]||(ee[4]=ae("div",{class:"loading-spinner"},null,-1)),ae("p",a3,Ve(Ce(t)("components.slides.loading-data")),1)])):a.value?(ye(),Ae("div",c3,[ae("h2",null,Ve(Ce(t)("components.slides.error")),1),ae("p",null,Ve(a.value),1),ae("button",{onClick:_e},Ve(Ce(t)("components.slides.retry")),1)])):(ye(),Ae("div",u3,[A.value?Kt("",!0):(ye(),Ae("div",d3,[ae("div",f3,[Ge(Ce(yf),{class:"slide-icon"}),ae("h1",h3,Ve(m.value),1),Ce(Gt).isMobile()?Kt("",!0):(ye(),Ae("div",p3,Ve(d.value.length>1?Ce(t)("components.slides.total-pages",{count:d.value.length}):Ce(t)("components.slides.total-page",{count:d.value.length})),1))]),ae("div",g3,[Ce(Gt).isMobile()?Kt("",!0):(ye(),Ae("div",{key:0,class:"button-play-slides",onClick:Xe},[ae("div",v3,[Ge(Ce(FP),{class:"slide-icon"}),ae("div",m3,Ve(Ce(t)("components.slides.play_slides")),1)])])),!Ce(Gt).isMobile()&&!i.value?(ye(),Ae("div",{key:1,class:"button-play-slides",onClick:bt},[ae("div",b3,[Ge(Ce(yf),{class:"slide-icon"}),ae("div",y3,Ve(Ce(t)("components.slides.create_ai_slides")||"Create AI Slides"),1)])])):Kt("",!0),f.value&&i.value&&s.value==="false"?(ye(),sr(Ce(mf),{key:2,options:Nt,onSelect:Et},{default:ro(()=>[ae("div",x3,[ae("div",w3,[Ge(Ce(YP),{class:"slide-icon"}),Ce(Gt).isMobile()?Kt("",!0):(ye(),Ae("div",_3,Ve(Ce(t)("components.slides.publish")||"Publish"),1))])])]),_:1})):Kt("",!0),Ge(Ce(mf),{options:w,onSelect:ie,disabled:y.value},{default:ro(()=>[ae("div",{class:"button-play-slides",disabled:y.value,title:y.value?Ce(t)("components.slides.exporting"):Ce(t)("components.slides.export-pdf")},[ae("div",S3,[y.value?(ye(),sr(Ce(RP),{key:0,class:"black-loading-animation"})):(ye(),sr(Ce(xP),{key:1,class:"slide-icon"})),Ce(Gt).isMobile()?Kt("",!0):(ye(),Ae("div",E3,Ve(Ce(t)("components.slides.export_pdf")),1)),ee[5]||(ee[5]=ae("span",{class:"beta-tag"},"beta",-1))])],8,C3)]),_:1},8,["disabled"]),!Ce(Gt).isMobile()&&i.value?(ye(),sr(Ce(Yg),{key:3,class:"rounded-lg",trigger:"hover",raw:"","show-arrow":!1},{trigger:ro(()=>[ae("div",T3,[Ge(Ce(VP),{class:"slide-icon cursor-pointer outline-none"})])]),default:ro(()=>[ae("div",{class:"button-menu-content",onClick:He},Ve(Ce(t)("components.slides.edit_in_canva_and_figma")),1)]),_:1})):Kt("",!0)])])),ae("div",$3,[ae("div",{class:Wn(["slides-container",Ce(Gt).isMobile()?"slides-mobile-container":""]),style:Tt({width:`${Te.value}px`})},[d.value.length===0?(ye(),Ae("div",P3,[ae("p",null,Ve(Ce(t)("components.slides.no-slides")),1)])):Kt("",!0),Ce(Gt).isMobile()?(ye(),Ae("div",{key:1,class:"slide-content",style:Tt({width:`${Te.value}px`,maxWidth:"100%"})},[me(d.value[B.value].content)?(ye(),Ae("div",{key:0,class:"iframe-container",style:Tt(we.value!==1?{height:st(B.value)+"px"}:{})},[Ge(bf,{htmlSrc:d.value[B.value].cdn_url,isFinished:!0,width:g.value,minHeight:x.value,style:Tt(gt.value),onHeightUpdated:ee[0]||(ee[0]=Oe=>K(Oe,B.value))},null,8,["htmlSrc","width","minHeight","style"])],4)):(ye(),Ae("pre",O3,Ve(d.value[B.value].content),1)),ae("div",k3,[Ge(Ce(xf),{onClick:z,class:Wn(["left-arrow-icon",B.value===0?"opacity-50":""])},null,8,["class"]),ae("div",A3,Ve(B.value+1)+"/"+Ve((ce=d.value)==null?void 0:ce.length),1),Ge(Ce(xf),{onClick:J,class:Wn(["w-[20px] h-[20px]",B.value===((Re=d.value)==null?void 0:Re.length)-1?"opacity-50":""])},null,8,["class"])])],4)):(ye(!0),Ae(We,{key:2},Lc(d.value,(Oe,Ze)=>(ye(),Ae("div",{key:Ze,class:"slide-content",style:Tt({width:`${Te.value}px`,maxWidth:"100%"})},[ae("div",I3,Ve(Ze+1)+" / "+Ve(d.value.length),1),me(Oe.content)?(ye(),Ae("div",{key:0,class:"iframe-container relative",style:Tt(we.value!==1?{height:st(Ze)+"px"}:{})},[Ge(bf,{htmlSrc:Oe.cdn_url,isFinished:!0,width:g.value,minHeight:x.value,style:Tt(gt.value),onHeightUpdated:yt=>K(yt,Ze)},null,8,["htmlSrc","width","minHeight","style","onHeightUpdated"])],4)):(ye(),Ae("pre",R3,Ve(Oe.content),1))],4))),128))],6)]),Ge(kP,{ref_key:"pdfExportRef",ref:q,onExportComplete:ee[1]||(ee[1]=Oe=>y.value=!1)},null,512),C.value&&!i.value?(ye(),Ae("button",{key:1,class:"genspark-badge-button",onClick:ee[2]||(ee[2]=Oe=>S.value=!0),onMouseenter:St,onMouseleave:$e},[ee[6]||(ee[6]=Rh('<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none" data-v-c9afc313><path d="M11.3412 0H2.65879C1.19038 0 0 1.19038 0 2.65879V11.3412C0 12.8096 1.19038 14 2.65879 14H11.3412C12.8096 14 14 12.8096 14 11.3412V2.65879C14 1.19038 12.8096 0 11.3412 0Z" fill="white" data-v-c9afc313></path><path d="M11.7403 10.7031H2.29243C2.09641 10.7031 1.9375 10.862 1.9375 11.0581V11.8033C1.9375 11.9993 2.09641 12.1582 2.29243 12.1582H11.7403C11.9363 12.1582 12.0952 11.9993 12.0952 11.8033V11.0581C12.0952 10.862 11.9363 10.7031 11.7403 10.7031Z" fill="#232425" data-v-c9afc313></path><path d="M5.09178 9.18166C5.03494 9.18166 4.98695 9.13998 4.97811 9.08314C4.60803 6.63655 4.34025 6.42056 1.91134 6.05427C1.83682 6.0429 1.78125 5.97848 1.78125 5.9027C1.78125 5.82691 1.83682 5.7625 1.91134 5.75113C4.32762 5.3861 4.54235 5.17011 4.90738 2.7551C4.91874 2.68058 4.98316 2.625 5.05894 2.625C5.13473 2.625 5.19914 2.68058 5.21051 2.7551C5.57554 5.17011 5.79153 5.3861 8.20655 5.75113C8.28107 5.7625 8.33664 5.82691 8.33664 5.9027C8.33664 5.97848 8.28107 6.0429 8.20655 6.05427C5.78017 6.42056 5.57302 6.63655 5.20546 9.08314C5.19662 9.13871 5.14862 9.18166 5.09178 9.18166Z" fill="#232425" data-v-c9afc313></path><path d="M9.70174 5.949C9.66637 5.949 9.63606 5.92248 9.63101 5.88711C9.39986 4.35878 9.23188 4.22363 7.71492 3.99501C7.66818 3.98743 7.63281 3.94828 7.63281 3.90028C7.63281 3.85355 7.66692 3.81313 7.71492 3.80555C9.2243 3.5782 9.35945 3.44305 9.5868 1.93366C9.59438 1.88693 9.63354 1.85156 9.68153 1.85156C9.72827 1.85156 9.76869 1.88567 9.77627 1.93366C10.0036 3.44305 10.1388 3.5782 11.6482 3.80555C11.6949 3.81313 11.7302 3.85228 11.7302 3.90028C11.7302 3.94702 11.6962 3.98743 11.6482 3.99501C10.1325 4.22363 10.0024 4.35878 9.77247 5.88711C9.76742 5.92248 9.73711 5.949 9.70174 5.949Z" fill="#232425" data-v-c9afc313></path><path d="M9.69114 9.76325C9.6684 9.76325 9.64946 9.74683 9.64567 9.7241C9.49915 8.75152 9.39179 8.66563 8.42679 8.52038C8.39648 8.51533 8.375 8.49007 8.375 8.45975C8.375 8.42944 8.39648 8.40418 8.42679 8.39912C9.38673 8.25387 9.47262 8.16798 9.61788 7.20804C9.62293 7.17772 9.64819 7.15625 9.6785 7.15625C9.70882 7.15625 9.73408 7.17772 9.73913 7.20804C9.88439 8.16798 9.97028 8.25387 10.9302 8.39912C10.9605 8.40418 10.982 8.42944 10.982 8.45975C10.982 8.49007 10.9605 8.51533 10.9302 8.52038C9.96523 8.66563 9.88312 8.75152 9.73661 9.7241C9.73282 9.74683 9.71387 9.76325 9.69114 9.76325Z" fill="#232425" data-v-c9afc313></path></svg>',1)),vo(" "+Ve(Ce(t)("components.genspark_badge.button_text")),1)],32)):Kt("",!0),C.value?(ye(),Ae("div",{key:2,class:"genspark-modal",style:Tt({display:S.value?"flex":"none"}),onMouseenter:tt,onMouseleave:Ot},[ae("div",L3,[ae("button",{class:"genspark-close",onClick:ee[3]||(ee[3]=Oe=>S.value=!1)},ee[7]||(ee[7]=[ae("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 14 14",fill:"none"},[ae("path",{d:"M11 3L3 11M3 3L11 11",stroke:"#232425","stroke-linecap":"round","stroke-linejoin":"round"})],-1)])),ae("h3",M3,Ve(Ce(t)("components.genspark_badge.title_text")),1),ae("a",{class:"genspark-report",href:`mailto:<EMAIL>?subject=Report%20inappropriate%20content&body=Current%20URL:%20${encodeURIComponent(E.value)}`},Ve(Ce(t)("components.genspark_badge.report_text")),9,F3),ae("p",N3,Ve(Ce(t)("components.genspark_badge.info_text")),1),ae("div",D3,[ae("button",{class:"genspark-remove-btn",onClick:$},Ve(Ce(t)("components.genspark_badge.remove_button_text")),1),ae("button",{class:"genspark-go-btn",onClick:k},Ve(Ce(t)("components.genspark_badge.go_button_text")),1)])])],36)):Kt("",!0)]))]))}}},B3=qs(z3,[["__scopeId","data-v-c9afc313"]]),H3=I1({locale:"en",messages:{en:{"components.slides.total-page":"{count} page total","components.slides.total-pages":"{count} pages total","components.slides.play_slides":"Play Slides","components.slides.export_pdf":"Export","components.slides.export_pptx":"Export to PPTX","components.slides.export_format_pdf":"PDF","components.slides.export_format_pptx":"PPTX","components.slides.loading-data":"Loading data...","components.slides.no-slides":"No slides found","components.slides.no-slides-found":"No slides found","components.slides.edit_in_canva_and_figma":"Edit in Canva and Figma","components.pdfexport.exporting":"Exporting...","components.slides.exporting":"Exporting...","components.slides.exporting_pptx":"Exporting PPTX...","components.slides.exporting_pdf":"Exporting PDF...","components.slides.pptx_export_estimate":"Estimated time: {minutes} minutes.","components.slides.pdf_export_estimate":"Estimated time: {minutes} minutes.","components.slides.export_do_not_close":"Please do not close the page.","components.genspark_badge.button_text":"Made with Genspark","components.genspark_badge.title_text":"This page was created by users with AI.","components.genspark_badge.report_text":"Report inappropriate content.","components.genspark_badge.info_text":"Page owner with Plus Plan can remove badge.","components.genspark_badge.remove_button_text":"Remove Badge","components.genspark_badge.go_button_text":"Go to Genspark","components.slides.publish":"Publish","components.slides.create_ai_slides":"Try AI Slides","components.slides.error":"Error","components.slides.retry":"Retry","components.slides.missing-parameters":"Missing required parameters","components.slides.meta-data-error":"Error parsing meta data","components.slides.fetch-failed":"Failed to fetch slide data","components.slides.fetch-error":"Error fetching data","components.slides.fetch-error-message":"An error occurred while fetching the data. Please try again later.","components.slides.pdf-export-error":"Error exporting PDF","components.slides.pdf-export-failed":"Failed to export PDF","components.slides.pptx-export-error":"Error exporting PPTX","components.slides.pptx-export-failed":"Failed to export PPTX","components.webpagerender.generating-pdf":"Generating PDF...","components.webpagerender.pdf-generation-error":"Error generating PDF","components.webpagerender.pdf-generated":"PDF generated successfully","components.webpagerender.generating-pptx":"Generating PPTX...","components.webpagerender.pptx-generation-error":"Error generating PPTX","components.webpagerender.pptx-generated":"PPTX generated successfully","components.slides.copied":"Copied!","components.slides.copy_failed":"Failed to copy"}}}),vv=Uh(B3);vv.use(H3);vv.mount("#app")});export default j3();</script>
    <style rel="stylesheet" crossorigin>.html-content-viewer[data-v-acf7cf76]{width:100%}.pdf-exporting-overlay[data-v-90e5e7a5]{position:fixed;top:0;left:0;right:0;bottom:0;background-color:#00000080;display:flex;flex-direction:column;align-items:center;justify-content:center;z-index:9999;color:#fff}.pdf-exporting-spinner[data-v-90e5e7a5]{border:4px solid rgba(255,255,255,.3);border-radius:50%;border-top:4px solid white;width:40px;height:40px;animation:spin-90e5e7a5 1s linear infinite;margin-bottom:15px}.progress-bar-container[data-v-90e5e7a5]{width:300px;height:8px;background-color:#ffffff4d;border-radius:4px;margin-top:15px;overflow:hidden}.progress-bar[data-v-90e5e7a5]{height:100%;background-color:#fff;border-radius:4px;transition:width .3s ease}@keyframes spin-90e5e7a5{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@media (prefers-color-scheme: dark){.pdf-exporting-overlay[data-v-90e5e7a5]{background-color:#000000b3}}.html-content-viewer[data-v-c78466df]{display:flex;justify-content:center;align-items:center;width:100%}.iframe[data-v-c78466df]{background-color:#fff}.keynote-container[data-v-c9afc313]{width:100%;min-height:100vh;color:#1d1d1f;overflow-x:hidden}.loading-section[data-v-c9afc313],.error-section[data-v-c9afc313]{display:flex;flex-direction:column;align-items:center;justify-content:center;min-height:300px;padding:40px}.loading-spinner[data-v-c9afc313]{border:4px solid rgba(0,0,0,.1);border-radius:50%;border-top:4px solid #0071e3;width:40px;height:40px;animation:spin-c9afc313 1s linear infinite;margin-bottom:20px}@keyframes spin-c9afc313{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.keynote-layout[data-v-c9afc313]{display:flex;height:100vh;overflow:hidden;flex-direction:column}.info-item[data-v-c9afc313]{display:flex;justify-content:space-between;margin-bottom:10px;font-size:14px}.info-label[data-v-c9afc313]{color:#6e6e73}.info-value[data-v-c9afc313]{font-weight:500}.keynote-main[data-v-c9afc313]{flex:1;display:flex;flex-direction:column;background-color:#fff;overflow-y:auto;overflow-x:hidden;align-items:center;justify-content:flex-start}.slides-container[data-v-c9afc313]{display:flex;flex-direction:column;gap:28px;align-items:center;width:100%;padding:24px 0 60px}.slide-content[data-v-c9afc313]{margin:0 auto}.iframe-container[data-v-c9afc313]{position:relative;width:100%;overflow:hidden;box-shadow:0 2px 6px #0000001a;border:1px solid #e0e0e0;border-radius:12px;transform-origin:top left;transition:height .3s ease}.iframe-container>iframe[data-v-c9afc313]{display:block;transition:transform .3s ease}.no-slides[data-v-c9afc313]{display:flex;justify-content:center;align-items:center;height:400px;width:100%;background-color:#f5f5f7;border-radius:8px;color:#6e6e73}.content-raw[data-v-c9afc313]{padding:20px;white-space:pre-wrap;font-family:monospace;overflow-x:auto;margin:0}.external-link[data-v-c9afc313]{position:absolute;bottom:20px;right:20px}.external-link a[data-v-c9afc313]{display:inline-block;padding:8px 16px;background-color:#0071e3e6;color:#fff;border-radius:20px;text-decoration:none;font-size:14px;font-weight:500;transition:background-color .2s ease}.black-loading-animation[data-v-c9afc313]{animation:rotate-c9afc313 1.5s linear infinite}@keyframes rotate-c9afc313{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.external-link a[data-v-c9afc313]:hover{background-color:#0071e3}.slide-header[data-v-c9afc313]{color:#606366;width:100%;padding:0 12px;text-align:left;font-size:14px;margin-bottom:8px}@media (max-width: 1024px){.keynote-main[data-v-c9afc313]{padding:20px}}.keynote-header[data-v-c9afc313]{display:flex;justify-content:space-between;align-items:center;padding:16px 24px;background-color:#f5f5f7;border-bottom:1px solid #e5e5e7;gap:12px}.file-info[data-v-c9afc313]{display:flex;align-items:center;gap:12px;flex:1;overflow:hidden}.slide-icon[data-v-c9afc313]{height:24px;color:#333;flex-shrink:0}.file-name[data-v-c9afc313]{font-size:18px;font-weight:500;margin:0;color:#1d1d1f;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;min-width:0}.page-count[data-v-c9afc313]{font-size:14px;color:#6e6e73;font-weight:400;flex-shrink:0}.action-buttons[data-v-c9afc313]{display:flex;align-items:center;flex-shrink:0;gap:12px}.pdf-button[data-v-c9afc313]{display:flex;align-items:center;justify-content:center;background:none;border:none;padding:8px;cursor:pointer;position:relative;border-radius:6px;transition:all .2s ease;color:#333;min-width:36px;min-height:36px}.pdf-button[data-v-c9afc313]:hover{transform:translateY(-1px)}.pdf-button[data-v-c9afc313]:active{transform:translateY(0)}.pdf-button[data-v-c9afc313]:disabled{opacity:.6;cursor:not-allowed;transform:none}.pdf-button .slide-icon[data-v-c9afc313]{width:20px;height:20px}.pdf-loading[data-v-c9afc313]{position:absolute;top:4px;right:4px;width:10px;height:10px;border:2px solid rgba(0,0,0,.1);border-radius:50%;border-top-color:#333;animation:spin-c9afc313 1s linear infinite}.genspark-badge-button[data-v-c9afc313]{position:fixed;bottom:20px;right:20px;background-color:#333;color:#fff;border:none;border-radius:4px;padding:8px 12px;font-size:12px;cursor:pointer;z-index:9999;display:flex;align-items:center;gap:6px}.genspark-modal[data-v-c9afc313]{position:fixed;bottom:80px;right:20px;z-index:10000;justify-content:end}.genspark-modal-content[data-v-c9afc313]{background-color:#fff;border-radius:8px;max-width:450px;width:100%;box-sizing:border-box;padding:20px;position:relative;box-shadow:0 4px 8px #0000001a;font-size:14px}@media (max-width: 768px){.genspark-modal-content[data-v-c9afc313]{max-width:90%}}.genspark-close[data-v-c9afc313]{position:absolute;top:10px;right:10px;font-size:20px;cursor:pointer;background:none;border:none}.genspark-title[data-v-c9afc313]{margin-bottom:8px;font-weight:400;display:inline;font-size:14px}.genspark-report[data-v-c9afc313]{color:#909499;text-decoration:underline;cursor:pointer;margin-bottom:14px;display:inline}.genspark-info[data-v-c9afc313]{margin:25px 0;color:#333;font-size:14px}.genspark-buttons[data-v-c9afc313]{display:flex;gap:10px}.genspark-remove-btn[data-v-c9afc313]{background-color:#f5f5f5;border:1px solid #ddd;color:#333;padding:4px 14px;border-radius:8px;cursor:pointer;flex:1;font-size:14px;box-sizing:border-box}.genspark-go-btn[data-v-c9afc313]{background-color:#222;border:none;color:#fff;padding:4px 14px;border-radius:8px;cursor:pointer;flex:1;font-size:14px;box-sizing:border-box}.button-play-slides[data-v-c9afc313]{display:flex;align-items:center;justify-content:center;border-radius:6px;transition:all .2s ease-in-out}.button-play-slides[data-v-c9afc313]:hover{transform:translateY(-1px)}.button-play-slides-content[data-v-c9afc313]{padding:.375rem .75rem;background-color:#fff;border-radius:.5rem;display:inline-flex;justify-content:flex-start;align-items:center;gap:.625rem;cursor:pointer;transition:background-color .2s ease}.button-play-slides-content[data-v-c9afc313]:hover{background-color:#0000000d}.button-menu-icon[data-v-c9afc313]{padding:.375rem;background-color:#fff;border-radius:.5rem;display:flex;justify-content:flex-start;align-items:center;cursor:pointer}.button-menu-content[data-v-c9afc313]{background-color:#fff;border-radius:.5rem;box-shadow:0 4px 15px #0000001a;display:flex;justify-content:center;align-items:center;padding:16px 24px;color:#262626;font-size:.875rem;line-height:1.25rem;cursor:pointer}.iframe-screen-container[data-v-c9afc313]{display:flex;justify-content:center;align-items:center;width:100vw;height:100vh;background-color:#000}.play-slides-text[data-v-c9afc313]{display:flex;justify-content:flex-start;color:#262626;font-size:.75rem;font-weight:400;font-family:Arial,sans-serif;line-height:1}.slide-navigation-container[data-v-c9afc313]{position:fixed;bottom:60px;left:50%;transform:translate(-50%);display:flex;align-items:center;justify-content:space-between;gap:10px;width:168px;margin:30px auto;padding:12px 24px;background-color:#000000e6;border-radius:36px;color:#fff;transition:background-color .3s ease}.slides-mobile-container[data-v-c9afc313]{height:calc(80vh - 200px);display:flex;justify-content:center;align-items:center}.left-arrow-icon[data-v-c9afc313]{transform:rotate(180deg)}.opacity-50[data-v-c9afc313]{opacity:.5}@media (prefers-color-scheme: dark){.keynote-main[data-v-c9afc313]{background-color:#232425}.keynote-header[data-v-c9afc313]{background-color:#323335;border-bottom:1px solid #3a3a3c}.file-name[data-v-c9afc313]{color:#fff}.page-count[data-v-c9afc313]{color:#a1a1a6}.pdf-loading[data-v-c9afc313]{border:2px solid rgba(255,255,255,.3);border-top-color:#fff}.iframe-container[data-v-c9afc313]{border-color:#3a3a3c}.genspark-modal-content[data-v-c9afc313]{background-color:#323335;color:#fff}.genspark-title[data-v-c9afc313],.genspark-info[data-v-c9afc313]{color:#fff}.genspark-remove-btn[data-v-c9afc313]{background-color:#444;border:1px solid #555;color:#fff}.genspark-go-btn[data-v-c9afc313]{background-color:#666;color:#fff}.file-info .slide-icon[data-v-c9afc313]{color:#fff}.button-play-slides-content[data-v-c9afc313]{background-color:#3f3f3f;color:#fff;border:1px solid #4a4a4a}.button-play-slides-content .play-slides-text[data-v-c9afc313]{color:#fff}.button-menu-icon[data-v-c9afc313]{background-color:#3f3f3f;border:1px solid #4a4a4a}.button-menu-content[data-v-c9afc313]{background-color:#323335;color:#fff;box-shadow:0 4px 15px #0000004d}.action-buttons .slide-icon[data-v-c9afc313]{color:#fff}.button-play-slides-content[data-v-c9afc313]:hover{background-color:#4a4a4a}.slide-navigation-container[data-v-c9afc313]{background-color:#0000004d}.beta-tag[data-v-c9afc313]{background-color:#ff9800;color:#232425}.share-content[data-v-c9afc313]{background-color:#323335;color:#fff}.share-url-input[data-v-c9afc313]{background-color:#232425;color:#fff;border-color:#4a4a4a}.share-copy-button[data-v-c9afc313]{background-color:#0071e3}}.beta-tag[data-v-c9afc313]{font-size:.6rem;background-color:#ff9800;color:#fff;padding:1px 4px;border-radius:4px;font-weight:700;position:relative;top:-1px}.export-progress-container[data-v-c9afc313]{position:fixed;top:100px;left:0;right:0;z-index:10000;background-color:#f5f5f7;padding:8px 16px;font-size:14px;margin:0 auto;border-radius:8px;border:1px solid #e5e5e7;max-width:600px}@media (max-width: 768px){.export-progress-container[data-v-c9afc313]{margin:0 20px}}.export-progress-content[data-v-c9afc313]{display:flex;align-items:center;max-width:900px;margin:0 auto}.export-progress-bar-wrapper[data-v-c9afc313]{flex:1;height:6px;background-color:#e0e0e0;border-radius:3px;overflow:hidden;margin-right:16px}.export-progress-bar[data-v-c9afc313]{height:100%;background-color:#0071e3;border-radius:3px;transition:width .3s ease}.export-progress-text[data-v-c9afc313]{margin-right:16px;flex-shrink:0;font-weight:500}.export-progress-estimate[data-v-c9afc313]{font-size:12px;color:#6e6e73;margin-top:2px}.export-progress-info[data-v-c9afc313]{width:20px;height:20px;cursor:pointer}.info-icon[data-v-c9afc313]{width:20px;height:20px;color:#6e6e73}.export-info-tooltip[data-v-c9afc313]{position:absolute;top:100%;right:16px;width:300px;background-color:#fff;border-radius:8px;box-shadow:0 4px 12px #00000026;padding:12px 16px;font-size:13px;line-height:1.4;color:#1d1d1f;margin-top:8px;z-index:10001}.share-content[data-v-c9afc313]{padding:16px;width:320px;max-width:90vw}.share-url-container[data-v-c9afc313]{display:flex;flex-direction:column;gap:10px}.share-url-input[data-v-c9afc313]{width:100%;padding:10px;border:1px solid #e0e0e0;border-radius:6px;font-size:14px;color:#333;background-color:#f5f5f7;cursor:pointer}.share-copy-button[data-v-c9afc313]{padding:10px 16px;background-color:#0071e3;color:#fff;border:none;border-radius:6px;cursor:pointer;font-weight:500;transition:background-color .2s ease}.share-copy-button[data-v-c9afc313]:hover{background-color:#0058b0}@media (prefers-color-scheme: dark){.export-progress-container[data-v-c9afc313]{background-color:#323335;border:1px solid #4a4a4a}.export-progress-bar-wrapper[data-v-c9afc313]{background-color:#3a3a3c}.export-progress-text[data-v-c9afc313]{color:#fff}.export-progress-estimate[data-v-c9afc313],.info-icon[data-v-c9afc313]{color:#a1a1a6}.export-info-tooltip[data-v-c9afc313]{background-color:#323335;color:#fff;box-shadow:0 4px 12px #00000040}}.emphasized-button[data-v-c9afc313]{background-color:#232425;color:#fff}.emphasized-button[data-v-c9afc313]:hover{background-color:#ccc}.emphasized-button .play-slides-text[data-v-c9afc313],.emphasized-button .slide-icon[data-v-c9afc313]{color:#fff}</style>
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
